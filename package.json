{"name": "meepic-backend", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "local": "ENV_PATH=.env.local strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi", "postinstall": "patch-package", "neo-local": "ENV_PATH=.env.local ./node_modules/.bin/strapi develop"}, "dependencies": {"@aws-sdk/client-sns": "^3.435.0", "@strapi-community/strapi-provider-upload-google-cloud-storage": "^5.0.5", "@strapi/plugin-i18n": "4.11.1", "@strapi/plugin-users-permissions": "4.11.1", "@strapi/provider-email-sendgrid": "^4.12.4", "@strapi/strapi": "4.11.1", "apple-sign-in-rest": "^1.0.3", "google-libphonenumber": "^3.2.33", "googleapis": "^128.0.0", "https-proxy-agent": "^7.0.6", "jose": "^4.14.6", "mathjs": "11.12.0", "mysql": "^2.18.1", "openai": "^4.102.0", "otp-generator": "^4.0.1", "patch-package": "^8.0.0", "qrcode": "^1.5.4", "semver": "^7.5.4", "solapi": "^5.2.3", "strapi-advanced-uuid": "1.0.11", "strapi-plugin-config-sync": "^1", "strapi-plugin-country-select": "^1.0.3", "strapi-plugin-init-admin-user": "^0.2.1", "strapi-plugin-multi-select": "^1.2.2", "strapi-plugin-redis": "^1.0.1", "strapi-plugin-responsive-image": "^1.1.0", "strapi-provider-upload-aws-s3": "file:providers/strapi-provider-upload-aws-s3", "xlsx": "^0.18.5"}, "workspaces": ["./providers/strapi-provider-upload-aws-s3", "./src/plugins/strapi-mee-data", "./src/plugins/strapi-mq", "./src/plugins/strapi-notification"], "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "18f6bca0-2ef4-4c7f-977a-c417ece74db3"}, "engines": {"node": ">=14.19.1 <=18.x.x", "npm": ">=6.0.0"}, "license": "MIT", "devDependencies": {"typescript": "^5.8.2"}}