{"key": "plugin_content_manager_configuration_content_types::api::feedback-category.feedback-category", "value": {"uid": "api::feedback-category.feedback-category", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "name", "defaultSortBy": "name", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "name", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "tag": {"edit": {"label": "tag", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "tag", "searchable": true, "sortable": true}}, "order": {"edit": {"label": "order", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "order", "searchable": true, "sortable": true}}, "locales": {"edit": {"label": "locales", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "locales", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "name", "tag", "order"], "edit": [[{"name": "name", "size": 6}, {"name": "tag", "size": 6}], [{"name": "order", "size": 4}], [{"name": "locales", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}