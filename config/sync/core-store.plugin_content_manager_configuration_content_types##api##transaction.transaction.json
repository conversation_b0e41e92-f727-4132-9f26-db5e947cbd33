{"key": "plugin_content_manager_configuration_content_types::api::transaction.transaction", "value": {"uid": "api::transaction.transaction", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "name", "defaultSortBy": "name", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "name", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "targetName": {"edit": {"label": "targetName", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "targetName", "searchable": true, "sortable": true}}, "customer": {"edit": {"label": "customer", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "customer", "searchable": true, "sortable": true}}, "type": {"edit": {"label": "type", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "type", "searchable": true, "sortable": true}}, "amount": {"edit": {"label": "amount", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "amount", "searchable": true, "sortable": true}}, "balance": {"edit": {"label": "balance", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "balance", "searchable": true, "sortable": true}}, "related": {"edit": {"label": "related", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "related", "searchable": true, "sortable": true}}, "ex": {"edit": {"label": "ex", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "ex", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "name", "targetName", "customer"], "edit": [[{"name": "name", "size": 6}, {"name": "targetName", "size": 6}], [{"name": "customer", "size": 6}, {"name": "type", "size": 6}], [{"name": "amount", "size": 4}, {"name": "balance", "size": 4}], [{"name": "related", "size": 6}], [{"name": "ex", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}