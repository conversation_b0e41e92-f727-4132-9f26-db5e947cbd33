{"key": "plugin_content_manager_configuration_content_types::api::university.university", "value": {"uid": "api::university.university", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "name", "defaultSortBy": "name", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "name", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "location": {"edit": {"label": "location", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "location", "searchable": true, "sortable": true}}, "regionCode": {"edit": {"label": "regionCode", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "regionCode", "searchable": true, "sortable": true}}, "major": {"edit": {"label": "major", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "major", "searchable": false, "sortable": false}}, "emailSuffix": {"edit": {"label": "emailSuffix", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "emailSuffix", "searchable": true, "sortable": true}}, "emailRegex": {"edit": {"label": "emailRegex", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "emailRegex", "searchable": true, "sortable": true}}, "logo": {"edit": {"label": "logo", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "logo", "searchable": false, "sortable": false}}, "rank": {"edit": {"label": "rank", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "rank", "searchable": true, "sortable": true}}, "website": {"edit": {"label": "website", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "website", "searchable": true, "sortable": true}}, "users": {"edit": {"label": "users", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "users", "searchable": false, "sortable": false}}, "emailSuffixes": {"edit": {"label": "emailSuffixes", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "emailSuffixes", "searchable": false, "sortable": false}}, "locales": {"edit": {"label": "locales", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "locales", "searchable": false, "sortable": false}}, "source": {"edit": {"label": "source", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "source", "searchable": true, "sortable": true}}, "blocked": {"edit": {"label": "blocked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "blocked", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "name", "location", "regionCode"], "edit": [[{"name": "name", "size": 6}, {"name": "location", "size": 6}], [{"name": "regionCode", "size": 6}], [{"name": "major", "size": 12}], [{"name": "emailSuffix", "size": 6}, {"name": "emailRegex", "size": 6}], [{"name": "logo", "size": 6}, {"name": "rank", "size": 4}], [{"name": "website", "size": 6}, {"name": "users", "size": 6}], [{"name": "emailSuffixes", "size": 12}], [{"name": "locales", "size": 12}], [{"name": "source", "size": 6}, {"name": "blocked", "size": 4}]]}}, "type": "object", "environment": null, "tag": null}