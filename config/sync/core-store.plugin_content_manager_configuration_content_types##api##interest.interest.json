{"key": "plugin_content_manager_configuration_content_types::api::interest.interest", "value": {"uid": "api::interest.interest", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "tag", "defaultSortBy": "tag", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "tag": {"edit": {"label": "tag", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "tag", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "name", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "users": {"edit": {"label": "users", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "users", "searchable": false, "sortable": false}}, "source": {"edit": {"label": "source", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "source", "searchable": true, "sortable": true}}, "locales": {"edit": {"label": "locales", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "locales", "searchable": false, "sortable": false}}, "category": {"edit": {"label": "category", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "category", "searchable": true, "sortable": true}}, "order": {"edit": {"label": "order", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "order", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "tag", "name", "users"], "edit": [[{"name": "tag", "size": 6}, {"name": "name", "size": 6}], [{"name": "users", "size": 6}, {"name": "source", "size": 6}], [{"name": "locales", "size": 12}], [{"name": "category", "size": 6}, {"name": "order", "size": 4}]]}}, "type": "object", "environment": null, "tag": null}