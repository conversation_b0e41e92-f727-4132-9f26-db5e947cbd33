{"key": "plugin_content_manager_configuration_content_types::api::ads-insight.ads-insight", "value": {"uid": "api::ads-insight.ads-insight", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "firstname"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "ads": {"edit": {"label": "ads", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "ads", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "user", "ads", "createdAt"], "edit": [[{"name": "user", "size": 6}, {"name": "ads", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}