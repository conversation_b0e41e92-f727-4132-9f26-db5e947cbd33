{"key": "plugin_content_manager_configuration_content_types::plugin::community.report", "value": {"uid": "plugin::community.report", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "related", "defaultSortBy": "related", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "reporter": {"edit": {"label": "reporter", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "reporter", "searchable": true, "sortable": true}}, "reportType": {"edit": {"label": "reportType", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "reportType", "searchable": true, "sortable": true}}, "related": {"edit": {"label": "related", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "related", "searchable": true, "sortable": true}}, "reasons": {"edit": {"label": "reasons", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "reasons", "searchable": false, "sortable": false}}, "content": {"edit": {"label": "content", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "content", "searchable": true, "sortable": true}}, "attachments": {"edit": {"label": "attachments", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "attachments", "searchable": false, "sortable": false}}, "resolved": {"edit": {"label": "resolved", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "resolved", "searchable": true, "sortable": true}}, "resolvedAt": {"edit": {"label": "resolvedAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "resolvedAt", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "reporter", "reportType", "related"], "edit": [[{"name": "reporter", "size": 6}, {"name": "reportType", "size": 6}], [{"name": "related", "size": 6}], [{"name": "reasons", "size": 12}], [{"name": "content", "size": 6}, {"name": "attachments", "size": 6}], [{"name": "resolved", "size": 4}, {"name": "resolvedAt", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}