{"key": "plugin_content_manager_configuration_content_types::plugin::benefits.board", "value": {"uid": "plugin::benefits.board", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "name", "defaultSortBy": "name", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "name", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "locales": {"edit": {"label": "locales", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "locales", "searchable": false, "sortable": false}}, "description": {"edit": {"label": "description", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "description", "searchable": true, "sortable": true}}, "related": {"edit": {"label": "related", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "related", "searchable": true, "sortable": true}}, "blocked": {"edit": {"label": "blocked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "blocked", "searchable": true, "sortable": true}}, "type": {"edit": {"label": "type", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "type", "searchable": true, "sortable": true}}, "creator": {"edit": {"label": "creator", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "creator", "searchable": true, "sortable": true}}, "postingPermission": {"edit": {"label": "postingPermission", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "postingPermission", "searchable": true, "sortable": true}}, "subscribers": {"edit": {"label": "subscribers", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "subscribers", "searchable": false, "sortable": false}}, "coupons": {"edit": {"label": "coupons", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "coupons", "searchable": false, "sortable": false}}, "order": {"edit": {"label": "order", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "order", "searchable": true, "sortable": true}}, "isDefault": {"edit": {"label": "isDefault", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "isDefault", "searchable": true, "sortable": true}}, "userLevelLimited": {"edit": {"label": "userLevelLimited", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "userLevelLimited", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "name", "description", "related"], "edit": [[{"name": "name", "size": 6}], [{"name": "locales", "size": 12}], [{"name": "description", "size": 6}, {"name": "related", "size": 6}], [{"name": "blocked", "size": 4}, {"name": "type", "size": 6}], [{"name": "creator", "size": 6}, {"name": "postingPermission", "size": 4}], [{"name": "subscribers", "size": 6}, {"name": "coupons", "size": 6}], [{"name": "order", "size": 4}, {"name": "isDefault", "size": 4}], [{"name": "userLevelLimited", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}