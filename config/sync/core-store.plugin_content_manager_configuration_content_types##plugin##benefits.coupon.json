{"key": "plugin_content_manager_configuration_content_types::plugin::benefits.coupon", "value": {"uid": "plugin::benefits.coupon", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "title", "defaultSortBy": "title", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "title": {"edit": {"label": "title", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "content": {"edit": {"label": "content", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "content", "searchable": false, "sortable": false}}, "media": {"edit": {"label": "media", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "media", "searchable": false, "sortable": false}}, "pinned": {"edit": {"label": "pinned", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "pinned", "searchable": true, "sortable": true}}, "blocked": {"edit": {"label": "blocked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "blocked", "searchable": true, "sortable": true}}, "removed": {"edit": {"label": "removed", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "removed", "searchable": true, "sortable": true}}, "likeCount": {"edit": {"label": "likeCount", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "likeCount", "searchable": true, "sortable": true}}, "board": {"edit": {"label": "board", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "board", "searchable": true, "sortable": true}}, "likers": {"edit": {"label": "likers", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "likers", "searchable": false, "sortable": false}}, "expiresAt": {"edit": {"label": "expiresAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "expiresAt", "searchable": true, "sortable": true}}, "phone": {"edit": {"label": "phone", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "phone", "searchable": true, "sortable": true}}, "level": {"edit": {"label": "level", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "level", "searchable": true, "sortable": true}}, "description": {"edit": {"label": "description", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "description", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "title", "media", "pinned", "expiresAt"], "edit": [[{"name": "title", "size": 6}], [{"name": "content", "size": 12}], [{"name": "media", "size": 6}, {"name": "pinned", "size": 4}], [{"name": "blocked", "size": 4}, {"name": "removed", "size": 4}, {"name": "likeCount", "size": 4}], [{"name": "board", "size": 6}, {"name": "likers", "size": 6}], [{"name": "expiresAt", "size": 6}, {"name": "phone", "size": 6}], [{"name": "level", "size": 4}, {"name": "description", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}