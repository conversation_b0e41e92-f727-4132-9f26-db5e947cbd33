{"key": "plugin_content_manager_configuration_content_types::api::verification.verification", "value": {"uid": "api::verification.verification", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "code", "defaultSortBy": "code", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "verificationId": {"edit": {"label": "verificationId", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "verificationId", "searchable": true, "sortable": true}}, "code": {"edit": {"label": "code", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "code", "searchable": true, "sortable": true}}, "expiredAt": {"edit": {"label": "expiredAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "expiredAt", "searchable": true, "sortable": true}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "verificationId", "code", "expiredAt"], "edit": [[{"name": "verificationId", "size": 6}, {"name": "code", "size": 6}], [{"name": "expiredAt", "size": 6}, {"name": "user", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}