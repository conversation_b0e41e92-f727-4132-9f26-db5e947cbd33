{"name": "Public", "description": "Default role given to unauthenticated user.", "type": "public", "permissions": [{"action": "plugin::users-permissions.auth.callback"}, {"action": "plugin::users-permissions.auth.connect"}, {"action": "plugin::users-permissions.auth.emailConfirmation"}, {"action": "plugin::users-permissions.auth.forgotPassword"}, {"action": "plugin::users-permissions.auth.register"}, {"action": "plugin::users-permissions.auth.resetPassword"}, {"action": "plugin::users-permissions.auth.sendEmailConfirmation"}]}