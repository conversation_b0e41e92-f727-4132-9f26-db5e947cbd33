{"key": "plugin_content_manager_configuration_content_types::api::verification-log.verification-log", "value": {"uid": "api::verification-log.verification-log", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "status": {"edit": {"label": "status", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "status", "searchable": true, "sortable": true}}, "note": {"edit": {"label": "note", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "note", "searchable": false, "sortable": false}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "status", "user", "createdAt"], "edit": [[{"name": "status", "size": 6}], [{"name": "note", "size": 12}], [{"name": "user", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}