{"key": "plugin_content_manager_configuration_content_types::plugin::audit-log.log", "value": {"uid": "plugin::audit-log.log", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "operatorName", "defaultSortBy": "operatorName", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "origin": {"edit": {"label": "origin", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "origin", "searchable": true, "sortable": true}}, "operatorId": {"edit": {"label": "operatorId", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "operatorId", "searchable": true, "sortable": true}}, "operatorName": {"edit": {"label": "operatorName", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "operatorName", "searchable": true, "sortable": true}}, "operatorEmail": {"edit": {"label": "operatorEmail", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "operatorEmail", "searchable": true, "sortable": true}}, "operation": {"edit": {"label": "operation", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "operation", "searchable": true, "sortable": true}}, "url": {"edit": {"label": "url", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "url", "searchable": true, "sortable": true}}, "ip_address": {"edit": {"label": "ip_address", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "ip_address", "searchable": true, "sortable": true}}, "http_method": {"edit": {"label": "http_method", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "http_method", "searchable": true, "sortable": true}}, "http_status": {"edit": {"label": "http_status", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "http_status", "searchable": true, "sortable": true}}, "device_id": {"edit": {"label": "device_id", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "device_id", "searchable": true, "sortable": true}}, "user_agent": {"edit": {"label": "user_agent", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "user_agent", "searchable": true, "sortable": true}}, "request_header": {"edit": {"label": "request_header", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "request_header", "searchable": false, "sortable": false}}, "request_body": {"edit": {"label": "request_body", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "request_body", "searchable": false, "sortable": false}}, "response_body": {"edit": {"label": "response_body", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "response_body", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "origin", "operatorId", "operatorName"], "edit": [[{"name": "origin", "size": 6}, {"name": "operatorId", "size": 4}], [{"name": "operatorName", "size": 6}, {"name": "operatorEmail", "size": 6}], [{"name": "operation", "size": 6}, {"name": "url", "size": 6}], [{"name": "ip_address", "size": 6}, {"name": "http_method", "size": 6}], [{"name": "http_status", "size": 4}, {"name": "device_id", "size": 6}], [{"name": "user_agent", "size": 6}], [{"name": "request_header", "size": 12}], [{"name": "request_body", "size": 12}], [{"name": "response_body", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}