{"key": "plugin_content_manager_configuration_content_types::api::deleted-user.deleted-user", "value": {"uid": "api::deleted-user.deleted-user", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "username", "defaultSortBy": "username", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "username": {"edit": {"label": "username", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "username", "searchable": true, "sortable": true}}, "email": {"edit": {"label": "email", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "email", "searchable": true, "sortable": true}}, "provider": {"edit": {"label": "provider", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "provider", "searchable": true, "sortable": true}}, "photos": {"edit": {"label": "photos", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "photos", "searchable": false, "sortable": false}}, "reasons": {"edit": {"label": "reasons", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "reasons", "searchable": false, "sortable": false}}, "note": {"edit": {"label": "note", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "note", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "user", "username", "email"], "edit": [[{"name": "user", "size": 6}, {"name": "username", "size": 6}], [{"name": "email", "size": 6}, {"name": "provider", "size": 6}], [{"name": "photos", "size": 6}], [{"name": "reasons", "size": 12}], [{"name": "note", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}