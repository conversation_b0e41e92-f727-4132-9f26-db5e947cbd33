{"key": "plugin_content_manager_configuration_content_types::api::apple-notification.apple-notification", "value": {"uid": "api::apple-notification.apple-notification", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "signedPayload": {"edit": {"label": "signedPayload", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "signedPayload", "searchable": true, "sortable": true}}, "payload": {"edit": {"label": "payload", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "payload", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "signedPayload", "createdAt", "updatedAt"], "edit": [[{"name": "signedPayload", "size": 6}], [{"name": "payload", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}