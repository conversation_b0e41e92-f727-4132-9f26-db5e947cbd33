{"key": "plugin_content_manager_configuration_content_types::plugin::users-permissions.user", "value": {"uid": "plugin::users-permissions.user", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "provider", "defaultSortBy": "provider", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "email": {"edit": {"label": "email", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "email", "searchable": true, "sortable": true}}, "provider": {"edit": {"label": "provider", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "provider", "searchable": true, "sortable": true}}, "password": {"edit": {"label": "password", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "password", "searchable": true, "sortable": true}}, "resetPasswordToken": {"edit": {"label": "resetPasswordToken", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "resetPasswordToken", "searchable": true, "sortable": true}}, "confirmationToken": {"edit": {"label": "confirmationToken", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "confirmationToken", "searchable": true, "sortable": true}}, "confirmed": {"edit": {"label": "confirmed", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "confirmed", "searchable": true, "sortable": true}}, "blocked": {"edit": {"label": "blocked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "blocked", "searchable": true, "sortable": true}}, "role": {"edit": {"label": "role", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "role", "searchable": true, "sortable": true}}, "username": {"edit": {"label": "username", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "username", "searchable": true, "sortable": true}}, "fullname": {"edit": {"label": "fullname", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "fullname", "searchable": true, "sortable": true}}, "agreed": {"edit": {"label": "agreed", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "agreed", "searchable": true, "sortable": true}}, "deleted": {"edit": {"label": "deleted", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "deleted", "searchable": true, "sortable": true}}, "deletedAt": {"edit": {"label": "deletedAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "deletedAt", "searchable": true, "sortable": true}}, "phone": {"edit": {"label": "phone", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "phone", "searchable": true, "sortable": true}}, "birthday": {"edit": {"label": "birthday", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "birthday", "searchable": true, "sortable": true}}, "gender": {"edit": {"label": "gender", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "gender", "searchable": true, "sortable": true}}, "introduction": {"edit": {"label": "introduction", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "introduction", "searchable": true, "sortable": true}}, "location": {"edit": {"label": "location", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "location", "searchable": true, "sortable": true}}, "latitude": {"edit": {"label": "latitude", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "latitude", "searchable": true, "sortable": true}}, "longitude": {"edit": {"label": "longitude", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "longitude", "searchable": true, "sortable": true}}, "avatar": {"edit": {"label": "avatar", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "avatar", "searchable": false, "sortable": false}}, "photos": {"edit": {"label": "photos", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "photos", "searchable": false, "sortable": false}}, "interests": {"edit": {"label": "interests", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "tag"}, "list": {"label": "interests", "searchable": false, "sortable": false}}, "university": {"edit": {"label": "university", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "university", "searchable": true, "sortable": true}}, "certificate": {"edit": {"label": "certificate", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "certificate", "searchable": false, "sortable": false}}, "verificationEmail": {"edit": {"label": "verificationEmail", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "verificationEmail", "searchable": true, "sortable": true}}, "verificationType": {"edit": {"label": "verificationType", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "verificationType", "searchable": true, "sortable": true}}, "emailSentAt": {"edit": {"label": "emailSentAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "emailSentAt", "searchable": true, "sortable": true}}, "verificationStatus": {"edit": {"label": "verificationStatus", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "verificationStatus", "searchable": true, "sortable": true}}, "balance": {"edit": {"label": "balance", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "balance", "searchable": true, "sortable": true}}, "totalExpenses": {"edit": {"label": "totalExpenses", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "totalExpenses", "searchable": true, "sortable": true}}, "avgRating": {"edit": {"label": "avgRating", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "avgRating", "searchable": true, "sortable": true}}, "countryCode": {"edit": {"label": "countryCode", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "countryCode", "searchable": true, "sortable": true}}, "referralCode": {"edit": {"label": "referralCode", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "referralCode", "searchable": true, "sortable": true}}, "regRefCode": {"edit": {"label": "regRefCode", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "regRefCode", "searchable": true, "sortable": true}}, "receiveNotification": {"edit": {"label": "receiveNotification", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "receiveNotification", "searchable": true, "sortable": true}}, "status": {"edit": {"label": "status", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "status", "searchable": true, "sortable": true}}, "fakeName": {"edit": {"label": "fakeName", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "fakeName", "searchable": true, "sortable": true}}, "visibility": {"edit": {"label": "visibility", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "visibility", "searchable": true, "sortable": true}}, "verificationLogs": {"edit": {"label": "verificationLogs", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "id"}, "list": {"label": "verificationLogs", "searchable": false, "sortable": false}}, "level": {"edit": {"label": "level", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "level", "searchable": true, "sortable": true}}, "orientation": {"edit": {"label": "orientation", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "orientation", "searchable": true, "sortable": true}}, "regionCode": {"edit": {"label": "regionCode", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "regionCode", "searchable": true, "sortable": true}}, "financialStatus": {"edit": {"label": "financialStatus", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "financialStatus", "searchable": true, "sortable": true}}, "certificateList": {"edit": {"label": "certificateList", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "certificateList", "searchable": false, "sortable": false}}, "nationalityCode": {"edit": {"label": "nationalityCode", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "nationalityCode", "searchable": true, "sortable": true}}, "phoneVerificationStatus": {"edit": {"label": "phoneVerificationStatus", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "phoneVerificationStatus", "searchable": true, "sortable": true}}, "firstTimeUpdateProfile": {"edit": {"label": "firstTimeUpdateProfile", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "firstTimeUpdateProfile", "searchable": true, "sortable": true}}, "title": {"edit": {"label": "title", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "title", "searchable": true, "sortable": true}}, "fakeRandomAvatar": {"edit": {"label": "fakeRandomAvatar", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "fakeRandomAvatar", "searchable": true, "sortable": true}}, "fakeAvatar": {"edit": {"label": "<PERSON><PERSON><PERSON><PERSON>", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "<PERSON><PERSON><PERSON><PERSON>", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "email", "confirmed", "blocked"], "edit": [[{"name": "email", "size": 6}, {"name": "password", "size": 6}], [{"name": "confirmed", "size": 4}, {"name": "blocked", "size": 4}], [{"name": "role", "size": 6}, {"name": "username", "size": 6}], [{"name": "fullname", "size": 6}, {"name": "agreed", "size": 4}], [{"name": "deleted", "size": 4}, {"name": "deletedAt", "size": 6}], [{"name": "phone", "size": 6}, {"name": "birthday", "size": 4}], [{"name": "gender", "size": 6}, {"name": "introduction", "size": 6}], [{"name": "location", "size": 6}, {"name": "latitude", "size": 4}], [{"name": "longitude", "size": 4}, {"name": "avatar", "size": 6}], [{"name": "photos", "size": 6}, {"name": "interests", "size": 6}], [{"name": "university", "size": 6}, {"name": "certificate", "size": 6}], [{"name": "verificationEmail", "size": 6}, {"name": "verificationType", "size": 6}], [{"name": "emailSentAt", "size": 6}, {"name": "verificationStatus", "size": 6}], [{"name": "balance", "size": 4}, {"name": "totalExpenses", "size": 4}, {"name": "avgRating", "size": 4}], [{"name": "countryCode", "size": 6}, {"name": "referralCode", "size": 6}], [{"name": "regRefCode", "size": 6}, {"name": "receiveNotification", "size": 4}], [{"name": "status", "size": 6}, {"name": "fakeName", "size": 6}], [{"name": "visibility", "size": 4}, {"name": "verificationLogs", "size": 6}], [{"name": "level", "size": 4}, {"name": "orientation", "size": 4}], [{"name": "regionCode", "size": 6}, {"name": "financialStatus", "size": 6}], [{"name": "certificateList", "size": 6}, {"name": "nationalityCode", "size": 6}], [{"name": "phoneVerificationStatus", "size": 6}, {"name": "firstTimeUpdateProfile", "size": 4}], [{"name": "title", "size": 6}], [{"name": "fakeRandomAvatar", "size": 6}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}