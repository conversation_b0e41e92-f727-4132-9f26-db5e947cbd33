{"key": "plugin_content_manager_configuration_content_types::api::subscription.subscription", "value": {"uid": "api::subscription.subscription", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "startedAt": {"edit": {"label": "startedAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "startedAt", "searchable": true, "sortable": true}}, "endedAt": {"edit": {"label": "endedAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "endedAt", "searchable": true, "sortable": true}}, "duration": {"edit": {"label": "duration", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "duration", "searchable": true, "sortable": true}}, "subscriptionPlan": {"edit": {"label": "subscriptionPlan", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "subscriptionPlan", "searchable": true, "sortable": true}}, "ex": {"edit": {"label": "ex", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "ex", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "user", "startedAt", "endedAt"], "edit": [[{"name": "user", "size": 6}, {"name": "startedAt", "size": 6}], [{"name": "endedAt", "size": 6}, {"name": "duration", "size": 4}], [{"name": "subscriptionPlan", "size": 6}], [{"name": "ex", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}