{"key": "plugin_content_manager_configuration_content_types::api::report.report", "value": {"uid": "api::report.report", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "reporter": {"edit": {"label": "reporter", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "reporter", "searchable": true, "sortable": true}}, "reported": {"edit": {"label": "reported", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "reported", "searchable": true, "sortable": true}}, "reasons": {"edit": {"label": "reasons", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "reasons", "searchable": false, "sortable": false}}, "attachments": {"edit": {"label": "attachments", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "attachments", "searchable": false, "sortable": false}}, "action": {"edit": {"label": "action", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "action", "searchable": true, "sortable": true}}, "tempBanDuration": {"edit": {"label": "tempBanDuration", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "tempBanDuration", "searchable": true, "sortable": true}}, "processedAt": {"edit": {"label": "processedAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "processedAt", "searchable": true, "sortable": true}}, "note": {"edit": {"label": "note", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "note", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "reporter", "reported", "attachments"], "edit": [[{"name": "reporter", "size": 6}, {"name": "reported", "size": 6}], [{"name": "reasons", "size": 12}], [{"name": "attachments", "size": 6}, {"name": "action", "size": 6}], [{"name": "tempBanDuration", "size": 4}, {"name": "processedAt", "size": 6}], [{"name": "note", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}