{"key": "plugin_content_manager_configuration_content_types::api::rating.rating", "value": {"uid": "api::rating.rating", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "rater": {"edit": {"label": "rater", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "rater", "searchable": true, "sortable": true}}, "rated": {"edit": {"label": "rated", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "rated", "searchable": true, "sortable": true}}, "score": {"edit": {"label": "score", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "score", "searchable": true, "sortable": true}}, "locked": {"edit": {"label": "locked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "locked", "searchable": true, "sortable": true}}, "lockPrice": {"edit": {"label": "lockPrice", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "lockPrice", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "rater", "rated", "score"], "edit": [[{"name": "rater", "size": 6}, {"name": "rated", "size": 6}], [{"name": "score", "size": 4}, {"name": "locked", "size": 4}, {"name": "lockPrice", "size": 4}]]}}, "type": "object", "environment": null, "tag": null}