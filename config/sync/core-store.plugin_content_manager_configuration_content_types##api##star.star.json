{"key": "plugin_content_manager_configuration_content_types::api::star.star", "value": {"uid": "api::star.star", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "playingDate": {"edit": {"label": "playingDate", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "playingDate", "searchable": true, "sortable": true}}, "event": {"edit": {"label": "event", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "event", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "playingDate", "updatedAt", "event"], "edit": [[{"name": "playingDate", "size": 6}, {"name": "event", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}