{"key": "plugin_content_manager_configuration_content_types::api::setting.setting", "value": {"uid": "api::setting.setting", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "key", "defaultSortBy": "key", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "key": {"edit": {"label": "key", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "key", "searchable": true, "sortable": true}}, "value": {"edit": {"label": "value", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "value", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "key", "createdAt", "updatedAt"], "edit": [[{"name": "key", "size": 6}], [{"name": "value", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}