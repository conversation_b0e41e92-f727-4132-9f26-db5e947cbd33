{"key": "plugin_content_manager_configuration_content_types::plugin::i18n.locale", "value": {"uid": "plugin::i18n.locale", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "name", "defaultSortBy": "name", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "name", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "code": {"edit": {"label": "code", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "code", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "name", "code", "createdAt"], "edit": [[{"name": "name", "size": 6}, {"name": "code", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}