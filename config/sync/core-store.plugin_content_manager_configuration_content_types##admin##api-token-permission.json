{"key": "plugin_content_manager_configuration_content_types::admin::api-token-permission", "value": {"uid": "admin::api-token-permission", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "action", "defaultSortBy": "action", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "action": {"edit": {"label": "action", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "action", "searchable": true, "sortable": true}}, "token": {"edit": {"label": "token", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "token", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "action", "token", "createdAt"], "edit": [[{"name": "action", "size": 6}, {"name": "token", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}