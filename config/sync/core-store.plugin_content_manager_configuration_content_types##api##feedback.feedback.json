{"key": "plugin_content_manager_configuration_content_types::api::feedback.feedback", "value": {"uid": "api::feedback.feedback", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "email": {"edit": {"label": "email", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "email", "searchable": true, "sortable": true}}, "category": {"edit": {"label": "category", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "category", "searchable": true, "sortable": true}}, "content": {"edit": {"label": "content", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "content", "searchable": true, "sortable": true}}, "attachments": {"edit": {"label": "attachments", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "attachments", "searchable": false, "sortable": false}}, "answer": {"edit": {"label": "answer", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "answer", "searchable": true, "sortable": true}}, "processedAt": {"edit": {"label": "processedAt", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "processedAt", "searchable": true, "sortable": true}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "email", "category", "content"], "edit": [[{"name": "email", "size": 6}, {"name": "category", "size": 6}], [{"name": "content", "size": 6}, {"name": "attachments", "size": 6}], [{"name": "answer", "size": 6}, {"name": "processedAt", "size": 6}], [{"name": "user", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}