{"key": "plugin_content_manager_configuration_content_types::api::star-event-member.star-event-member", "value": {"uid": "api::star-event-member.star-event-member", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "event": {"edit": {"label": "event", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "event", "searchable": true, "sortable": true}}, "status": {"edit": {"label": "status", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "status", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"edit": [[{"name": "user", "size": 6}, {"name": "event", "size": 6}], [{"name": "status", "size": 6}]], "list": ["id", "user", "event", "status"]}}, "type": "object", "environment": null, "tag": null}