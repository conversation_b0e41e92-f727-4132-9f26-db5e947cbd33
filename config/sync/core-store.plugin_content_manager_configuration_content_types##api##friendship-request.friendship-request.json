{"key": "plugin_content_manager_configuration_content_types::api::friendship-request.friendship-request", "value": {"uid": "api::friendship-request.friendship-request", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "source", "defaultSortBy": "source", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "sender": {"edit": {"label": "sender", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "sender", "searchable": true, "sortable": true}}, "receiver": {"edit": {"label": "receiver", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "receiver", "searchable": true, "sortable": true}}, "level": {"edit": {"label": "level", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "level", "searchable": true, "sortable": true}}, "locked": {"edit": {"label": "locked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "locked", "searchable": true, "sortable": true}}, "lockPrice": {"edit": {"label": "lockPrice", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "lockPrice", "searchable": true, "sortable": true}}, "source": {"edit": {"label": "source", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "source", "searchable": true, "sortable": true}}, "handleResult": {"edit": {"label": "handleResult", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "handleResult", "searchable": true, "sortable": true}}, "handleMsg": {"edit": {"label": "handleMsg", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "handleMsg", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "sender", "receiver", "level"], "edit": [[{"name": "sender", "size": 6}, {"name": "receiver", "size": 6}], [{"name": "level", "size": 6}, {"name": "locked", "size": 4}], [{"name": "lockPrice", "size": 4}, {"name": "source", "size": 6}], [{"name": "handleResult", "size": 6}, {"name": "handleMsg", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}