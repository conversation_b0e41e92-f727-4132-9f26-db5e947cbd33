{"key": "plugin_content_manager_configuration_content_types::api::term.term", "value": {"uid": "api::term.term", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "content": {"edit": {"label": "content", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "content", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "createdAt", "updatedAt"], "edit": [[{"name": "content", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}