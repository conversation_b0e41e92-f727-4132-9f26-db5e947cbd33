{"key": "plugin_content_manager_configuration_content_types::api::recharge.recharge", "value": {"uid": "api::recharge.recharge", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "rechargeId", "defaultSortBy": "rechargeId", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "customer": {"edit": {"label": "customer", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "customer", "searchable": true, "sortable": true}}, "product": {"edit": {"label": "product", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "productId"}, "list": {"label": "product", "searchable": true, "sortable": true}}, "rechargeId": {"edit": {"label": "rechargeId", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "rechargeId", "searchable": true, "sortable": true}}, "paymentMethod": {"edit": {"label": "paymentMethod", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "paymentMethod", "searchable": true, "sortable": true}}, "transactionId": {"edit": {"label": "transactionId", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "transactionId", "searchable": true, "sortable": true}}, "amount": {"edit": {"label": "amount", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "amount", "searchable": true, "sortable": true}}, "status": {"edit": {"label": "status", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "status", "searchable": true, "sortable": true}}, "productBak": {"edit": {"label": "productBak", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "productBak", "searchable": false, "sortable": false}}, "ex": {"edit": {"label": "ex", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "ex", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "customer", "product", "rechargeId"], "edit": [[{"name": "customer", "size": 6}, {"name": "product", "size": 6}], [{"name": "rechargeId", "size": 6}, {"name": "paymentMethod", "size": 6}], [{"name": "transactionId", "size": 6}, {"name": "amount", "size": 4}], [{"name": "status", "size": 6}], [{"name": "productBak", "size": 12}], [{"name": "ex", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}