{"key": "plugin_content_manager_configuration_content_types::plugin::community.comment", "value": {"uid": "plugin::community.comment", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "content": {"edit": {"label": "content", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "content", "searchable": true, "sortable": true}}, "blocked": {"edit": {"label": "blocked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "blocked", "searchable": true, "sortable": true}}, "removed": {"edit": {"label": "removed", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "removed", "searchable": true, "sortable": true}}, "threadOf": {"edit": {"label": "threadOf", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "id"}, "list": {"label": "threadOf", "searchable": true, "sortable": true}}, "post": {"edit": {"label": "post", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "post", "searchable": true, "sortable": true}}, "authorUser": {"edit": {"label": "authorUser", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "authorUser", "searchable": true, "sortable": true}}, "likers": {"edit": {"label": "likers", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "likers", "searchable": false, "sortable": false}}, "dislikers": {"edit": {"label": "dislikers", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "dislikers", "searchable": false, "sortable": false}}, "mentions": {"edit": {"label": "mentions", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "mentions", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "content", "blocked", "removed"], "edit": [[{"name": "content", "size": 6}, {"name": "blocked", "size": 4}], [{"name": "removed", "size": 4}, {"name": "threadOf", "size": 6}], [{"name": "post", "size": 6}, {"name": "authorUser", "size": 6}], [{"name": "likers", "size": 6}, {"name": "dislikers", "size": 6}], [{"name": "mentions", "size": 12}]]}}, "type": "object", "environment": null, "tag": null}