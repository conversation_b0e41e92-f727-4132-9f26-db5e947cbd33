{"key": "plugin_users-permissions_email", "value": {"reset_password": {"display": "Email.template.reset_password", "icon": "sync", "options": {"from": {"name": "Administration Panel", "email": "<EMAIL>"}, "response_email": "", "object": "Reset password", "message": "<p>We heard that you lost your password. Sorry about that!</p>\n\n<p>But don’t worry! You can use the following link to reset your password:</p>\n<p><%= URL %>?code=<%= TOKEN %></p>\n\n<p>Thanks.</p>"}}, "email_confirmation": {"display": "Email.template.email_confirmation", "icon": "check-square", "options": {"from": {"name": "Administration Panel", "email": "<EMAIL>"}, "response_email": "", "object": "Account confirmation", "message": "<p>Thank you for registering!</p>\n\n<p>You have to confirm your email address. Please click on the link below.</p>\n\n<p><%= URL %>?confirmation=<%= CODE %></p>\n\n<p>Thanks.</p>"}}, "email_captcha": {"display": "Email.template.email_confirmation", "icon": "check-square", "options": {"from": {"name": "Administration Panel", "email": "<EMAIL>"}, "response_email": "", "object": "Account Registration Verification Code", "message": "<p>Thank you for registering!</p>\n\n<p>Thank you for choosing to register with our platform. As part of our security measures, we require all users to verify their email addresses. To complete the registration process, we have sent a verification code to the email address associated with your account.</p>\n\n<p>Please find below your unique verification code:</p>\n<p>Verification Code: [<%= CODE %>]</p>\n\n<p>Thanks.</p>"}}}, "type": "object", "environment": null, "tag": null}