{"key": "plugin_content_manager_configuration_content_types::api::pre-membership.pre-membership", "value": {"uid": "api::pre-membership.pre-membership", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "realname", "defaultSortBy": "realname", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "realname": {"edit": {"label": "realname", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "realname", "searchable": true, "sortable": true}}, "nickname": {"edit": {"label": "nickname", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "nickname", "searchable": true, "sortable": true}}, "university": {"edit": {"label": "university", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "name"}, "list": {"label": "university", "searchable": true, "sortable": true}}, "email": {"edit": {"label": "email", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "email", "searchable": true, "sortable": true}}, "countryCode": {"edit": {"label": "countryCode", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "countryCode", "searchable": true, "sortable": true}}, "phone": {"edit": {"label": "phone", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "phone", "searchable": true, "sortable": true}}, "photos": {"edit": {"label": "photos", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "photos", "searchable": false, "sortable": false}}, "referrer": {"edit": {"label": "referrer", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "referrer", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "realname", "nickname", "university"], "edit": [[{"name": "realname", "size": 6}, {"name": "nickname", "size": 6}], [{"name": "university", "size": 6}, {"name": "email", "size": 6}], [{"name": "countryCode", "size": 6}, {"name": "phone", "size": 6}], [{"name": "photos", "size": 6}, {"name": "referrer", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}