{"key": "plugin_content_manager_configuration_content_types::api::black.black", "value": {"uid": "api::black.black", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "user": {"edit": {"label": "user", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "user", "searchable": true, "sortable": true}}, "blockUser": {"edit": {"label": "blockUser", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "blockUser", "searchable": true, "sortable": true}}, "ex": {"edit": {"label": "ex", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "ex", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "user", "blockUser", "ex"], "edit": [[{"name": "user", "size": 6}, {"name": "blockUser", "size": 6}], [{"name": "ex", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}