{"name": "Authenticated", "description": "Default role given to authenticated user.", "type": "authenticated", "permissions": [{"action": "api::ads-insight.ads-insight.create"}, {"action": "api::ads-insight.ads-insight.delete"}, {"action": "api::ads-insight.ads-insight.find"}, {"action": "api::ads-insight.ads-insight.findOne"}, {"action": "api::ads-insight.ads-insight.update"}, {"action": "api::apple-notification.apple-notification.create"}, {"action": "api::apple-notification.apple-notification.delete"}, {"action": "api::apple-notification.apple-notification.find"}, {"action": "api::apple-notification.apple-notification.findOne"}, {"action": "api::apple-notification.apple-notification.update"}, {"action": "api::black.black.create"}, {"action": "api::black.black.find"}, {"action": "api::black.black.remove"}, {"action": "api::captcha.captcha.request"}, {"action": "api::captcha.captcha.verify"}, {"action": "api::card.card.accept"}, {"action": "api::card.card.deleteCard"}, {"action": "api::card.card.detail"}, {"action": "api::card.card.findGot"}, {"action": "api::card.card.findHighEval"}, {"action": "api::card.card.findMatched"}, {"action": "api::card.card.findPassed"}, {"action": "api::card.card.findSent"}, {"action": "api::card.card.findTempToday"}, {"action": "api::card.card.findToday"}, {"action": "api::card.card.getHeartStatus"}, {"action": "api::card.card.jobDetail"}, {"action": "api::card.card.premium"}, {"action": "api::card.card.premiumRule"}, {"action": "api::card.card.rating"}, {"action": "api::card.card.reject"}, {"action": "api::card.card.sendResume"}, {"action": "api::card.card.sentHeart"}, {"action": "api::card.card.suggestJobs"}, {"action": "api::card.card.superSend"}, {"action": "api::card.card.unlock"}, {"action": "api::feedback-category.feedback-category.find"}, {"action": "api::feedback.feedback.create"}, {"action": "api::interest-category.interest-category.find"}, {"action": "api::interest.interest.find"}, {"action": "api::meecoin.meecoin.find"}, {"action": "api::posts.posts.postsReport"}, {"action": "api::pre-membership.pre-membership.checkNickname"}, {"action": "api::pre-membership.pre-membership.checkReferrer"}, {"action": "api::pre-membership.pre-membership.create"}, {"action": "api::pre-membership.pre-membership.exportToExcel"}, {"action": "api::pre-membership.pre-membership.find"}, {"action": "api::pre-membership.pre-membership.findOne"}, {"action": "api::recharge.recharge.generateOrder"}, {"action": "api::recharge.recharge.googleVerify"}, {"action": "api::recharge.recharge.notification"}, {"action": "api::recharge.recharge.sandboxNotification"}, {"action": "api::recharge.recharge.verifyReceipt"}, {"action": "api::report.report.create"}, {"action": "api::star-comment.star-comment.getEventComments"}, {"action": "api::star-comment.star-comment.postEventComments"}, {"action": "api::star-event-member.star-event-member.getMembers"}, {"action": "api::star-event-member.star-event-member.joinEvent"}, {"action": "api::star-event.star-event.findOne"}, {"action": "api::star-event.star-event.getEvents"}, {"action": "api::star-event.star-event.getEventSections"}, {"action": "api::star-event.star-event.getMyPastEvents"}, {"action": "api::star.star.findCurrent"}, {"action": "api::star.star.findPast"}, {"action": "api::term.term.createLocalization"}, {"action": "api::term.term.delete"}, {"action": "api::term.term.find"}, {"action": "api::term.term.update"}, {"action": "api::third-party.third-party.geocode"}, {"action": "api::third-party.third-party.hello"}, {"action": "api::third-party.third-party.inappproducts"}, {"action": "api::transaction.transaction.find"}, {"action": "api::university.university.find"}, {"action": "api::university.university.findOne"}, {"action": "api::university.university.getRegions"}, {"action": "api::verification.verification.sendVerificationCode"}, {"action": "api::verification.verification.verifyCode"}, {"action": "plugin::benefits.board.find"}, {"action": "plugin::benefits.coupon.find"}, {"action": "plugin::benefits.coupon.findOne"}, {"action": "plugin::community.board.find"}, {"action": "plugin::community.comment.dislikeToggle"}, {"action": "plugin::community.comment.find"}, {"action": "plugin::community.comment.likeToggle"}, {"action": "plugin::community.comment.post"}, {"action": "plugin::community.comment.remove"}, {"action": "plugin::community.post.dislikeToggle"}, {"action": "plugin::community.post.find"}, {"action": "plugin::community.post.findLikes"}, {"action": "plugin::community.post.findOne"}, {"action": "plugin::community.post.findSuggestions"}, {"action": "plugin::community.post.likeToggle"}, {"action": "plugin::community.post.post"}, {"action": "plugin::community.post.remove"}, {"action": "plugin::community.post.update"}, {"action": "plugin::community.report.post"}, {"action": "plugin::community.user.getUserComments"}, {"action": "plugin::community.user.getUserInfo"}, {"action": "plugin::community.user.getUserPosts"}, {"action": "plugin::content-type-builder.components.getComponent"}, {"action": "plugin::content-type-builder.components.getComponents"}, {"action": "plugin::content-type-builder.content-types.getContentType"}, {"action": "plugin::content-type-builder.content-types.getContentTypes"}, {"action": "plugin::email.email.send"}, {"action": "plugin::i18n.locales.listLocales"}, {"action": "plugin::manager.version.checkVersion"}, {"action": "plugin::notification.notification.badge"}, {"action": "plugin::notification.notification.find"}, {"action": "plugin::notification.notification.view"}, {"action": "plugin::notification.notification.viewAll"}, {"action": "plugin::openim.user.connect"}, {"action": "plugin::openim.user.getToken"}, {"action": "plugin::openim.user.importUsers"}, {"action": "plugin::upload.content-api.destroy"}, {"action": "plugin::upload.content-api.find"}, {"action": "plugin::upload.content-api.findOne"}, {"action": "plugin::upload.content-api.upload"}, {"action": "plugin::users-permissions.auth.callback"}, {"action": "plugin::users-permissions.auth.changePassword"}, {"action": "plugin::users-permissions.auth.connect"}, {"action": "plugin::users-permissions.auth.emailConfirmation"}, {"action": "plugin::users-permissions.auth.forgotPassword"}, {"action": "plugin::users-permissions.auth.register"}, {"action": "plugin::users-permissions.auth.resetPassword"}, {"action": "plugin::users-permissions.auth.sendEmailConfirmation"}, {"action": "plugin::users-permissions.permissions.getPermissions"}, {"action": "plugin::users-permissions.role.createRole"}, {"action": "plugin::users-permissions.role.deleteRole"}, {"action": "plugin::users-permissions.role.find"}, {"action": "plugin::users-permissions.role.findOne"}, {"action": "plugin::users-permissions.role.updateRole"}, {"action": "plugin::users-permissions.user.balance"}, {"action": "plugin::users-permissions.user.certificateVerification"}, {"action": "plugin::users-permissions.user.checkFakeName"}, {"action": "plugin::users-permissions.user.checkName"}, {"action": "plugin::users-permissions.user.connectCommunity"}, {"action": "plugin::users-permissions.user.count"}, {"action": "plugin::users-permissions.user.create"}, {"action": "plugin::users-permissions.user.delete"}, {"action": "plugin::users-permissions.user.destroy"}, {"action": "plugin::users-permissions.user.emailVerification"}, {"action": "plugin::users-permissions.user.find"}, {"action": "plugin::users-permissions.user.findOne"}, {"action": "plugin::users-permissions.user.getRegisterBonus"}, {"action": "plugin::users-permissions.user.getVerificationState"}, {"action": "plugin::users-permissions.user.me"}, {"action": "plugin::users-permissions.user.referralVerification"}, {"action": "plugin::users-permissions.user.sendEmailVerification"}, {"action": "plugin::users-permissions.user.update"}]}