{"key": "plugin_content_manager_configuration_content_types::api::star-comment.star-comment", "value": {"uid": "api::star-comment.star-comment", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "id", "defaultSortBy": "id", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "text": {"edit": {"label": "text", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "text", "searchable": true, "sortable": true}}, "creator": {"edit": {"label": "creator", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "creator", "searchable": true, "sortable": true}}, "event": {"edit": {"label": "event", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "title"}, "list": {"label": "event", "searchable": true, "sortable": true}}, "removed": {"edit": {"label": "removed", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "removed", "searchable": true, "sortable": true}}, "blocked": {"edit": {"label": "blocked", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "blocked", "searchable": true, "sortable": true}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "text", "creator", "event"], "edit": [[{"name": "text", "size": 6}], [{"name": "creator", "size": 6}, {"name": "event", "size": 6}], [{"name": "removed", "size": 4}, {"name": "blocked", "size": 4}]]}}, "type": "object", "environment": null, "tag": null}