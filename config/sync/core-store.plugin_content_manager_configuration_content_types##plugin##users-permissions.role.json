{"key": "plugin_content_manager_configuration_content_types::plugin::users-permissions.role", "value": {"uid": "plugin::users-permissions.role", "settings": {"bulkable": true, "filterable": true, "searchable": true, "pageSize": 10, "mainField": "name", "defaultSortBy": "name", "defaultSortOrder": "ASC"}, "metadatas": {"id": {"edit": {}, "list": {"label": "id", "searchable": true, "sortable": true}}, "name": {"edit": {"label": "name", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "name", "searchable": true, "sortable": true}}, "description": {"edit": {"label": "description", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "description", "searchable": true, "sortable": true}}, "type": {"edit": {"label": "type", "description": "", "placeholder": "", "visible": true, "editable": true}, "list": {"label": "type", "searchable": true, "sortable": true}}, "permissions": {"edit": {"label": "permissions", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "action"}, "list": {"label": "permissions", "searchable": false, "sortable": false}}, "users": {"edit": {"label": "users", "description": "", "placeholder": "", "visible": true, "editable": true, "mainField": "provider"}, "list": {"label": "users", "searchable": false, "sortable": false}}, "createdAt": {"edit": {"label": "createdAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "createdAt", "searchable": true, "sortable": true}}, "updatedAt": {"edit": {"label": "updatedAt", "description": "", "placeholder": "", "visible": false, "editable": true}, "list": {"label": "updatedAt", "searchable": true, "sortable": true}}}, "layouts": {"list": ["id", "name", "description", "type"], "edit": [[{"name": "name", "size": 6}, {"name": "description", "size": 6}], [{"name": "type", "size": 6}, {"name": "permissions", "size": 6}], [{"name": "users", "size": 6}]]}}, "type": "object", "environment": null, "tag": null}