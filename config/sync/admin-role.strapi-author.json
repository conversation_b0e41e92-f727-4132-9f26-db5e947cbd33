{"name": "Author", "code": "strapi-author", "description": "Authors can manage the content they have created.", "permissions": [{"action": "plugin::content-manager.explorer.create", "subject": "api::ads-insight.ads-insight", "properties": {"fields": ["user", "ads"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::ads-insight.ads-insight", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::ads-insight.ads-insight", "properties": {"fields": ["user", "ads"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::ads-insight.ads-insight", "properties": {"fields": ["user", "ads"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::apple-notification.apple-notification", "properties": {"fields": ["signedPayload", "payload"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::apple-notification.apple-notification", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::apple-notification.apple-notification", "properties": {"fields": ["signedPayload", "payload"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::apple-notification.apple-notification", "properties": {"fields": ["signedPayload", "payload"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::black.black", "properties": {"fields": ["user", "blockUser", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::black.black", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::black.black", "properties": {"fields": ["user", "blockUser", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::black.black", "properties": {"fields": ["user", "blockUser", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::deleted-user.deleted-user", "properties": {"fields": ["user", "username", "email", "provider", "photos", "reasons", "note"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::deleted-user.deleted-user", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::deleted-user.deleted-user", "properties": {"fields": ["user", "username", "email", "provider", "photos", "reasons", "note"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::deleted-user.deleted-user", "properties": {"fields": ["user", "username", "email", "provider", "photos", "reasons", "note"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::feedback-category.feedback-category", "properties": {"fields": ["name", "tag", "order", "locales"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::feedback-category.feedback-category", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::feedback-category.feedback-category", "properties": {"fields": ["name", "tag", "order", "locales"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::feedback-category.feedback-category", "properties": {"fields": ["name", "tag", "order", "locales"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::feedback.feedback", "properties": {"fields": ["email", "category", "content", "attachments", "answer", "processedAt", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::feedback.feedback", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::feedback.feedback", "properties": {"fields": ["email", "category", "content", "attachments", "answer", "processedAt", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::feedback.feedback", "properties": {"fields": ["email", "category", "content", "attachments", "answer", "processedAt", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::friendship-request.friendship-request", "properties": {"fields": ["sender", "receiver", "level", "locked", "lockPrice", "source", "handleResult", "handleMsg"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::friendship-request.friendship-request", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::friendship-request.friendship-request", "properties": {"fields": ["sender", "receiver", "level", "locked", "lockPrice", "source", "handleResult", "handleMsg"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::friendship-request.friendship-request", "properties": {"fields": ["sender", "receiver", "level", "locked", "lockPrice", "source", "handleResult", "handleMsg"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::friendship.friendship", "properties": {"fields": ["user", "friend"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::friendship.friendship", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::friendship.friendship", "properties": {"fields": ["user", "friend"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::friendship.friendship", "properties": {"fields": ["user", "friend"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::interest-category.interest-category", "properties": {"fields": ["name", "order", "locales", "interests"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::interest-category.interest-category", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::interest-category.interest-category", "properties": {"fields": ["name", "order", "locales", "interests"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::interest-category.interest-category", "properties": {"fields": ["name", "order", "locales", "interests"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::interest.interest", "properties": {"fields": ["tag", "name", "users", "source", "locales", "category", "order"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::interest.interest", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::interest.interest", "properties": {"fields": ["tag", "name", "users", "source", "locales", "category", "order"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::interest.interest", "properties": {"fields": ["tag", "name", "users", "source", "locales", "category", "order"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::meecoin.meecoin", "properties": {"fields": ["productId", "totalAmount", "amount", "bonus", "discount", "level", "price", "status", "order", "name", "recharges"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::meecoin.meecoin", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::meecoin.meecoin", "properties": {"fields": ["productId", "totalAmount", "amount", "bonus", "discount", "level", "price", "status", "order", "name", "recharges"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::meecoin.meecoin", "properties": {"fields": ["productId", "totalAmount", "amount", "bonus", "discount", "level", "price", "status", "order", "name", "recharges"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::pre-membership.pre-membership", "properties": {"fields": ["realname", "nickname", "university", "email", "countryCode", "phone", "photos", "referrer"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::pre-membership.pre-membership", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::pre-membership.pre-membership", "properties": {"fields": ["realname", "nickname", "university", "email", "countryCode", "phone", "photos", "referrer"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::pre-membership.pre-membership", "properties": {"fields": ["realname", "nickname", "university", "email", "countryCode", "phone", "photos", "referrer"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::rating.rating", "properties": {"fields": ["rater", "rated", "score", "locked", "lockPrice"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::rating.rating", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::rating.rating", "properties": {"fields": ["rater", "rated", "score", "locked", "lockPrice"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::rating.rating", "properties": {"fields": ["rater", "rated", "score", "locked", "lockPrice"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::recharge.recharge", "properties": {"fields": ["customer", "product", "rechargeId", "paymentMethod", "transactionId", "amount", "status", "productBak", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::recharge.recharge", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::recharge.recharge", "properties": {"fields": ["customer", "product", "rechargeId", "paymentMethod", "transactionId", "amount", "status", "productBak", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::recharge.recharge", "properties": {"fields": ["customer", "product", "rechargeId", "paymentMethod", "transactionId", "amount", "status", "productBak", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::recommendation.recommendation", "properties": {"fields": ["recommender", "recommended", "type", "locked", "lockPrice", "sequence"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::recommendation.recommendation", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::recommendation.recommendation", "properties": {"fields": ["recommender", "recommended", "type", "locked", "lockPrice", "sequence"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::recommendation.recommendation", "properties": {"fields": ["recommender", "recommended", "type", "locked", "lockPrice", "sequence"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::report.report", "properties": {"fields": ["reporter", "reported", "reasons", "attachments", "action", "tempBanDuration", "processedAt", "note"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::report.report", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::report.report", "properties": {"fields": ["reporter", "reported", "reasons", "attachments", "action", "tempBanDuration", "processedAt", "note"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::report.report", "properties": {"fields": ["reporter", "reported", "reasons", "attachments", "action", "tempBanDuration", "processedAt", "note"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::setting.setting", "properties": {"fields": ["key", "value"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::setting.setting", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::setting.setting", "properties": {"fields": ["key", "value"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::setting.setting", "properties": {"fields": ["key", "value"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::subscription-plan.subscription-plan", "properties": {"fields": ["name", "description", "price", "duration", "status", "locales"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::subscription-plan.subscription-plan", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::subscription-plan.subscription-plan", "properties": {"fields": ["name", "description", "price", "duration", "status", "locales"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::subscription-plan.subscription-plan", "properties": {"fields": ["name", "description", "price", "duration", "status", "locales"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::subscription.subscription", "properties": {"fields": ["user", "startedAt", "endedAt", "duration", "subscriptionPlan", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::subscription.subscription", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::subscription.subscription", "properties": {"fields": ["user", "startedAt", "endedAt", "duration", "subscriptionPlan", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::subscription.subscription", "properties": {"fields": ["user", "startedAt", "endedAt", "duration", "subscriptionPlan", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::term.term", "properties": {"fields": ["content"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::term.term", "properties": {"fields": ["content"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::term.term", "properties": {"fields": ["content"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::transaction.transaction", "properties": {"fields": ["name", "targetName", "customer", "type", "amount", "balance", "related", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::transaction.transaction", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::transaction.transaction", "properties": {"fields": ["name", "targetName", "customer", "type", "amount", "balance", "related", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::transaction.transaction", "properties": {"fields": ["name", "targetName", "customer", "type", "amount", "balance", "related", "ex"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::university.university", "properties": {"fields": ["name", "location", "regionCode", "major", "emailSuffix", "emailRegex", "logo", "rank", "website", "users", "emailSuffixes", "locales", "source", "blocked"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::university.university", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::university.university", "properties": {"fields": ["name", "location", "regionCode", "major", "emailSuffix", "emailRegex", "logo", "rank", "website", "users", "emailSuffixes", "locales", "source", "blocked"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::university.university", "properties": {"fields": ["name", "location", "regionCode", "major", "emailSuffix", "emailRegex", "logo", "rank", "website", "users", "emailSuffixes", "locales", "source", "blocked"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::verification-log.verification-log", "properties": {"fields": ["status", "note", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::verification-log.verification-log", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::verification-log.verification-log", "properties": {"fields": ["status", "note", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::verification-log.verification-log", "properties": {"fields": ["status", "note", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::verification.verification", "properties": {"fields": ["verificationId", "code", "expiredAt", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::verification.verification", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::verification.verification", "properties": {"fields": ["verificationId", "code", "expiredAt", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::verification.verification", "properties": {"fields": ["verificationId", "code", "expiredAt", "user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "api::whitelist.whitelist", "properties": {"fields": ["user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "api::whitelist.whitelist", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "api::whitelist.whitelist", "properties": {"fields": ["user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "api::whitelist.whitelist", "properties": {"fields": ["user"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "plugin::community.board", "properties": {"fields": ["name", "locales", "description", "related", "blocked", "type", "postingPermission", "creator", "subscribers", "posts", "order", "isDefault", "isAdvertisement"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "plugin::community.board", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "plugin::community.board", "properties": {"fields": ["name", "locales", "description", "related", "blocked", "type", "postingPermission", "creator", "subscribers", "posts", "order", "isDefault", "isAdvertisement"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "plugin::community.board", "properties": {"fields": ["name", "locales", "description", "related", "blocked", "type", "postingPermission", "creator", "subscribers", "posts", "order", "isDefault", "isAdvertisement"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "plugin::community.comment", "properties": {"fields": ["content", "blocked", "removed", "threadOf", "post", "authorUser", "likers", "dislikers", "mentions"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "plugin::community.comment", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "plugin::community.comment", "properties": {"fields": ["content", "blocked", "removed", "threadOf", "post", "authorUser", "likers", "dislikers", "mentions"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "plugin::community.comment", "properties": {"fields": ["content", "blocked", "removed", "threadOf", "post", "authorUser", "likers", "dislikers", "mentions"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "plugin::community.post", "properties": {"fields": ["authorUser", "title", "content", "media", "pinned", "blocked", "removed", "likeCount", "board", "likers", "dislikers", "comments", "weight", "adUrl"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "plugin::community.post", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "plugin::community.post", "properties": {"fields": ["authorUser", "title", "content", "media", "pinned", "blocked", "removed", "likeCount", "board", "likers", "dislikers", "comments", "weight", "adUrl"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "plugin::community.post", "properties": {"fields": ["authorUser", "title", "content", "media", "pinned", "blocked", "removed", "likeCount", "board", "likers", "dislikers", "comments", "weight", "adUrl"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "plugin::community.report", "properties": {"fields": ["reporter", "reportType", "related", "reasons", "content", "attachments", "resolved", "resolvedAt"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "plugin::community.report", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "plugin::community.report", "properties": {"fields": ["reporter", "reportType", "related", "reasons", "content", "attachments", "resolved", "resolvedAt"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "plugin::community.report", "properties": {"fields": ["reporter", "reportType", "related", "reasons", "content", "attachments", "resolved", "resolvedAt"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "plugin::notification.message", "properties": {"fields": ["title", "content", "imageUrl", "link", "audienceType", "segments", "status"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "plugin::notification.message", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "plugin::notification.message", "properties": {"fields": ["title", "content", "imageUrl", "link", "audienceType", "segments", "status"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "plugin::notification.message", "properties": {"fields": ["title", "content", "imageUrl", "link", "audienceType", "segments", "status"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.create", "subject": "plugin::notification.notification", "properties": {"fields": ["user", "thumbnail", "thumbnailUrl", "text", "link", "message", "type", "viewed", "audience"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.delete", "subject": "plugin::notification.notification", "properties": {}, "conditions": []}, {"action": "plugin::content-manager.explorer.read", "subject": "plugin::notification.notification", "properties": {"fields": ["user", "thumbnail", "thumbnailUrl", "text", "link", "message", "type", "viewed", "audience"]}, "conditions": []}, {"action": "plugin::content-manager.explorer.update", "subject": "plugin::notification.notification", "properties": {"fields": ["user", "thumbnail", "thumbnailUrl", "text", "link", "message", "type", "viewed", "audience"]}, "conditions": []}, {"action": "plugin::upload.assets.copy-link", "subject": null, "properties": {}, "conditions": []}, {"action": "plugin::upload.assets.create", "subject": null, "properties": {}, "conditions": []}, {"action": "plugin::upload.assets.download", "subject": null, "properties": {}, "conditions": []}, {"action": "plugin::upload.assets.update", "subject": null, "properties": {}, "conditions": []}, {"action": "plugin::upload.configure-view", "subject": null, "properties": {}, "conditions": []}, {"action": "plugin::upload.read", "subject": null, "properties": {}, "conditions": []}]}