module.exports = ({ env }) => [
  "strapi::errors",
  {
    name: "strapi::cors",
    config: {
      headers: [
        "Content-Type",
        "Authorization",
        "Origin",
        "Accept",
        "User-Agent",
        "x-requested-with",
        "x-starcheck-lang",
        "x-starcheck-version",
        "x-starcheck-platform",
        "x-im-token",
        "x-im-platformid",
        "x-starcheck-deviceid",
      ],
    },
  },
  "strapi::poweredBy",
  "strapi::logger",
  "strapi::query",
  "strapi::session",
  "strapi::favicon",
  "strapi::public",
  {
    name: "strapi::security",
    config: {
      contentSecurityPolicy: {
        useDefaults: true,
        directives: {
          "connect-src": ["'self'", "https:"],
          "img-src": ["*"],
          "media-src": ["*"],
          "script-src": [
            "'self'",
            "'unsafe-eval'",
            "unsafe-inline",
            "connect.facebook.net",
          ],
          "object-src": ["'self'", "connect.facebook.net"],
          "frame-src": ["*"],
          upgradeInsecureRequests: null,
        },
      },
    },
  },
  {
    name: "strapi::body",
    config: {
      formLimit: "256mb",
      jsonLimit: "256mb",
      textLimit: "256mb",
      formidable: {
        maxFileSize: 1024 * 1024 * 1024,
      },
    },
  },
];
