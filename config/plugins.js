const { readFileSync } = require("fs");
const path = require("path");

module.exports = ({ env }) => {
  const providerOptions = {
    accessKeyId: env("AWS_ACCESS_KEY_ID"),
    secretAccessKey: env("AWS_ACCESS_SECRET"),
    region: env("AWS_REGION"),
    params: {
      Bucket: env("AWS_BUCKET"),
    },
    endpoint: env("AWS_ENDPOINT"),
    s3ForcePathStyle: env("AWS_S3_FORCE_PATH_STYLE"),
    baseUrl: env("CDN_BASE_URL"),
  };

  if (env("AWS_PROXY")) {
    providerOptions.proxy = env("AWS_PROXY");
  }

  return {
    upload: {
      config: {
        provider:
          "@strapi-community/strapi-provider-upload-google-cloud-storage",
        providerOptions: {
          serviceAccount: env.json("GCS_SERVICE_ACCOUNT"),
          bucketName: env("GCS_BUCKET_NAME"),
          basePath: env("GCS_BASE_PATH"),
          baseUrl: env("GCS_BASE_URL"),
          publicFiles: env("GCS_PUBLIC_FILES"),
          uniform: env("GCS_UNIFORM"),
        },
      },
    },
    redis: {
      config: {
        connections: {
          default: {
            connection: {
              host: env("REDIS_HOST", "127.0.0.1"),
              port: env("REDIS_PORT", 6379),
              db: env("REDIS_DB", 0),
              password: env("REDIS_PASSWORD", ""),
              tls: env.bool("REDIS_SSL", false) && {
                ca: readFileSync(path.join(__dirname, env("REDIS_SSL_CA"))),
              },
            },
          },
          openim: {
            connection: {
              host: env("OPENIM_REDIS_HOST", "127.0.0.1"),
              port: env("OPENIM_REDIS_PORT", 6379),
              db: env("OPENIM_REDIS_DB", 0),
              password: env("OPENIM_REDIS_PASSWORD", ""),
            },
          },
        },
      },
    },
    email: {
      config: {
        provider: "sendgrid",
        providerOptions: {
          apiKey: env("SENDGRID_API_KEY"),
        },
        settings: {
          defaultFrom: env("SENDGRID_FROM"),
          defaultReplyTo: env("SENDGRID_REPLY_TO"),
        },
      },
    },
    meedata: {
      enabled: true,
      resolve: "./src/plugins/strapi-mee-data",
      config: {
        mongodb: {
          host: env("MONGO_HOST"),
          port: env("MONGO_PORT"),
          name: env("MONGO_NAME"),
          user: env("MONGO_USER"),
          pass: env("MONGO_PASS"),
          tls: env.bool("MONGO_SSL", false) && {
            ca: path.join(__dirname, env("MONGO_SSL_CA")),
          },
        },
      },
    },
    mq: {
      enabled: true,
      resolve: "./src/plugins/strapi-mq",
      config: {
        rabbitmq: {
          host: env("RABBITMQ_HOST"),
          user: env("RABBITMQ_USER"),
          pass: env("RABBITMQ_PASS"),
          tls: env.bool("RABBITMQ_SSL", false) && {
            ca: readFileSync(path.join(__dirname, env("RABBITMQ_SSL_CA"))),
          },
        },
      },
    },
    openim: {
      enabled: true,
      resolve: "./src/plugins/strapi-open-im",
      config: {
        host: env("OPEN_IM_HOST"),
        secret: env("OPEN_IM_SECRET"),
        adminUserId: env("OPEN_IM_ADMIN"),
      },
    },
    notification: {
      enabled: true,
      resolve: "./src/plugins/strapi-notification",
      config: {
        appId: env("ONESIGNAL_APPID"),
        appKey: env("ONESIGNAL_APPKEY"),
      },
    },
    manager: {
      enabled: true,
      resolve: "./src/plugins/strapi-manager",
    },
    community: {
      enabled: true,
      resolve: "./src/plugins/strapi-community",
    },
    benefits: {
      enabled: true,
      resolve: "./src/plugins/strapi-benefits",
    },
    "audit-log": {
      enabled: true,
      resolve: "./src/plugins/audit-log",
      config: {
        deletion: {
          enabled: true,
          frequency: "logAge", // "logAge" or "logCount"
          options: {
            value: 2,
            interval: "week", // "day" or "week" or "month" or "year" // Don't add this config property if the frequency is "logCount"
          },
        },
        filters: {
          endpoint: {
            exclude: ["/content-manager/uid", "/admin/renew-token", "/_health"],
          },
          status: {},
          method: {
            exclude: ["OPTION"],
          },
        },
        redactedValues: ["password", "token"],
        pickHeaders: [
          "x-starcheck-lang",
          "x-starcheck-version",
          "x-starcheck-platform",
          "user-agent",
          "x-starcheck-deviceid",
        ],
      },
    },
  };
};
