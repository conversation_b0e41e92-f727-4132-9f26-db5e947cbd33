---
description: 
globs: 
alwaysApply: false
---
# Strapi项目结构

这个项目是基于Strapi构建的后端应用程序，以下是对项目结构的概述：

## 核心目录

- [config/](mdc:config) - 包含Strapi的核心配置文件
  - [database.js](mdc:config/database.js) - 数据库连接配置
  - [server.js](mdc:config/server.js) - 服务器配置
  - [middlewares.js](mdc:config/middlewares.js) - 中间件配置
  - [plugins.js](mdc:config/plugins.js) - 插件配置

## API结构

Strapi API位于 [src/api/](mdc:src/api) 目录，每个API包含以下结构：
- `controllers/` - 处理HTTP请求的控制器
- `services/` - 包含业务逻辑的服务层
- `routes/` - 定义API路由
- `content-types/` - 定义内容模型结构

例如，[posts API](mdc:src/api/posts) 管理帖子相关功能。

## 插件

自定义插件位于 [src/plugins/](mdc:src/plugins) 目录：
- [strapi-community/](mdc:src/plugins/strapi-community) - 社区功能相关插件
- [strapi-benefits/](mdc:src/plugins/strapi-benefits) - 福利功能相关插件
- [strapi-notification/](mdc:src/plugins/strapi-notification) - 通知功能相关插件
- [strapi-mq/](mdc:src/plugins/strapi-mq) - 消息队列相关插件

## 扩展

[src/extensions/](mdc:src/extensions) 目录包含对Strapi核心功能的扩展：
- [users-permissions/](mdc:src/extensions/users-permissions) - 扩展用户权限插件
- [community/](mdc:src/extensions/community) - 扩展社区相关功能
- [benefits/](mdc:src/extensions/benefits) - 扩展福利相关功能

## 数据库

- [database/](mdc:database) - 包含数据库相关文件，如迁移脚本

## 开发和部署

- [ecosystem.config.js](mdc:ecosystem.config.js) - PM2配置文件用于部署
