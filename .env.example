HOST=0.0.0.0
PORT=1337
APP_KEYS="toBeModified1,toBeModified2"
API_TOKEN_SALT=tobemodified
ADMIN_JWT_SECRET=tobemodified
TRANSFER_TOKEN_SALT=
JWT_SECRET=tobemodified
IS_PROD=

#MYSQL
DATABASE_URL=
DATABASE_HOST=
DATABASE_PORT=
DATABASE_NAME=
DATABASE_USERNAME=
DATABASE_PASSWORD=
DATABASE_CHARSET=

#REDIS
REDIS_HOST=
REDIS_PORT=
REDIS_DB=
REDIS_PASSWORD=
REDIS_SSL=
REDIS_SSL_CA=

#OPENIM REDIS
OPENIM_REDIS_HOST=
OPENIM_REDIS_PORT=
OPENIM_REDIS_DB=
OPENIM_REDIS_PASSWORD=

#AWS
AWS_ACCESS_KEY_ID=
AWS_ACCESS_SECRET=
AWS_REGION=
AWS_BUCKET=
CDN_BASE_URL=
CDN_ROOT_PATH=

INIT_ADMIN=
INIT_ADMIN_USERNAME=
INIT_ADMIN_PASSWORD=
INIT_ADMIN_FIRSTNAME=
INIT_ADMIN_LASTNAME=
INIT_ADMIN_EMAIL=

#Sendgrid
SENDGRID_API_KEY=
SENDGRID_FROM=
SENDGRID_REPLY_TO=

#MONGODB
MONGO_HOST=
MONGO_PORT=
MONGO_NAME=
MONGO_USER=
MONGO_PASS=
MONGO_SSL=
MONGO_SSL_CA=

#MQ
RABBITMQ_HOST=
RABBITMQ_USER=
RABBITMQ_PASS=
RABBITMQ_SSL=
RABBITMQ_SSL_CA=

#OPEN IM
OPEN_IM_HOST=
OPEN_IM_SECRET=
OPEN_IM_ADMIN=

#ONESIGNAL
ONESIGNAL_APPID=
ONESIGNAL_APPKEY=

#G
