diff --git a/node_modules/@strapi/plugin-upload/admin/src/components/AssetCard/VideoPreview.js b/node_modules/@strapi/plugin-upload/admin/src/components/AssetCard/VideoPreview.js
index 9e68400..8292f01 100644
--- a/node_modules/@strapi/plugin-upload/admin/src/components/AssetCard/VideoPreview.js
+++ b/node_modules/@strapi/plugin-upload/admin/src/components/AssetCard/VideoPreview.js
@@ -18,7 +18,12 @@ export const VideoPreview = ({ url, mime, onLoadDuration, alt, ...props }) => {
       canvas.getContext('2d').drawImage(video, 0, 0, video.videoWidth, video.videoHeight);
 
       video.replaceWith(canvas);
-      onLoadDuration(video.duration);
+
+      let duration = video.duration;
+      if (duration == 'Infinity')
+        duration = 0;
+
+      onLoadDuration(duration);
     }
   };
 
@@ -47,7 +52,7 @@ export const VideoPreview = ({ url, mime, onLoadDuration, alt, ...props }) => {
 };
 
 VideoPreview.defaultProps = {
-  onLoadDuration() {},
+  onLoadDuration() { },
   size: 'M',
 };
 
