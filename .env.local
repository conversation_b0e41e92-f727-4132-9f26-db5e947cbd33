HOST=0.0.0.0
PORT=5337
APP_KEYS=CM889X5Vs1Y6j6M4jpVWOw==,hAOYjmnQCVX+lp/sP4148g==,d85dxcisUgS7Kd3735/F+Q==,KlpnGUR/KKbK7qi+AJouCg==
API_TOKEN_SALT=5AUh787HwSMScRRzcfiLcw==
ADMIN_JWT_SECRET=H/0HaRv2KdabmBo2rkLIWA==
TRANSFER_TOKEN_SALT=1nAp53rVbEaayN8YKYc5Kg==
JWT_SECRET=t/CYuAvF3QZF2OmThQkFKK==
IS_PROD=false

#AWS代理设置（针对本地开发使用）
AWS_PROXY=http://127.0.0.1:7890

#MYSQL
DATABASE_URL=
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3306
DATABASE_NAME=scheck
DATABASE_USERNAME=root
DATABASE_PASSWORD=rootpassword
DATABASE_CHARSET=utf8mb4

#REDIS
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_SSL=false
REDIS_SSL_CA=

#OPENIM REDIS
OPENIM_REDIS_HOST=127.0.0.1
OPENIM_REDIS_PORT=6379
OPENIM_REDIS_DB=5
OPENIM_REDIS_PASSWORD=

#AWS
AWS_ACCESS_KEY_ID=********************
AWS_ACCESS_SECRET=SdfE3kmQdiNCvhd4EJnrMpQH00cC5UYqtQL+B/fl
AWS_REGION=ap-northeast-2
AWS_BUCKET=an2-dev-atreez
# AWS_BUCKET_URL=an2-dev-atreez.s3.ap-northeast-2.amazonaws.com
CDN_BASE_URL=https://an2-dev-atreez.s3.ap-northeast-2.amazonaws.com
CDN_ROOT_PATH=starcheck

#GCP
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GCS_BUCKET_NAME=starcheck
GCS_BASE_PATH=
GCS_BASE_URL=https://storage.googleapis.com/starcheck
GCS_PUBLIC_FILES=true
GCS_UNIFORM=true

INIT_ADMIN=true
INIT_ADMIN_USERNAME=admin
INIT_ADMIN_PASSWORD=admin123
INIT_ADMIN_FIRSTNAME=Admin
INIT_ADMIN_LASTNAME=Admin
INIT_ADMIN_EMAIL=<EMAIL>

#Sendgrid
SENDGRID_API_KEY=*********************************************************************
SENDGRID_FROM=<EMAIL>
SENDGRID_REPLY_TO=<EMAIL>

#MONGODB
MONGO_HOST=mongodb://127.0.0.1
MONGO_PORT=27017
MONGO_NAME=scheck
MONGO_USER=root
MONGO_PASS=rootpassword
MONGO_SSL=false
MONGO_SSL_CA=

#MQ
RABBITMQ_HOST=127.0.0.1
RABBITMQ_USER=user
RABBITMQ_PASS=wobushiren
RABBITMQ_SSL=false
RABBITMQ_SSL_CA=

#OPEN IM
OPEN_IM_HOST=http://*************:10002
OPEN_IM_SECRET=openIM123
OPEN_IM_ADMIN=imAdmin

#ONESIGNAL
ONESIGNAL_APPID=************************************
ONESIGNAL_APPKEY=os_v2_app_27c3ttdptbdmhigfnnnkxsn7mufh36ylxiauu45vwj3cp44sd4ja56jt5j6glov3roc4va5zefdxrzur6eku62ouiaq6wfefosx4vmi


OPENAI_API_KEY=********************************************************************************************************************************************************************

BFL_API_KEY=103cb8aa-6b62-4eb9-8542-af71f0b08cee
