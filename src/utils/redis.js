// @ts-nocheck
const dayjs = require('dayjs');
const { PRICES } = require("../constants");
const math = require('mathjs');

const incrRecommendUsedCount = async (day, userId) => {
    await strapi.redis.connections.default.client.incr(`RECOMMENDDATION_USED_COUNT:${day}:${userId}`);
}

const getRecommendQuotas = async (day, userId, defaultCount) => {
    //RECOMMENDDATION_USED_COUNT: It records used data
    const usedCount = await strapi.redis.connections.default.client.get(`RECOMMENDDATION_USED_COUNT:${day}:${userId}`) || 0;
    return defaultCount - Number(usedCount);
}

//Deprecated
const decrRecommendQuotas = async (day, userId, defaultCount) => {
    const count = await strapi.redis.connections.default.client.get(`RECOMMEND_LIMIT:${day}:${userId}`);
    if (!count) {
        await strapi.redis.connections.default.client.set(`RECOMMEND_LIMIT:${day}:${userId}`, defaultCount);
    }
    await strapi.redis.connections.default.client.decr(`RECOMMEND_LIMIT:${day}:${userId}`);
}

const getPassedRecommendQuotas = async (userId, days, defaultCount) => {
    const calendarList = Array.from({ length: days }, (_, i) => {
        const date = dayjs().subtract(i + 1, 'day').format('YYYYMMDD');
        return date;
    });

    return await Promise.all(
        calendarList.map(async day => {
            const usedCount = await strapi.redis.connections.default.client.get(`RECOMMENDDATION_USED_COUNT:${day}:${userId}`) || 0;
            return { day, count: defaultCount - Number(usedCount) };
        })
    );
};

const setTotalExpenses = async (userId, totalExpenses) => {
    await strapi.redis.connections.default.client.set(`users:${userId}:totalExpenses`, totalExpenses || 0);
}

const getTotalExpenses = async (userId) => {
    const totalExpenses = await strapi.redis.connections.default.client.get(`users:${userId}:totalExpenses`) || 0;
    return Number(totalExpenses);
}

const setBalance = async (userId, amount) => {
    await strapi.redis.connections.default.client.set(`users:${userId}:balance`, amount || 0);
}

const getBalance = async (userId) => {
    const balance = await strapi.redis.connections.default.client.get(`users:${userId}:balance`) || 0;
    return Number(balance);
}

const incrBalance = async (userId, amount) => {
    if (amount < 0) {
        await strapi.redis.connections.default.client.incrby(`users:${userId}:totalExpenses`, Math.abs(amount));
    }
    return await strapi.redis.connections.default.client.incrby(`users:${userId}:balance`, amount);
}

const addRating = async (rated, rater, score) => {
    await strapi.redis.connections.default.client.zadd(`users:${rated}:ratings`, score, rater);
}

const getRatingData = async (rater, rated) => {
    if (!rater || !rated) {
        return {
            rated: false,
            ratingScore: 0,
            ratingBonus: PRICES.EVALUATE_BONUS
        }
    }
    const res = await strapi.redis.connections.default.client.zscore(`users:${rated}:ratings`, rater);
    return {
        rated: res != null,
        ratingScore: Number(res || 0),
        ratingBonus: PRICES.EVALUATE_BONUS
    }
}

const getAvgRating = async (rated) => {
    try {
        const memberCount = await strapi.redis.connections.default.client.zcard(`users:${rated}:ratings`);
        if (memberCount == 0)
            return 0;
        const result = await strapi.redis.connections.default.client.zrange(`users:${rated}:ratings`, 0, -1, 'WITHSCORES');
        if (result) {
            const scores = result.filter((_, index) => index % 2 !== 0).map(Number);
            const averageScore = math.evaluate(`mean(${scores.join(",")})`).toFixed(1);
            return Number(averageScore);
        }
        return 0;
    } catch (error) {
        console.log(`Failed to calculate the user's average score. id: ${rated}`);
        return 0;
    }
}

const addHeartSubscription = async (userId, subscriptionPlan) => {
    const seconds = subscriptionPlan.duration * 86400;
    await strapi.redis.connections.default.client.setex(`HEART_SUBSCRIPTION:${userId}`, seconds, JSON.stringify(subscriptionPlan));
}

const isSubscribed = async (userId) => {
    const res = await strapi.redis.connections.default.client.exists(`HEART_SUBSCRIPTION:${userId}`);
    return res === 1;
}

const addToBlackList = async (userId, blackUserId) => {
    await strapi.redis.connections.default.client.sadd(`BLACK_LIST:${userId}`, blackUserId);
}

const removeFromBlackList = async (userId, blackUserId) => {
    await strapi.redis.connections.default.client.srem(`BLACK_LIST:${userId}`, blackUserId);
}

const isBlacklisted = async (userId, targetUserId) => {
    const res = await strapi.redis.connections.default.client.sismember(`BLACK_LIST:${userId}`, targetUserId);
    return res === 1;
}

const getBlackList = async (userId) => {
    const res = await strapi.redis.connections.default.client.smembers(`BLACK_LIST:${userId}`);
    return res.map(item => Number(item));
}

const saveUsedReferralCode = async (code, userId) => {
    await strapi.redis.connections.default.client.hset(`USED_REFERRAL_CODES`, code, userId || 0);
}

const checkReferralCodeUsed = async (code) => {
    const res = await strapi.redis.connections.default.client.hexists(`USED_REFERRAL_CODES`, code);
    return res === 1;
}

const getReferralCodeOwner = async (code) => {
    return await strapi.redis.connections.default.client.hget(`USED_REFERRAL_CODES`, code);
}

const addRegisterBonus = async (userId, amount) => {
    await strapi.redis.connections.default.client.hincrby(`REGISTER_BONUS`, userId, amount);
}

const getRegisterBonus = async (userId) => {
    return await strapi.redis.connections.default.client.hget(`REGISTER_BONUS`, userId) || 0;
}

const delRegisterBouns = async (userId) => {
    await strapi.redis.connections.default.client.hdel(`REGISTER_BONUS`, userId);
}

const getHeartReminderCount = async (relatedId) => {
    return await strapi.redis.connections.default.client.get(`GOT_HEART_REMINDER:${relatedId}`) || 0;
}

const incrHeartReminderCount = async (relatedId) => {
    await strapi.redis.connections.default.client.incr(`GOT_HEART_REMINDER:${relatedId}`);
}

const setAppVersion = async (appVersion) => {
    await await strapi.redis.connections.default.client.set("MEETOK:APPVERSION", JSON.stringify(appVersion));
}

const getAppVersion = async () => {
    let res = await strapi.redis.connections.default.client.get("MEETOK:APPVERSION");
    return res ? JSON.parse(res) : {
        android: {
            latestVersion: "1.0.0",
            forceUpdateVersion: "1.0.0"
        },
        ios: {
            latestVersion: "1.0.0",
            forceUpdateVersion: "1.0.0"
        }
    };
}

const addWhitelist = async (userId) => {
    await strapi.redis.connections.default.client.sadd(`MEETOK:WHITELIST`, userId);
}

const isWhitelisted = async (userId) => {
    const res = await strapi.redis.connections.default.client.sismember(`MEETOK:WHITELIST`, userId);
    return res === 1;
}

const removeWhitelist = async (userIds) => {
    await strapi.redis.connections.default.client.srem(`MEETOK:WHITELIST`, userIds);
}

const saveRejectionReason = async (userId, data = { en: '', vi: '' }) => {
    await strapi.redis.connections.default.client.set(`REJECTION_REASON:${userId}`, JSON.stringify(data));
}

const getRejectionReason = async (userId, locale) => {
    const res = await strapi.redis.connections.default.client.get(`REJECTION_REASON:${userId}`);
    const result = res ? JSON.parse(res) : { en: '', vi: '' };
    return locale ? result[locale] : result;
}

const incrRatingCount = async (userId) => {
    return await strapi.redis.connections.default.client.hincrby(`USER_RATING_COUNT`, userId, 1);
}

const clearRatingCount = async (userId) => {
    return await strapi.redis.connections.default.client.hset(`USER_RATING_COUNT`, userId, 0);
}

/**
 * score = timestamp
 * @param {*} userId 
 * @param {*} params  [score1, 'user1', score2, 'user2']
 */
const addUserRecommendations = async (userId, params) => {
    await strapi.redis.connections.default.client.zadd(`users:${userId}:recommendations`, params);
}

const getUserRecommendations = async (userId) => {
    const result = await strapi.redis.connections.default.client.zrange(`users:${userId}:recommendations`, 0, -1) || [];
    return result.map(Number);
}


const updateSettings = async (key, value) => {
    try {
        await strapi.redis.connections.default.client.hset(`SETTINGS`, key, JSON.stringify(value));
    } catch (error) {
        strapi.log.info('Failed to synchronize settings information into the redis.')
    }
}

const getSettings = async (key) => {
    const res = await strapi.redis.connections.default.client.hget(`SETTINGS`, key);
    return res ? JSON.parse(res) : null;
}

module.exports = {
    incrRecommendUsedCount,
    getRecommendQuotas,
    decrRecommendQuotas,
    getPassedRecommendQuotas,
    setBalance,
    getBalance,
    incrBalance,
    addRating,
    getRatingData,
    getAvgRating,
    addHeartSubscription,
    isSubscribed,
    addToBlackList,
    removeFromBlackList,
    getBlackList,
    isBlacklisted,
    saveUsedReferralCode,
    checkReferralCodeUsed,
    getReferralCodeOwner,
    addRegisterBonus,
    getRegisterBonus,
    delRegisterBouns,
    getHeartReminderCount,
    incrHeartReminderCount,
    setAppVersion,
    getAppVersion,
    addWhitelist,
    isWhitelisted,
    removeWhitelist,
    saveRejectionReason,
    getRejectionReason,
    incrRatingCount,
    clearRatingCount,
    addUserRecommendations,
    getUserRecommendations,
    updateSettings,
    getSettings,
    setTotalExpenses,
    getTotalExpenses
}