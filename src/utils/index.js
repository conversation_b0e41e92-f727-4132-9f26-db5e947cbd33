// @ts-nocheck
const { PhoneNumberUtil, PhoneNumberFormat } = require('google-libphonenumber');
const { customAlphabet } = require('nanoid');
const { saveUsedReferralCode, checkReferralCodeUsed } = require('./redis');
const _ = require('lodash');


const generateReferralCode = async () => {
    const nanoid = customAlphabet('23456789ABCDEFGHJKMNPQRSTUVWXYZ', 5);
    while (true) {
        const code = nanoid();
        const used = await checkReferralCodeUsed(code);
        if (!used) {
            await saveUsedReferralCode(code);
            return code;
        }
    }
}

const getShuffledArr = (arr) => {
    return arr.reduce(
        (newArr, _, i) => {
            var rand = i + (Math.floor(Math.random() * (newArr.length - i)));
            [newArr[rand], newArr[i]] = [newArr[i], newArr[rand]]
            return newArr
        }, [...arr]
    )
}

const formatPhoneNumber = (countryCode, phone) => {
    const phoneUtil = PhoneNumberUtil.getInstance();
    const number = `+${countryCode}${phone}`;
    const parsedNumber = phoneUtil.parse(number);
    const isValid = phoneUtil.isValidNumber(parsedNumber);
    let result = {
        isValid
    }
    if (isValid) {
        let phoneNumber = phoneUtil.format(parsedNumber, PhoneNumberFormat.NATIONAL);
        phoneNumber = phoneNumber.replace(/[() -]/g, '');
        result = {
            ...result,
            phoneCountryCode: String(parsedNumber.getCountryCode()),
            phoneNumber
        }
    }
    return result;
}

const getErrorMessage = (errorCode, locale) => {
    return strapi.messages[locale || 'en'][errorCode] || 'Woops! Something went wrong. Please, try again.';
}

const checkObjectKeys = (obj, keys) => {
    return keys.every(key => {
        const value = _.get(obj, key);
        return value !== null && (!Array.isArray(value) || value.length > 0) && !(_.isString(value) && _.trim(value) === '');
    });
}

module.exports = {
    getShuffledArr,
    formatPhoneNumber,
    generateReferralCode,
    getErrorMessage,
    checkObjectKeys
};