const { isArray } = require('lodash/fp');
const _ = require('lodash');

const sanitizeSubscriptionPlan = async (plans, local) => {
    if (!local)
        return plans;

    return await Promise.all(plans.map(plan => {
        plan.description = plan.locales?.[local] || plan.description;
        delete plan.locales
        return plan;
    }))
}

const sanitizeLocale = async (data, locale) => {
    if (!locale) return data;
    if (isArray(data)) {
        return Promise.all(data.map(entry => sanitizeLocale(entry, locale)));
    }
    data.name = data.locales?.[locale] || data.name;
    delete data.locales;
    return data;
}

const sanitizeUserLocale = async (user, locale) => {
    if (!locale) return user;
    if (user.university) {
        user.university = await sanitizeLocale(user.university, locale);
    }
    if (user.interests) {
        user.interests = await sanitizeLocale(user.interests, locale);
    }

    // Ensure certificateList is an array
    if (user.certificate || user.certificateList) {
        user.certificateList = Array.isArray(user.certificateList) ? user.certificateList : [];

        // Add the certificate if the user has one and it doesn't exist in the list
        if (user.certificate && user.certificate.id) {
            const certificateExists = user.certificateList.some(certificate => certificate.id === user.certificate.id);
            if (!certificateExists) {
                user.certificateList.push(user.certificate);
            }
        }
    }
    return _.omit(user, ['password', 'resetPasswordToken', 'confirmationToken']);
}

module.exports = {
    sanitizeSubscriptionPlan,
    sanitizeLocale,
    sanitizeUserLocale
};