const { SNSClient, PublishCommand } = require("@aws-sdk/client-sns");

const clientConfig = {
    region: 'ap-southeast-1',
    credentials: {
        accessKeyId: '********************',
        secretAccessKey: 'BPsclDRRxornSfAxgZaqo6jUixDW8aelZuYOsAd2'
    }
};

const snsClient = new SNSClient(clientConfig);

async function sendSMS(contryCode, phoneNumber, message) {
    const params = {
        Message: message,
        PhoneNumber: `+${contryCode}${phoneNumber}`
    };
    const command = new PublishCommand(params);

    try {
        const response = await snsClient.send(command);
        console.log(response);
        return response.MessageId;
    } catch (err) {
        throw err;
    }
}

module.exports = sendSMS;