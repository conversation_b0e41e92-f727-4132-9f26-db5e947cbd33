export default {
  routes: [
    {
      method: "GET",
      path: "/star-events/sections",
      handler: "api::star-event.star-event.getEventSections",
      config: {
        // auth: false,
      },
    },
    {
      method: "GET",
      path: "/star-events",
      handler: "api::star-event.star-event.getEvents",
      config: {
        // auth: false,
      },
    },
    {
      method: "GET",
      path: "/star-events/my-past",
      handler: "api::star-event.star-event.getMyPastEvents",
      config: {
        // auth: false,
      },
    }
  ],
};
