/**
 * star-event controller
 */

import { factories } from "@strapi/strapi";
import { getErrorMessage } from "../../../utils";
import { StarEventMemberStatus } from "../../star-event-member/common";
import { StarEventType } from "../common";
const utils = require("@strapi/utils");
const { ValidationError, ForbiddenError } = utils.errors;

export default factories.createCoreController(
  "api::star-event.star-event",
  ({ strapi }) => ({
    async findOne(ctx) {
      try {
        const { id } = ctx.params;
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        if (!id) {
          throw new ValidationError(getErrorMessage("error.validated", locale));
        }
        const eventService: any = strapi.service("api::star-event.star-event");
        var event = await eventService.findOneWith({
          eventId: id,
          userId: authUser?.id,
        });
        return event;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },

    async getEventSections(ctx) {
      try {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        if (!authUser) {
          throw new ForbiddenError(getErrorMessage("forbidden", locale));
        }

        const result = { upcoming: [], featured: [] };
        var memberService: any = strapi.service(
          "api::star-event-member.star-event-member"
        );
        var commentService: any = strapi.service(
          "api::star-comment.star-comment"
        );
        var applied = await memberService.findWith({
          userId: authUser?.id,
        });
        applied = await Promise.all(
          applied.map(async (item: any) => {
            const event = item.event;
            const count = await memberService.countWith({
              eventId: event.id,
              status: [StarEventMemberStatus.JOINED],
            });
            const commentCount = await commentService.countWithEvent(event.id);
            event.extra = {
              status: item.status,
              membersCount: count,
              commentsCount: commentCount,
            };
            return event;
          })
        );

        const eventService: any = strapi.service("api::star-event.star-event");
        var featured = await eventService.findWith({
          future: true,
          type: StarEventType.NORMAL,
          userId: authUser?.id,
        });

        featured = featured.filter((event) => {
          return !applied.find((joinedEvent) => joinedEvent.id === event.id);
        });

        applied = applied.filter((event: any) => {
          const now = new Date();
          const startDate = new Date(event.startDate);
          return (
            // event.type === StarEventType.NORMAL &&
            startDate > now
            // && startDate < new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000)
          );
        });

        result.upcoming = applied;
        result.featured = featured;
        return result;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },

    async getEvents(ctx) {
      try {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const {
          limit = 10,
          offset = 0,
          exclude,
          type = StarEventType.NORMAL,
          futureLimited = "true",
          pastLimited = "false",
        } = ctx.query;

        if (!authUser) {
          throw new ForbiddenError(getErrorMessage("forbidden", locale));
        }

        const eventService: any = strapi.service("api::star-event.star-event");
        var featured = await eventService.findWith({
          future: futureLimited == "true",
          past: pastLimited == "true",
          type:
            type == StarEventType.NORMAL || type == StarEventType.SPECIAL_GUEST
              ? type
              : undefined,
          userId: authUser?.id,
          limit,
          offset,
          excludeIds: exclude ? [exclude] : [],
        });

        return featured;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },

    async getMyPastEvents(ctx) {
      try {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        if (!authUser) {
          throw new ForbiddenError(getErrorMessage("forbidden", locale));
        }
        var memberService: any = strapi.service(
          "api::star-event-member.star-event-member"
        );
        var commentService: any = strapi.service(
          "api::star-comment.star-comment"
        );
        var applied = await memberService.findWith({
          userId: authUser?.id,
        });
        applied = await Promise.all(
          applied.map(async (item: any) => {
            const event = item.event;
            const count = await memberService.countWith({
              eventId: event.id,
              status: [StarEventMemberStatus.JOINED],
            });
            const commentCount = await commentService.countWithEvent(event.id);
            event.extra = {
              status: item.status,
              membersCount: count,
              commentsCount: commentCount,
            };
            return event;
          })
        );
        applied = applied.filter((event: any) => {
          const now = new Date();
          const startDate = new Date(event.startDate);
          return (
            // event.type === StarEventType.NORMAL &&
            startDate < now
            // && startDate > new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
          );
        });

        return applied;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },
  })
);
