import { factories } from "@strapi/strapi";
import { StarEventMemberStatus } from "../../star-event-member/common";

export default factories.createCoreService(
  "api::star-event.star-event",
  ({ strapi }) => ({
    async findOneWith({ eventId, userId }) {
      if (!eventId) {
        throw new Error("eventId is required");
      }
      const event = await strapi.entityService.findOne(
        "api::star-event.star-event",
        eventId,
        {
          populate: {
            photos: { fields: ["url", "mime"] },
            star: {
              fields: [
                "username",
                "email",
                "level",
                "introduction",
                "title",
                "fullname",
              ],
              populate: {
                photos: { fields: ["url", "mime"] },
                avatar: {
                  fields: ["url", "mime"],
                },
              },
            },
          },
        }
      );
      const memberService: any = strapi.service(
        "api::star-event-member.star-event-member"
      );
      const count = await memberService.countWith({
        eventId: event.id,
        status: [StarEventMemberStatus.JOINED],
      });

      const commentService: any = strapi.service(
        "api::star-comment.star-comment"
      );
      const commentCount = await commentService.countWithEvent(event.id);

      event.extra = { membersCount: count, commentsCount: commentCount };
      if (userId) {
        const member = await memberService.findWith({
          userId: userId,
          eventId: event.id,
        });
        if (member.length > 0) {
          event.extra.status = member[0].status;
        } else {
          event.extra.status = StarEventMemberStatus.NONE;
        }
      }
      return event;
    },

    async findWith({
      future = false,
      past = false,
      type,
      userId,
      offset = 0,
      limit = 1000,
      excludeIds,
    }) {
      var filters: any = {};
      if (future && !past) {
        filters.startDate = {
          $gt: new Date(),
        };
      }
      if (past && !future) {
        filters.startDate = {
          $lt: new Date(),
        };
      }
      if (type) {
        filters.type = type;
      }
      if (excludeIds && excludeIds.length > 0) {
        filters.id = {
          $notIn: excludeIds,
        };
      }

      var items = await strapi.entityService.findMany(
        "api::star-event.star-event",
        {
          filters: filters,
          populate: {
            photos: { fields: ["url", "mime"] },
            star: {
              fields: [
                "username",
                "email",
                "level",
                "introduction",
                "title",
                "fullname",
              ],
              populate: {
                photos: { fields: ["url", "mime"] },
                avatar: {
                  fields: ["url", "mime"],
                },
              },
            },
          },
          sort: { startDate: "asc" },
          start: offset,
          limit: limit,
        }
      );

      const memberService: any = strapi.service(
        "api::star-event-member.star-event-member"
      );
      const commentService: any = strapi.service(
        "api::star-comment.star-comment"
      );
      items = await Promise.all(
        items.map(async (event: any) => {
          const count = await memberService.countWith({
            eventId: event.id,
            status: [StarEventMemberStatus.JOINED],
          });
          const commentCount = await commentService.countWithEvent(event.id);
          event.extra = { membersCount: count, commentsCount: commentCount };
          if (userId) {
            const member = await memberService.findWith({
              userId: userId,
              eventId: event.id,
            });
            if (member.length > 0) {
              event.extra.status = member[0].status;
            } else {
              event.extra.status = StarEventMemberStatus.NONE;
            }
          }
          return event;
        })
      );
      return items;
    },
  })
);
