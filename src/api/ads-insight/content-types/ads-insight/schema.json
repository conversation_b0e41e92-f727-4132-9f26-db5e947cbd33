{"kind": "collectionType", "collectionName": "ads_insights", "info": {"singularName": "ads-insight", "pluralName": "ads-insights", "displayName": "AdsInsight", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "admin::user"}, "ads": {"type": "relation", "relation": "oneToOne", "target": "plugin::community.post"}}}