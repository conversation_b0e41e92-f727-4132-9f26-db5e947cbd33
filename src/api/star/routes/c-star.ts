/**
 * star router
 */

// 合并默认路由和自定义路由
export default {
  routes: [
    {
      method: "GET",
      path: "/stars/current",
      handler: "api::star.star.findCurrent",
      config: {
        // auth: false, // 设置为true如果需要认证
      },
    },
    {
      method: "GET",
      path: "/stars/past",
      handler: "api::star.star.findPast",
      config: {
        // auth: false, // 设置为true如果需要认证
      },
    },
  ],
};
