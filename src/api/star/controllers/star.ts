/**
 * star controller
 */
// @ts-ignore
import { factories } from "@strapi/strapi";
import { StarEventType } from "../../star-event/common";

export default factories.createCoreController(
  "api::star.star",
  ({ strapi }) => ({
    async findCurrent(ctx) {
      try {
        // 获取当前日期
        const authUser = ctx.state.user;

        const populate = {
          previewImage: {
            fields: ["url", "id", "formats"],
          },
          event: {
            fields: ["title"],
          },
        };
        // 查询playingDate已到达且是最近的项目
        const result = {
          last: null,
          current: null,
          next: null,
          normalEvents: null,
        };
        const lteList = await strapi.entityService.findMany("api::star.star", {
          filters: {
            playingDate: {
              $lte: new Date(),
            },
          },
          populate: populate,
          sort: { playingDate: "desc" },
          limit: 2,
        });

        const gtList = await strapi.entityService.findMany("api::star.star", {
          filters: {
            playingDate: {
              $gt: new Date(),
            },
          },
          populate: populate,
          sort: { playingDate: "asc" },
          limit: 1,
        });

        const eventService: any = strapi.service("api::star-event.star-event");

        if (lteList && lteList.length > 0) {
          const current = lteList[0] as any;
          if (current && current.event) {
            const eventId = current.event.id;
            var event = await eventService.findOneWith({
              eventId: eventId,
              userId: authUser?.id,
            });
            current.event = event;

            var commentsService: any = strapi.service(
              "api::star-comment.star-comment"
            );
            const comments = await commentsService.findWith({
              eventId: eventId,
              offset: 0,
              limit: 5,
            });

            current.event.comments = comments;
            result.current = current;
            if (gtList && gtList.length > 0) {
              var next = gtList[0];
              const eventId = next.event.id;
              var event = await eventService.findOneWith({
                eventId: eventId,
                userId: authUser?.id,
              });
              next.event = event;
              result.next = next;
            }
            if (lteList.length > 1) {
              var last = lteList[1];
              const eventId = last.event.id;
              var event = await eventService.findOneWith({
                eventId: eventId,
                userId: authUser?.id,
              });
              last.event = event;
              result.last = last;
            }

            var filters: any = {
              future: true,
              type: StarEventType.NORMAL,
              limit: 5,
            };
            if (authUser) {
              filters.userId = authUser.id;
            }
            const normalEvents = await eventService.findWith(filters);
            result.normalEvents = normalEvents;
            return result;
          } else {
            return ctx.notFound("Star found but no event data");
          }
        } else {
          return ctx.notFound("No stars");
        }
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },

    async findPast(ctx) {
      try {
        // 获取当前日期
        const authUser = ctx.state.user;
        const { limit = "10", offset = "0" } = ctx.query;
        const populate = {
          event: {
            fields: ["title"],
          },
        };
        // 查询playingDate已到达且是最近的项目
        const lteList: Array<any> = await strapi.entityService.findMany(
          "api::star.star",
          {
            filters: {
              playingDate: {
                $lte: new Date(),
              },
            },
            populate: populate,
            sort: { playingDate: "desc" },
            start: parseInt(offset) + 1,
            limit: parseInt(limit),
          }
        );

        const eventService: any = strapi.service("api::star-event.star-event");
        if (lteList && lteList.length > 0) {
          await Promise.all(
            lteList.map(async (item: any) => {
              if (item && item.event) {
                const eventId = item.event.id;
                var event = await eventService.findOneWith({
                  eventId: eventId,
                  userId: authUser?.id,
                });
                item.event = event;
              }
              return item;
            })
          );
          return lteList;

          // lteList.forEach(async (item: any) => {
          //   if (item && item.event) {
          //     const eventId = item.event.id;
          //     var event = await eventService.findOneWith({
          //       eventId: eventId,
          //       userId: authUser?.id,
          //     });
          //     item.event = event;
          //   }
          // });
        }
        return lteList;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },
  })
);
