// @ts-nocheck
"use strict";

const path = require("path");
const { google } = require("googleapis");

module.exports = {
  async hello(ctx) {
    const version = ctx.request.header["x-starcheck-version"] || "1.0.0";
    const platform = ctx.request.header["x-starcheck-platform"] || "android";
    ctx.state.version = version;
    ctx.state.platform = platform;
    const service = await strapi
      .plugin("manager")
      .controller("version")
      .getServiceVersion(ctx);
    return { ok: true, service };
  },

  async geocode(ctx) {
    const { latitude, longitude, language = "en" } = ctx.query;
    return await strapi
      .service("api::third-party.third-party")
      .geocode(latitude, longitude, language);
  },

  async inappproducts(ctx) {
    const auth = new google.auth.GoogleAuth({
      keyFile: path.join(__dirname, "meetok-396106-47c8a29d989c.json"),
      scopes: ["https://www.googleapis.com/auth/androidpublisher"],
    });
    const client = await auth.getClient();
    const publisher = google.androidpublisher({
      version: "v3",
      auth: client,
    });
    const { data } = await publisher.inappproducts.list({
      packageName: "me.meetok.app",
    });
    return data;
  },
};
