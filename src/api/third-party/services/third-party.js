// @ts-nocheck
'use strict';
const axios = require("axios");

module.exports = {
    async geocode(latitude, longitude, language) {
        const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=AIzaSyAgVU4rF9ZD5cZkL7yY06ZVn0WEnvRtXUw&language=${language}`;
        const { data } = await axios.get(url);
        return data;
    },

    async appVerify(receipt, type) {
        const url = type == 0 ? "https://sandbox.itunes.apple.com/verifyReceipt" : "https://buy.itunes.apple.com/verifyReceipt";
        const config = {
            headers: {
                'Content-Type': 'application/json;charset=UTF-8'
            },
            timeout: 5000
        };
        const body = { "receipt-data": receipt }
        const { data } = await axios.post(url, body, config);
        return data;
    },

    async sendSMS(countryCode, recipientNo, text) {
        const url = 'https://api-sms.cloud.toast.com/sms/v3.0/appKeys/Bgo8y9eT2DFuEh3q/sender/sms';
        const config = {
            headers: {
                'Content-Type': 'application/json;charset=UTF-8',
                'X-Secret-Key': 'bBZDa24Q'
            },
            timeout: 5000
        };
        const body = {
            body: text,
            sendNo: '01084599727',
            recipientList: [
                {
                    countryCode,
                    recipientNo,
                    internationalRecipientNo: `${countryCode}${recipientNo}`
                }
            ]
        }
        console.log(body);
        const { data } = await axios.post(url, body, config);
        console.log(JSON.stringify(data));
        return data;
    },

}
