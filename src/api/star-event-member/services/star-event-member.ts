/**
 * star-event-member service
 */

import { factories } from "@strapi/strapi";

export default factories.createCoreService(
  "api::star-event-member.star-event-member",
  ({ strapi }) => ({
    async findWith({
      userId,
      eventId,
      status = Array<string>(),
      offset = 0,
      limit = 10000,
    }) {
      var filters: any = {};
      if (!userId && !eventId) {
        throw new Error("userId or eventId is required");
      }
      if (userId) {
        filters.user = userId;
      }
      if (eventId) {
        filters.event = eventId;
      }
      if (status.length > 0) {
        filters.status = {
          $in: status,
        };
      }

      const eventMember = await strapi.entityService.findMany(
        "api::star-event-member.star-event-member",
        {
          populate: {
            user: {
              fields: [
                "username",
                "email",
                "level",
                "introduction",
                "title",
                "fullname",
              ],
              populate: {
                // photos: { fields: ["url"] },
                avatar: {
                  fields: ["url", "mime"],
                },
              },
            },
            event: {
              fields: ["title", "address", "maxMembers", "startDate"],
              populate: {
                star: {
                  fields: [
                    "username",
                    "email",
                    "level",
                    "introduction",
                    "title",
                    "fullname",
                  ],
                  populate: {
                    photos: { fields: ["url", "mime"] },
                    avatar: {
                      fields: ["url", "mime"],
                    },
                  },
                },
                photos: { fields: ["url", "mime"] },
              },
            },
          },
          filters: filters,
          limit: limit,
          start: offset,
        }
      );
      return eventMember;
    },

    async countWith({ userId, eventId, status = Array<string>() }) {
      var filters: any = {};
      if (!userId && !eventId) {
        throw new Error("userId or eventId is required");
      }
      if (userId) {
        filters.user = userId;
      }
      if (eventId) {
        filters.event = eventId;
      }
      if (status.length > 0) {
        filters.status = {
          $in: status,
        };
      }

      const eventMember = await strapi.entityService.findMany(
        "api::star-event-member.star-event-member",
        {
          filters: filters,
        }
      );
      return eventMember.length;
    },

    async create({ userId, eventId, status }) {
      const eventMember = await strapi.entityService.create(
        "api::star-event-member.star-event-member",
        {
          data: {
            user: userId,
            event: eventId,
            status: status,
          },
        }
      );
      return eventMember;
    },
  })
);
