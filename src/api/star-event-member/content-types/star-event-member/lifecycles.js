module.exports = {
  async beforeCreate(event) {
    const { data } = event.params;

    // 检查是否已存在相同的用户和活动组合
    const existingEntry = await strapi.db
      .query("api::star-event-member.star-event-member")
      .findOne({
        where: {
          user: data.user,
          event: data.event,
        },
      });

    if (existingEntry) {
      throw new Error("You are already a member of this event");
    }
  },

  async afterCreate(event) {},
};
