{"kind": "collectionType", "collectionName": "star_event_members", "info": {"singularName": "star-event-member", "pluralName": "star-event-members", "displayName": "StarEventMember", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "event": {"type": "relation", "relation": "oneToOne", "target": "api::star-event.star-event"}, "status": {"type": "enumeration", "enum": ["applied", "joined", "rejected"], "default": "applied", "required": true}}}