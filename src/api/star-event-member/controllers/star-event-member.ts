/**
 * star-event-member controller
 */

import { factories } from "@strapi/strapi";
import { StarEventMemberStatus } from "../common";
import { StarEventType } from "../../star-event/common";
import { getErrorMessage } from "../../../utils";
const utils = require("@strapi/utils");
const { ValidationError, ForbiddenError } = utils.errors;

export default factories.createCoreController(
  "api::star-event-member.star-event-member",
  ({ strapi }) => ({
    async joinEvent(ctx) {
      try {
        const { eventId } = ctx.params;
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;

        const memberService: any = strapi.service(
          "api::star-event-member.star-event-member"
        );
        const member = await memberService.findWith({
          userId: authUser?.id,
          eventId: eventId,
        });

        const eventService: any = strapi.service("api::star-event.star-event");
        const event = await eventService.findOneWith({
          eventId: eventId,
          userId: authUser?.id,
        });

        if (member.length > 0) {
          return event;
        } else {
          if (event.extra.membersCount >= event.maxMembers) {
            throw new ValidationError(
              getErrorMessage("up.to.max.members", locale)
            );
          }
          await memberService.create({
            userId: authUser?.id,
            eventId: eventId,
            status:
              event.type === StarEventType.NORMAL
                ? StarEventMemberStatus.JOINED
                : StarEventMemberStatus.APPLIED,
          });
          return await eventService.findOneWith({
            eventId: eventId,
            userId: authUser?.id,
          });
        }
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },

    async getMembers(ctx) {
      try {
        const { eventId } = ctx.params;
        const { limit = 10, offset = 0 } = ctx.query;

        const memberService: any = strapi.service(
          "api::star-event-member.star-event-member"
        );
        var members = await memberService.findWith({
          eventId: eventId,
          status: [StarEventMemberStatus.JOINED],
          limit,
          offset,
        });
        members = members.map((member) => {
          return member.user;
        });
        return members;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },
  })
);
