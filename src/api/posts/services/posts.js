"use strict";

module.exports = {
  postsReport: async () => {
    try {
      // Function to select a random item from a list of items with weights
      function weightedRandomSelection(items, weights) {
        let totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
        let randomWeight = Math.random() * totalWeight;
        let currentWeight = 0;

        for (let i = 0; i < items.length; i++) {
          currentWeight += weights[i];
          if (currentWeight >= randomWeight) {
            return items[i];
          }
        }

        // If no item is selected due to rounding errors, return the last item
        return items[items.length - 1];
      }

      // Get existing articles
      const ctx = strapi.requestContext.get();

      const existingArticles = ctx.request.body["ids"];

      // Get Articles
      const articles = await strapi.entityService.findMany(
        "plugin::community.post",
        {
          filters: {
            id: {
              $notIn: existingArticles,
            },
            board: {
              id: {
                $not: 139,
              },
            },
          },
          populate: {
            board: { fields: ["id", "name"] },
            media: { fields: ["url", "ext", "mime", "formats", "size"] },
            authorUser: {
              fields: [
                "id",
                "fakeName",
                "fakeRandomAvatar",
                "level",
                "regionCode",
                "financialStatus",
                "verificationStatus",
                "status",
              ],
              populate: {
                fakeAvatar: {
                  fields: ["id", "url"],
                },
              },
            },
          },
          limit: 3,
        }
      );

      // Get Advertisements
      const advertisements = await strapi.entityService.findMany(
        "plugin::community.post",
        {
          filters: {
            id: {
              $notIn: existingArticles,
            },
            board: {
              id: 139,
            },
          },
          populate: {
            board: { fields: ["id", "name"] },
            media: { fields: ["url", "ext", "mime", "formats", "size"] },
            authorUser: {
              fields: [
                "id",
                "fakeName",
                "fakeRandomAvatar",
                "level",
                "regionCode",
                "financialStatus",
                "verificationStatus",
                "status",
              ],
              populate: {
                fakeAvatar: {
                  fields: ["id", "url"],
                },
              },
            },
          },
        }
      );

      // Get random advertisement
      const weights = advertisements.map((ad) => ad.weight);
      const randomAd = weightedRandomSelection(advertisements, weights);

      return [...articles, ...(randomAd ? [randomAd] : [])];
    } catch (err) {
      return err;
    }
  },
};
