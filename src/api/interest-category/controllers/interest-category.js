// @ts-nocheck
'use strict';

/**
 * interest-category controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::interest-category.interest-category', ({ strapi }) => ({
    async find(ctx) {
        const locale = ctx.state.locale;
        const entries = await strapi.db.query('api::interest-category.interest-category').findMany({
            select: ["id", "name", "locales"],
            orderBy: { order: 'asc' },
            populate: {
                interests: {
                    select: ["id", "name", "tag", "locales", "source"],
                    orderBy: { order: 'asc' }
                }
            },
        });
        return entries.map(cate => {
            return {
                id: cate.id,
                name: cate.locales?.[locale] || cate.name,
                interests: cate.interests.map(interest => {
                    return {
                        id: interest.id,
                        name: interest.locales?.[locale] || interest.name,
                        tag: interest.tag,
                        source: interest.source
                    }
                }),
            }
        });
    }
}));
