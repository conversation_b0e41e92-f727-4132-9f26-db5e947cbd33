{"kind": "collectionType", "collectionName": "interest_categories", "info": {"singularName": "interest-category", "pluralName": "interest-categories", "displayName": "InterestCategory"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "order": {"type": "integer"}, "locales": {"type": "json"}, "interests": {"type": "relation", "relation": "oneToMany", "target": "api::interest.interest", "mappedBy": "category"}}}