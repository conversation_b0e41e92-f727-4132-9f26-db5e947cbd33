'use strict';

/**
 * university service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::university.university', ({ strapi }) => ({
    async getVailable(data) {
        const entry = await strapi.db.query('api::university.university').findOne({
            where: { id: data.id }
        });
        if (entry) return entry;

        return await strapi.db.query('api::university.university').create({
            data: {
                name: data.name,
                rank: 999,
                source: 'custom',
                emailSuffixes: [],
                locales: {
                    en: data.name,
                    vi: data.name
                }
            }
        });
    },
}));
