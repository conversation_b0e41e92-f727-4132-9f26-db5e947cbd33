{"kind": "collectionType", "collectionName": "universities", "info": {"singularName": "university", "pluralName": "universities", "displayName": "University", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "location": {"type": "string"}, "regionCode": {"type": "string"}, "major": {"type": "customField", "options": ["Law", "Economics", "Technology", "Education", "Media", "PR", "Marketing", "Architecture", "Medical", "Art"], "customField": "plugin::multi-select.multi-select"}, "emailSuffix": {"type": "string"}, "emailRegex": {"type": "string"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "rank": {"type": "integer"}, "website": {"type": "string"}, "users": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user", "mappedBy": "university"}, "emailSuffixes": {"type": "json"}, "locales": {"type": "json"}, "source": {"type": "enumeration", "enum": ["vietnam", "hongkong", "singapore", "japan", "taiwan", "korea", "malaysia", "indonesia", "thailand", "philippines", "global", "custom"]}, "blocked": {"type": "boolean", "default": false}}}