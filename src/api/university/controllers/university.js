// @ts-nocheck
'use strict';

/**
 * university controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const { sanitizeLocale } = require('../../../utils/locale');

module.exports = createCoreController('api::university.university', ({ strapi }) => ({
    async find(ctx) {
        const locale = ctx.state.locale;
        const entries = await strapi.db.query('api::university.university').findMany({
            where: {
                blocked: false,
                source: { $ne: 'custom' }
            },
            populate: {
                logo: {
                    select: ['url']
                }
            },
            orderBy: { id: 'asc' }
        });
        return await sanitizeLocale(entries, locale);
    },

    async findOne(ctx) {
        const { id } = ctx.request.params;
        return await strapi.db.query('api::university.university').findOne({
            where: { id: id },
            populate: {
                logo: {
                    select: ['url']
                }
            }
        });
    },

    async getRegions(ctx) {
        const locale = ctx.state.locale;
        const regions = [
            {
                name: 'Hong Kong',
                regionCode: 'HK',
                source: 'hongkong',
                locales: {
                    en: 'Hong Kong',
                    vi: 'Hồng Kông'
                },
                universities: []
            },
            {
                name: 'Indonesia',
                source: 'indonesia',
                regionCode: 'ID',
                locales: {
                    en: 'Indonesia',
                    vi: 'Indonesia'
                },
                universities: []
            },
            {
                name: 'Japan',
                source: 'japan',
                regionCode: 'JP',
                locales: {
                    en: 'Japan',
                    vi: 'Nhật Bản'
                },
                universities: []
            },
            {
                name: 'Korea',
                source: 'korea',
                regionCode: 'KR',
                locales: {
                    en: 'Korea',
                    vi: 'Hàn Quốc'
                },
                universities: []
            },
            {
                name: 'Malaysia',
                source: 'malaysia',
                regionCode: 'MY',
                locales: {
                    en: 'Malaysia',
                    vi: 'Malaysia'
                },
                universities: []
            },
            {
                name: 'Philippines',
                source: 'philippines',
                regionCode: 'PH',
                locales: {
                    en: 'Philippines',
                    vi: 'Philippines'
                },
                universities: []
            },
            {
                name: 'Singapore',
                source: 'singapore',
                regionCode: 'SG',
                locales: {
                    en: 'Singapore',
                    vi: 'Singapore'
                },
                universities: []
            },
            {
                name: 'Taiwan',
                source: 'taiwan',
                regionCode: 'TW',
                locales: {
                    en: 'Taiwan',
                    vi: 'Đài Loan'
                },
                universities: []
            },
            {
                name: 'Thailand',
                source: 'thailand',
                regionCode: 'TH',
                locales: {
                    en: 'Thailand',
                    vi: 'Thái Lan'
                },
                universities: []
            },
            {
                name: 'Vietnam',
                source: 'vietnam',
                regionCode: 'VN',
                locales: {
                    en: 'Vietnam',
                    vi: 'Việt Nam'
                },
                universities: []
            },
            {
                name: 'Global 200',
                source: 'global',
                regionCode: 'GLOBAL',
                locales: {
                    en: 'Global 200',
                    vi: 'Toàn cầu 200'
                },
                universities: []
            }
        ]

        const regionsMap = {};
        regions.forEach(region => {
            region.name = region.locales?.[locale || 'en'] || region.name;
            delete region.locales;
            regionsMap[region.source] = region;
        });

        const universities = await strapi.db.query('api::university.university').findMany({
            where: {
                blocked: false,
                source: { $ne: 'custom' }
            },
            populate: {
                logo: {
                    select: ['url']
                }
            },
            orderBy: { name: 'asc' }
        });

        universities.forEach(university => {
            university.name = university.locales?.[locale || 'en'] || university.name;
            delete university.locales;
            const region = regionsMap[university.source];
            if (region) {
                region.universities.push(university);
            }
        });
        return Object.values(regionsMap);
    }
}));
