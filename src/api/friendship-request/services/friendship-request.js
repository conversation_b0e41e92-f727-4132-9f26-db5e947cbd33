// @ts-nocheck
'use strict';

/**
 * friendship-request service
 */

const { createCoreService } = require('@strapi/strapi').factories;
const { HEART_LEVEL, PRICES, REQUEST_STATUS } = require("../../../constants");
const { NOTIFICATION_TYPE } = require('../../../plugins/strapi-notification/server/constants')

module.exports = createCoreService('api::friendship-request.friendship-request', ({ strapi }) => ({
    async save(values) {
        const { sender, receiver, level, source, transactionId, handleResult } = values;
        const price = level === HEART_LEVEL.LOW ? PRICES.UNLOCK_HEART : 0;
        const data = {
            sender: { disconnect: [], connect: [{ id: sender }] },
            receiver: { disconnect: [], connect: [{ id: receiver }] },
            level,
            source,
            locked: level === HEART_LEVEL.LOW,
            lockPrice: price,
            handleResult: handleResult || REQUEST_STATUS.PENDING
        };
        //save
        const entry = await strapi.db.query('api::friendship-request.friendship-request').create({ data });
        //Update transaction records.
        if (transactionId) {
            // @ts-ignore
            strapi.service('api::transaction.transaction').edit(transactionId, {
                related: `api::friendship-request.friendship-request:${entry.id}`
            });
        }
        //Removed from the other section 
        this.markAsInvalid(sender, receiver);

        //return heart status
        return {
            id: entry.id,
            hearted: true,
            heartedLevel: level
        };
    },

    async edit(id, params = {}) {
        return await strapi.entityService.update('api::friendship-request.friendship-request', id, {
            data: params
        });
    },

    async markAsInvalid(receiver, sender) {
        //Removed from the recommended list. 
        await strapi.plugin('meedata').service('recommendations').markAsInvalid(receiver, sender);
        //Removed from the rating list. 
        await strapi.plugin('meedata').service('ratings').markAsInvalid(receiver, sender, true);
    },

    async reject(requestId, authUser, targetId, reason) {
        await strapi.entityService.update('api::friendship-request.friendship-request', requestId, {
            data: {
                handleResult: REQUEST_STATUS.REJECTED,
                handleMsg: reason
            }
        });
        await strapi.plugin('mq').service('publish').sendNotificationMessage({
            type: NOTIFICATION_TYPE.HEART_REJECT,
            user: { id: targetId },
            rejecter: { id: authUser.id, username: authUser.username },
            ex: {
                en: reason,
                vi: reason
            }
        });
        return {
            rejected: true,
            rejectReason: reason
        }
    }
}));
