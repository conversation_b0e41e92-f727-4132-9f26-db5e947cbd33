const _ = require('lodash');
const { REQUEST_STATUS } = require("../../../../constants");

module.exports = {
    async beforeCreate(event) {
        const { sender, receiver, level, locked, lockPrice, handleResult = REQUEST_STATUS.PENDING, handleMsg } = event.params.data;
        event.state = {
            sender: sender.connect[0].id,
            receiver: receiver.connect[0].id,
            level,
            locked,
            lockPrice,
            handleResult,
            handleMsg
        }
    },
    async afterCreate(event) {
        const { result, state } = event;
        if (state) {
            try {
                await strapi.plugin('meedata').service('requests').sync({
                    ...state,
                    relatedId: result.id
                });
            } catch (error) {
                strapi.log.error("========= Failed to synchronize the requests data ============");
            }
        }
    },

    async afterUpdate(event) {
        const { result, params } = event;
        const { locked, handleResult, handleMsg } = params?.data || {};

        const updateParams = {};
        if (_.has(params?.data, 'locked')) {
            updateParams.locked = locked;
        }

        if (handleResult) {
            updateParams.handleResult = handleResult;
        }

        if (handleMsg) {
            updateParams.handleMsg = handleMsg;
        }
        if (Object.keys(updateParams).length > 0) {
            await strapi.plugin('meedata').service('requests').updateByRelatedId(result.id, updateParams);
        }
    },
}