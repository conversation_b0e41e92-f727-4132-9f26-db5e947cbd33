{"kind": "collectionType", "collectionName": "friendship_requests", "info": {"singularName": "friendship-request", "pluralName": "friendship-requests", "displayName": "FriendshipRequest", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"sender": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "receiver": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "level": {"type": "enumeration", "enum": ["low", "high"]}, "locked": {"type": "boolean", "default": false}, "lockPrice": {"type": "integer"}, "source": {"type": "string"}, "handleResult": {"type": "enumeration", "enum": ["pending", "matched", "rejected", "expired"]}, "handleMsg": {"type": "string"}}}