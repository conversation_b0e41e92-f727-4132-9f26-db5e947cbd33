{"kind": "collectionType", "collectionName": "verification_logs", "info": {"singularName": "verification-log", "pluralName": "verification-logs", "displayName": "verificationLog", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"status": {"type": "enumeration", "enum": ["verified", "not_verified_yet", "rejected"]}, "note": {"type": "json"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "verificationLogs"}}}