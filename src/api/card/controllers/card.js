'use strict';

const utils = require('@strapi/utils');
// @ts-ignore
const { ApplicationError, ValidationError, ForbiddenError, NotFoundError } = utils.errors;
const {
    CARD_COLLECTION,
    RECOMMEND_TYPE,
    HEART_LEVEL,
    PRICES,
    TRANSACTION,
    SOURCE,
    REQUEST_STATUS
} = require('../../../constants');
const { getShuffledArr, getErrorMessage } = require("../../../utils");
const { isSubscribed, addHeartSubscription } = require('../../../utils/redis');
const dayjs = require("dayjs");

module.exports = ({ strapi }) => ({
    async findTempToday(ctx) {
        let partners = await strapi
            .query('plugin::users-permissions.user').findMany({
                where: {
                    id: {
                        $between: [16, 70]
                    }
                },
                populate: {
                    avatar: {
                        select: ['url']
                    }
                }
            });
        partners = getShuffledArr(partners);
        return partners;
    },

    async passed(ctx) {
        let partners = await strapi
            .query('plugin::users-permissions.user').findMany({
                where: {
                    id: {
                        $between: [16, 70]
                    }
                },
                populate: {
                    avatar: {
                        select: ['url']
                    }
                }
            });
        partners = getShuffledArr(partners);
        return partners;

    },
    async findToday(ctx) {
        const authUser = ctx.state.user;

        //Update the latest access time.
        strapi.plugin('meedata').service('users').updateLastAccessTime(authUser.id);

        const [matched, got, sent, premium, given] = await Promise.all([
            await strapi.service('api::card.card').findMatched(authUser?.id, 0, 20, 'unconnected'),
            await strapi.service('api::card.card').findGot(authUser?.id, CARD_COLLECTION.TODAY, 0, 10),
            await strapi.service('api::card.card').findSent(authUser?.id, CARD_COLLECTION.TODAY, 0, 10),
            await strapi.service('api::card.card').findPremiumRecommend(authUser?.id, CARD_COLLECTION.TODAY),
            await strapi.service('api::card.card').findStandardRecommend(authUser?.id, CARD_COLLECTION.TODAY, 0)
        ])
        return { matched, got, sent, premium, given };
    },

    async findPassed(ctx) {
        const authUser = ctx.state.user;
        const diffInDays = dayjs().startOf('day').diff(dayjs(authUser.createdAt, "YYYYMMDD"), 'day');

        const [matched, got, sent, premium, highEval, given] = await Promise.all([
            await strapi.service('api::card.card').findMatched(authUser?.id, 0, 10, 'unconnected'),
            await strapi.service('api::card.card').findGot(authUser?.id, CARD_COLLECTION.PASSED, 0, 10),
            await strapi.service('api::card.card').findSent(authUser?.id, CARD_COLLECTION.PASSED, 0, 10),
            await strapi.service('api::card.card').findPremiumRecommend(authUser?.id, CARD_COLLECTION.PASSED),
            await strapi.service('api::card.card').findHighEval(authUser?.id, 0, 12),
            await strapi.service('api::card.card').findStandardRecommend(authUser?.id, CARD_COLLECTION.PASSED, diffInDays >= 6 ? 6 : diffInDays)
        ])
        return { matched, got, sent, premium, highEval, given };
    },

    async findMatched(ctx) {
        const authUser = ctx.state.user;
        const { offset = 0, limit = 20, status = 'unconnected' } = ctx.query;
        return await strapi.service('api::card.card').findMatched(authUser?.id, offset, limit, status);
    },

    async findGot(ctx) {
        const authUser = ctx.state.user;
        const { offset = 0, limit = 10, coll = CARD_COLLECTION.TODAY } = ctx.query;
        return await strapi.service('api::card.card').findGot(authUser?.id, coll, offset, limit);
    },
    async findSent(ctx) {
        const authUser = ctx.state.user;
        const { offset = 0, limit = 10, coll = CARD_COLLECTION.TODAY } = ctx.query;
        return await strapi.service('api::card.card').findSent(authUser?.id, coll, offset, limit);
    },

    async findHighEval(ctx) {
        const authUser = ctx.state.user;
        let { offset = 0, limit = 10 } = ctx.query;
        return await strapi.service('api::card.card').findHighEval(authUser?.id, offset, limit)
    },

    async premiumRule(ctx) {
        return {
            quantity: 3,
            price: PRICES.PREMIUM_RECOMMENDATION
        }
    },

    async premium(ctx) {
        const locale = ctx.state.locale;
        const authUser = ctx.state.user;
        //check blance
        const recommenders = await strapi.plugin('meedata').service('users').recommendUsers(true, authUser.id, 3);
        if (recommenders.length != 3) {
            throw new ApplicationError(getErrorMessage('error.recommend', locale));
        }
        const sequence = dayjs().format('YYYYMMDD')
        const transactionEntry = await strapi.service("api::transaction.transaction").executePayment(
            authUser.id,
            TRANSACTION.PREMIUM_RECOMMENDATION,
            PRICES.PREMIUM_RECOMMENDATION
        );
        strapi.service('api::recommendation.recommendation').bulkAdd(
            authUser?.id,
            recommenders,
            RECOMMEND_TYPE.PREMIUM,
            sequence,
            transactionEntry.id);
        return recommenders;
    },

    async unlock(ctx) {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const { id } = ctx.params;
        const { lock } = ctx.request.body;
        if (!lock) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        return await strapi.service('api::card.card').unlock(lock, authUser.id, Number(id));
    },

    async detail(ctx) {
        const authUser = ctx.state.user;
        const { id } = ctx.params;
        // const hasViewPermission = await strapi.service('api::card.card').hasViewPermission(id, authUser);
        // if (!hasViewPermission)
        //     throw new ForbiddenError();
        return await strapi.service('api::card.card').detail(Number(id), authUser, ctx.state.locale)
    },

    async getHeartStatus(ctx) {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const { id } = ctx.params;
        let { source = SOURCE.COMMUNITY } = ctx.request.body;
        return await strapi.service('api::card.card').getHeartStatus(Number(id), authUser, locale, source)
    },

    async sentHeart(ctx) {
        const sender = ctx.state.user;
        const locale = ctx.state.locale;
        const { id } = ctx.params;
        let { level, subscriptionPlan, source = SOURCE.PROFILE } = ctx.request.body;
        if (!level || ![HEART_LEVEL.LOW, HEART_LEVEL.HIGHT].includes(level))
            throw new ValidationError(getErrorMessage('error.validated', locale));

        //'double heart' need to be sent in the community.
        if (source === SOURCE.COMMUNITY) {
            level = HEART_LEVEL.HIGHT;
        }

        //check receiver
        const receiver = await strapi.entityService.findOne('plugin::users-permissions.user', Number(id));
        if (!receiver)
            throw new ValidationError(getErrorMessage('error.validated', locale));

        //Check the sent status. If sent, return directly.
        const heartedData = await strapi.plugin('meedata').service('requests').getSentAndGotData(sender?.id, receiver.id);
        if (heartedData.hearted) {
            return heartedData;
        }
        let transactionType = level == HEART_LEVEL.LOW ? TRANSACTION.SENT_HEART : TRANSACTION.SENT_DOUBLE_HEART;
        let transactionPrice = level == HEART_LEVEL.LOW ? PRICES.SENT_HEART : source === SOURCE.COMMUNITY ? PRICES.COMMUNITY_DOUBLE_HEART : PRICES.SENT_DOUBLE_HEART;
        if (level == HEART_LEVEL.LOW) {
            const subscribed = await isSubscribed(sender.id);
            if (subscribed) {
                transactionPrice = 0;
            } else if (subscriptionPlan) {
                subscriptionPlan = await strapi.service("api::subscription-plan.subscription-plan").findOne(subscriptionPlan.id);
                if (!subscriptionPlan)
                    throw new ValidationError(getErrorMessage('error.validated', locale));
                //The price is changed to the price of the subscription.
                transactionPrice = subscriptionPlan.price;
                //insert subscription
                const subscriptionData = {
                    user: sender.id,
                    subscriptionPlan: subscriptionPlan.id,
                    duration: subscriptionPlan.duration,
                    startedAt: new Date(),
                    endedAt: dayjs().add(subscriptionPlan.duration, 'day').toDate(),
                    ex: subscriptionPlan
                }
                await addHeartSubscription(sender.id, subscriptionPlan);
                await strapi.service("api::subscription.subscription").add(subscriptionData);
            }
        }
        const transactionEntry = await strapi.service("api::transaction.transaction").executePayment(
            sender.id,
            transactionType,
            transactionPrice,
            `api::friendship-request.friendship-request`,
            receiver.id);
        //insert data
        const values = {
            sender: sender.id, receiver: receiver.id, level, source, transactionId: transactionEntry.id
        }
        if (heartedData && heartedData.gotHeart && heartedData.gotRelatedId) {
            values.handleResult = REQUEST_STATUS.MATCHED;
            await strapi.service("api::friendship-request.friendship-request").edit(heartedData.gotRelatedId, { handleResult: REQUEST_STATUS.MATCHED });
        }
        const saveResult = await strapi.service("api::friendship-request.friendship-request").save(values);
        //Check whether match is required
        const matchResult = await strapi.service("api::card.card").matchReceiver(sender.id, receiver.id, saveResult);
        return {
            ...saveResult,
            ...matchResult
        };
    },

    //You got DoubleHeart from xxx. If you Accept this, you will be matched.
    async accept(ctx) {
        const authUser = ctx.state.user;
        const { id } = ctx.params;
        const gotDouble = await strapi.plugin('meedata').service('requests').getGotDoubleHeart(Number(id), authUser.id);
        if (!gotDouble) throw new ForbiddenError();
        //update request 
        if (gotDouble.relatedId) {
            await strapi.service("api::friendship-request.friendship-request").edit(gotDouble.relatedId, { handleResult: REQUEST_STATUS.MATCHED });
        }
        return await strapi.service("api::card.card").directMatch(Number(id), authUser.id);
    },

    async rating(ctx) {
        const rater = ctx.state.user;
        const locale = ctx.state.locale;
        const { id } = ctx.params;
        const score = Number(ctx.request.body.score);
        if (isNaN(score) || score < 0.5 || score > 5 || !(score % 1 != 0.5 || score % 1 != 0))
            throw new ValidationError(getErrorMessage('error.validated', locale));

        //check rated
        const rated = await strapi.entityService.findOne('plugin::users-permissions.user', Number(id));
        if (!rated)
            throw new ValidationError(getErrorMessage('error.validated', locale));

        //insert data
        return await strapi.service("api::rating.rating").save(rater.id, rated.id, score);
    },

    async superSend(ctx) {
        const locale = ctx.state.locale;
        const { senderName, receiverName, heartCount = 1 } = ctx.request.body;
        const level = heartCount == 2 ? HEART_LEVEL.HIGHT : HEART_LEVEL.LOW;
        if (!senderName || !receiverName) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const sender = await strapi.plugin('meedata').service('users').findOneByName(senderName);
        if (!sender) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const receiver = await strapi.plugin('meedata').service('users').findOneByName(receiverName);
        if (!receiver) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const result = await strapi.plugin('meedata').service('requests').findOne(sender.id, receiver.id);
        if (result) {
            throw new ApplicationError(getErrorMessage('duplicate.send', locale));
        }

        const transactionEntry = await strapi.service("api::transaction.transaction").executePayment(
            sender.id,
            level == HEART_LEVEL.LOW ? TRANSACTION.SENT_HEART : TRANSACTION.SENT_DOUBLE_HEART,
            level == HEART_LEVEL.LOW ? PRICES.SENT_HEART : PRICES.SENT_DOUBLE_HEART,
            `api::friendship-request.friendship-request`,
            receiver.id);
        //insert data
        const values = {
            sender: sender.id, receiver: receiver.id, level, source: SOURCE.PROFILE, transactionId: transactionEntry.id
        }
        const saveResult = await strapi.service("api::friendship-request.friendship-request").save(values);

        //TODO Check whether match is required
        const matchResult = await strapi.service("api::card.card").matchReceiver(sender.id, receiver.id, saveResult);

        return {
            ...saveResult,
            ...matchResult
        };
    },

    async deleteCard(ctx) {
        const sender = ctx.state.user;
        const { id } = ctx.params;
        await strapi.plugin('meedata').service('friendships').updateDeletedStatus(sender.id, Number(id), true);
        return {
            ok: true
        }
    },

    async suggestJobs(ctx) {
        const authUser = ctx.state.user;
        const { coll = 'business' } = ctx.query;
        if (coll === 'business') {
            let partners = await strapi
                .query('plugin::users-permissions.user').findMany({
                    select: ['id', 'username', 'email', 'fullname'],
                    where: {
                        $or: [
                            {
                                id: {
                                    $between: [200, 223]
                                }
                            },
                            {
                                id: {
                                    $between: [10000200, 10000223]
                                }
                            }
                        ]

                    },
                    populate: {
                        avatar: {
                            select: ['url']
                        }
                    }
                });
            partners = getShuffledArr(partners);
            return partners;
        } else {
            const [senders] = await strapi.db.connection.raw(`SELECT frsl.user_id FROM friendship_requests_receiver_links frrl inner join friendship_requests_sender_links frsl on frsl.friendship_request_id  = frrl.friendship_request_id AND frrl.user_id  = ${authUser.id}`);
            const senderIds = senders.map(el => el.user_id);
            return await strapi
                .query('plugin::users-permissions.user').findMany({
                    select: ['id', 'username', 'email', 'fullname'],
                    where: {
                        id: { $in: senderIds }
                    },
                    populate: {
                        avatar: {
                            select: ['url']
                        }
                    }
                });
        }
    },

    async jobDetail(ctx) {
        const authUser = ctx.state.user;
        const { id } = ctx.params;
        return await strapi.service('api::card.card').jobDetail(Number(id), authUser, ctx.state.locale)
    },


    async sendResume(ctx) {
        const sender = ctx.state.user;
        const locale = ctx.state.locale;
        const { id } = ctx.params;

        //check receiver
        const receiver = await strapi.entityService.findOne('plugin::users-permissions.user', Number(id));
        if (!receiver)
            throw new ValidationError(getErrorMessage('error.validated', locale));

        //Check the sent status. If sent, return directly.
        const heartedData = await strapi.plugin('meedata').service('requests').getSentAndGotData(sender?.id, receiver.id);
        if (heartedData.hearted) {
            return heartedData;
        }
        const transactionEntry = await strapi.service("api::transaction.transaction").executePayment(
            sender.id,
            TRANSACTION.SENT_HEART,
            PRICES.SENT_HEART,
            `api::friendship-request.friendship-request`,
            receiver.id);
        //insert data
        const values = {
            sender: sender.id, receiver: receiver.id, level: HEART_LEVEL.LOW, source: SOURCE.PROFILE, transactionId: transactionEntry.id
        }
        const saveResult = await strapi.service("api::friendship-request.friendship-request").save(values);

        return {
            ...saveResult,
            matched: true
        };
    },

    async reject(ctx) {
        const locale = ctx.state.locale;
        const authUser = ctx.state.user;
        const { id } = ctx.params;
        const { reason } = ctx.request.body;
        if (!reason) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }

        const request = await strapi.db.query('api::friendship-request.friendship-request').findOne({
            where: { sender: id, receiver: authUser.id },
            orderBy: { id: 'desc' },
        });
        if (!request) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        if (request.handleResult === REQUEST_STATUS.REJECTED) {
            throw new ApplicationError(getErrorMessage('request.rejected', locale));
        } else if (request.handleResult === REQUEST_STATUS.EXPIRED) {
            throw new ApplicationError(getErrorMessage('request.expired', locale));
        } else if (request.handleResult === REQUEST_STATUS.MATCHED) {
            throw new ApplicationError(getErrorMessage('request.mathched', locale));
        }
        return await strapi.service("api::friendship-request.friendship-request").reject(request.id, authUser, id, reason);
    }
})
