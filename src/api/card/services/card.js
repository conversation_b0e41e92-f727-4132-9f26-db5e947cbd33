const utils = require('@strapi/utils');
// @ts-ignore
const { ApplicationError } = utils.errors;
const {
    LOCKS,
    PRICES,
    HEART_LEVEL,
    TRANSACTION,
    RECOMMEND_TYPE,
    SOURCE,
} = require('../../../constants');

// @ts-ignore
const {
    getRatingData,
    getRecommendQuotas,
    incrRecommendUsedCount,
    isSubscribed,
    isBlacklisted
} = require("../../../utils/redis");

const { NOTIFICATION_TYPE } = require('../../../plugins/strapi-notification/server/constants')

const dayjs = require("dayjs");
const utc = require('dayjs/plugin/utc');

const { sanitizeUserLocale } = require('../../../utils/locale')
const { getErrorMessage } = require('../../../utils')

module.exports = {
    /**
     * @param {any} authorId
     * @param {any} offset
     * @param {any} limit
     */
    async findMatched(authorId, offset, limit, status) {
        return await strapi.plugin('meedata').service('friendships').findMatched(authorId, offset, limit, status);
    },

    /**
     * @param {any} authorId
     * @param {any} coll
     * @param {any} offset
     * @param {any} limit
     */
    async findGot(authorId, coll, offset, limit) {
        return await strapi.plugin('meedata').service('requests').findGot(authorId, coll, offset, limit);
    },

    /**
     * @param {any} authorId
     * @param {any} coll
     * @param {any} offset
     * @param {any} limit
     */
    async findSent(authorId, coll, offset, limit) {
        return await strapi.plugin('meedata').service('requests').findSent(authorId, coll, offset, limit);
    },

    /**
     * @param {any} authorId
     * @param {any} coll
     */
    async findPremiumRecommend(authorId, coll) {
        return await strapi.plugin('meedata').service('recommendations').findPremiumRecommend(authorId, coll);
    },

    /**
     * @param {any} authorId
     * @param {any} coll
     */
    async findStandardRecommend(authorId, coll, days) {
        return await strapi.plugin('meedata').service('recommendations').findStandardRecommend(authorId, coll, days);
    },

    /**
     * @param {any} authorId
     * @param {any} offset
     * @param {any} limit
     */
    // @ts-ignore
    async findHighEval(authorId, offset, limit) {
        return await strapi.plugin('meedata').service('ratings').findRatings(authorId, offset, limit);
    },

    /**
     * @param {any} id
     * @param {{ id: any; }} authUser
     */
    async detail(id, authUser, locale) {
        let user = await strapi
            .query('plugin::users-permissions.user')
            .findOne({
                where: { id },
                select: ['username', 'birthday', 'gender', 'introduction', 'location', 'avgRating', 'financialStatus', 'verificationStatus', 'status'],
                populate: {
                    avatar: {
                        select: ['id', 'url', 'formats']
                    },
                    photos: {
                        select: ['id', 'url', 'formats']
                    },
                    interests: true,
                    university: {
                        populate: {
                            logo: {
                                select: ['id', 'url', 'formats']
                            }
                        }
                    }
                }
            });
        if (!user)
            return user;

        const data = await this.getHeartStatus(id, authUser, locale, SOURCE.PROFILE);
        user = {
            ...user,
            data
        }
        return await sanitizeUserLocale(user, locale);
    },

    async getHeartStatus(id, authUser, locale, source) {
        //get got and sent status
        const heartedData = await strapi.plugin('meedata').service('requests').getSentAndGotData(authUser?.id, id);
        //get match status
        const isMatched = await strapi.plugin('meedata').service('friendships').isMatched(authUser?.id, id);
        if (source === SOURCE.COMMUNITY) {
            return {
                ...heartedData,
                matched: isMatched,
                heartList: [
                    {
                        level: HEART_LEVEL.HIGHT,
                        price: PRICES.COMMUNITY_DOUBLE_HEART
                    }
                ],
            }
        }

        //get subscription Plans
        const subscriptionPlans = await strapi.service("api::subscription-plan.subscription-plan").findActive(locale);
        //get subscribed status
        const subscribed = await isSubscribed(authUser.id);
        //get rating status
        const ratingData = await getRatingData(authUser?.id, id);
        //Check whether the user is blacklisted. 
        const blacklisted = authUser ? await isBlacklisted(authUser.id, id) : false;
        return {
            ...heartedData,
            ...ratingData,
            matched: isMatched,
            heartList: [
                {
                    level: HEART_LEVEL.LOW,
                    price: PRICES.SENT_HEART
                },
                {
                    level: HEART_LEVEL.HIGHT,
                    price: PRICES.SENT_DOUBLE_HEART
                }
            ],
            subscribed,
            blacklisted,
            subscriptionPlans
        }
    },

    /**
     * @param {any} id
     * @param {any} authUser
     */
    // @ts-ignore
    async hasViewPermission(id, authUser) {
        if (!authUser) {
            return false;
        }
        //check
        return true;
    },

    /**
     * @param {any} lock
     * @param {any} authUserId
     * @param {any} targetId
     */
    async unlock(lock, authUserId, targetId) {
        const locale = strapi.requestContext.get().state.locale;
        switch (lock) {
            case LOCKS.HEART:
                return this.unlockHeart(targetId, authUserId, locale);
            case LOCKS.RECOMMEND:
                return this.unlockRecommend(authUserId, targetId, locale);
            case LOCKS.EVALUATION:
                return this.unlockEvalution(targetId, authUserId);
            default:
                throw new ApplicationError(getErrorMessage('error.occurred', locale));
        }
    },
    /**
     * @param {any} sender
     * @param {any} receiver
     */
    async unlockHeart(sender, receiver, locale) {
        const entry = await strapi.plugin('meedata').service('requests').findOne(sender, receiver);
        if (!entry) {
            throw new ApplicationError(getErrorMessage('error.occurred', locale));
        }
        if (entry.locked) {
            // @ts-ignore
            //execute payment
            await strapi.service("api::transaction.transaction").executePayment(
                receiver,
                TRANSACTION.UNLOCK_HEART,
                PRICES.UNLOCK_HEART,
                `api::friendship-request.friendship-request:${entry.relatedId}`,
                sender);
            // @ts-ignore
            await strapi.service("api::friendship-request.friendship-request").edit(entry.relatedId, { locked: false });
        }
        // @ts-ignore
        return await strapi.plugin('meedata').service('requests').findSingleGot(sender, receiver);
    },

    /**
     * @param {any} authUserId
     * @param {any} sequence
     */
    async unlockRecommend(authUserId, sequence, locale) {
        const dailyGivenRules = await strapi.service('api::setting.setting').getDailyGivenRules();
        const diffInDays = dayjs().startOf('day').diff(dayjs(String(sequence), "YYYYMMDD"), 'day');
        if (diffInDays > 7 || diffInDays < 0) {
            throw new ApplicationError(getErrorMessage('error.occurred', locale));
        }
        const remainingQuota = await getRecommendQuotas(sequence, authUserId, dailyGivenRules.length);

        if (remainingQuota < 1) {
            throw new ApplicationError(getErrorMessage('error.occurred', locale));
        }
        //today
        if (diffInDays === 0) {
            const curHour = dayjs().hour();
            const activeCount = dailyGivenRules.filter(hour => hour <= curHour).length;
            const userdCount = dailyGivenRules.length - remainingQuota;
            if (userdCount >= activeCount) {
                throw new ApplicationError(getErrorMessage('error.occurred', locale));
            }
        }
        // @ts-ignore
        const recommenders = await strapi.plugin('meedata').service('users').recommendUsers(false, authUserId, 1);
        if (recommenders.length > 0) {
            //execute payment
            let transactionEntry;
            if (diffInDays > 0) {
                // @ts-ignore
                transactionEntry = await strapi.service("api::transaction.transaction").executePayment(
                    authUserId,
                    TRANSACTION.UNLOCK_PAST_RECOMMENDATION,
                    PRICES.UNLOCK_PAST_RECOMMENDATION
                );
            }
            await incrRecommendUsedCount(sequence, authUserId);
            // @ts-ignore
            strapi.service('api::recommendation.recommendation').bulkAdd(
                authUserId,
                recommenders,
                RECOMMEND_TYPE.STANDARD,
                sequence,
                transactionEntry?.id);
            dayjs.extend(utc);
            //模拟时间
            const createdAt = dayjs(String(sequence)).hour(12).minute(0).second(0).utc().format();
            return {
                ...recommenders[0],
                createdAt
            };
        } else {
            throw new ApplicationError(getErrorMessage('error.recommend', locale));
        }
    },

    /**
     * @param {any} rater
     * @param {any} rated
     */
    // @ts-ignore
    async unlockEvalution(rater, rated, locale) {
        // @ts-ignore
        const entry = await strapi.plugin('meedata').service('ratings').findOne(rater, rated);
        if (!entry) {
            throw new ApplicationError(getErrorMessage('error.occurred', locale));
        }
        if (entry.locked) {
            //execute payment
            // @ts-ignore
            await strapi.service("api::transaction.transaction").executePayment(
                rated,
                TRANSACTION.UNLOCK_EVALUATION,
                PRICES.UNLOCK_EVALUATION,
                `api::rating.rating:${entry.relatedId}`,
                rater);
            // @ts-ignore
            await strapi.service("api::rating.rating").edit(entry.relatedId, { locked: false });
        }
        return await strapi.plugin('meedata').service('ratings').findSingleRating(rater, rated);
    },

    /**
     * @param {any} sender
     * @param {any} receiver
     */
    // @ts-ignore
    async matchReceiver(sender, receiver, related) {
        const entry = await strapi.plugin('meedata').service('requests').findOne(receiver, sender);
        if (entry) {
            // @ts-ignore
            await strapi.service("api::friendship.friendship").save(sender, receiver);
            //send matched push
            await strapi.plugin('mq').service('publish').sendNotificationMessage({
                type: NOTIFICATION_TYPE.MATCHED,
                user: { id: receiver },
                friendId: sender
            });
            await strapi.plugin('mq').service('publish').sendNotificationMessage({
                type: NOTIFICATION_TYPE.MATCHED,
                user: { id: sender },
                friendId: receiver
            });
        } else {
            await strapi.plugin('mq').service('publish').sendGotHeartMessage({
                sender, receiver, relatedId: related.id, level: related.heartedLevel
            });
        }
        return {
            matched: entry !== null
        }
    },

    async directMatch(userId, friendId) {
        // @ts-ignore
        await strapi.service("api::friendship.friendship").save(userId, friendId);
        //send matched push
        await strapi.plugin('mq').service('publish').sendNotificationMessage({
            type: NOTIFICATION_TYPE.MATCHED,
            user: { id: userId },
            friendId: friendId
        });
        await strapi.plugin('mq').service('publish').sendNotificationMessage({
            type: NOTIFICATION_TYPE.MATCHED,
            user: { id: friendId },
            friendId: userId
        });
        return { matched: true }
    },


    /**
     * @param {any} id
     * @param {{ id: any; }} authUser
     */
    async jobDetail(id, authUser, locale) {
        let user = await strapi
            .query('plugin::users-permissions.user')
            .findOne({
                where: { id },
                select: ['username', 'birthday', 'gender', 'introduction', 'location', 'avgRating', 'financialStatus', 'verificationStatus', 'status'],
                populate: {
                    avatar: {
                        select: ['id', 'url', 'formats']
                    },
                    photos: {
                        select: ['id', 'url', 'formats']
                    },
                    interests: true,
                    university: {
                        populate: {
                            logo: {
                                select: ['id', 'url', 'formats']
                            }
                        }
                    }
                }
            });
        if (!user)
            return user;


        const heartedData = await strapi.plugin('meedata').service('requests').getSentAndGotData(authUser?.id, id);
        const blacklisted = authUser ? await isBlacklisted(authUser.id, id) : false;
        let data = {
            ...heartedData,
            heartList: [
                {
                    level: HEART_LEVEL.LOW,
                    price: PRICES.SENT_HEART
                },
                {
                    level: HEART_LEVEL.HIGHT,
                    price: PRICES.SENT_DOUBLE_HEART
                }
            ],
            blacklisted
        }
        user = {
            ...user,
            data
        }
        return await sanitizeUserLocale(user, locale);
    },
}