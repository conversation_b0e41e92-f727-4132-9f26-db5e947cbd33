'use strict';

module.exports = {
  routes: [
    {
      method: 'GET',
      path: '/cards/today',
      handler: 'card.findTempToday'
    },
    {
      method: 'GET',
      path: '/cards/new-today',
      handler: 'card.findToday'
    },
    {
      method: 'GET',
      path: '/cards/passed',
      handler: 'card.findPassed'
    },
    {
      method: 'GET',
      path: '/cards/matched',
      handler: 'card.findMatched'
    },
    {
      method: 'GET',
      path: '/cards/got',
      handler: 'card.findGot'
    },
    {
      method: 'GET',
      path: '/cards/sent',
      handler: 'card.findSent'
    },
    {
      method: 'GET',
      path: '/cards/high-eval',
      handler: 'card.findHighEval'
    },
    {
      method: 'GET',
      path: '/cards/premium-rule',
      handler: 'card.premiumRule'
    },
    {
      method: 'POST',
      path: '/cards/premium',
      handler: 'card.premium'
    },
    {
      method: 'GET',
      path: '/cards/:id',
      handler: 'card.detail'
    },
    {
      method: 'GET',
      path: '/cards/:id/heart',
      handler: 'card.getHeartStatus'
    },
    {
      method: 'POST',
      path: '/cards/:id/heart',
      handler: 'card.sentHeart'
    },
    {
      method: 'POST',
      path: '/cards/:id/accept',
      handler: 'card.accept'
    },
    {
      method: 'POST',
      path: '/cards/:id/unlock',
      handler: 'card.unlock'
    },
    {
      method: 'POST',
      path: '/cards/:id/rating',
      handler: 'card.rating'
    },
    {
      method: 'POST',
      path: '/cards/super-send',
      handler: 'card.superSend'
    },
    {
      method: 'GET',
      path: '/cards/jobs/today',
      handler: 'card.suggestJobs'
    },
    {
      method: 'GET',
      path: '/cards/jobs/:id',
      handler: 'card.jobDetail'
    },
    {
      method: 'POST',
      path: '/cards/jobs/:id/send',
      handler: 'card.sendResume'
    },
    {
      method: 'POST',
      path: '/cards/:id/delete',
      handler: 'card.deleteCard'
    },
    {
      method: 'POST',
      path: '/cards/:id/reject',
      handler: 'card.reject'
    },
  ]
}
