'use strict';

/**
 * feedback-category controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const { sanitizeLocale } = require('../../../utils/locale');

module.exports = createCoreController('api::feedback-category.feedback-category', ({ strapi }) => ({
    async find(ctx) {
        const locale = ctx.state.locale;
        const entries = await strapi.db.query('api::feedback-category.feedback-category').findMany({
            orderBy: { order: 'asc' }
        });
        return await sanitizeLocale(entries, locale);
    }
}));
