'use strict';

/**
 * setting service
 */

const { createCoreService } = require('@strapi/strapi').factories;
const { SETTING_KEYS } = require('../../../constants');
const { getSettings } = require('../../../utils/redis');

module.exports = createCoreService('api::setting.setting', ({ strapi }) => ({
    async importSettings() {
        const settings = [
            {
                key: "daily_given_cards", value: [9, 12, 15, 18]
            },
            {
                key: "daily_comment_reminder_count", value: 10
            }
        ]
        const keys = settings.map(item => item.key);
        const entries = await strapi.db.query('api::setting.setting').findMany({
            where: { key: { $in: keys } },
        });

        const filteredSettings = settings.filter(setting => !entries.some(entry => entry.key === setting.key));
        if (filteredSettings.length > 0) {
            await strapi.db.query('api::setting.setting').createMany({ data: filteredSettings });
        }
    },

    async getSetting(key) {
        const entry = await strapi.db.query('api::setting.setting').findOne({
            select: ['key', 'value'],
            where: { key },
        });
        return entry?.value;
    },

    async updateSetting(key, value) {
        const entry = await strapi.db.query('api::setting.setting').findOne({
            select: ['id', 'key', 'value'],
            where: { key },
        });
        let result;
        if (entry) {
            result = await strapi.db.query('api::setting.setting').update({
                select: ['key', 'value'],
                where: { id: entry.id },
                data: { value },
            });
        } else {
            result = await strapi.db.query('api::setting.setting').create({
                select: ['key', 'value'],
                data: {
                    key, value
                }
            });
        }
        return result.value;
    },

    async getDailyGivenRules() {
        return await getSettings(SETTING_KEYS.DAILY_GIVEN_CARDS) || [9, 12, 15, 18];
    },

    async getCommentReminderRule() {
        return await getSettings(SETTING_KEYS.DAILY_COMMENT_REMINDER_COUNT) || 10;
    },
}));
