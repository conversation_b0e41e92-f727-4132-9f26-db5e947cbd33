const { updateSettings } = require('../../../../utils/redis');
module.exports = {
    async afterCreate(event) {
        const { result } = event;
        try {
            if (result.key && result.value) {
                await updateSettings(result.key, result.value);
            }
        } catch (error) {
            strapi.log.info('Failed to synchronize settings information into the redis.')
        }
    },

    async afterUpdate(event) {
        const { result } = event;
        try {
            if (result.key && result.value) {
                await updateSettings(result.key, result.value);
            }
        } catch (error) {
            strapi.log.info('Failed to synchronize settings information into the redis.')
        }
    },

    async afterCreateMany(event) {
        const { data } = event.params;
        await Promise.all(data.map(async item => {
            if (item.key && item.value) {
                await updateSettings(item.key, item.value);
            }
        }))
    }
}