{"kind": "collectionType", "collectionName": "reports", "info": {"singularName": "report", "pluralName": "reports", "displayName": "Report"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"reporter": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "reported": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "reasons": {"type": "json"}, "attachments": {"allowedTypes": ["images"], "type": "media", "multiple": true}, "action": {"type": "enumeration", "enum": ["PERMA_BAN", "TEMP_BAN", "WARNING", "NOTHING"]}, "tempBanDuration": {"type": "integer"}, "processedAt": {"type": "datetime"}, "note": {"type": "text"}}}