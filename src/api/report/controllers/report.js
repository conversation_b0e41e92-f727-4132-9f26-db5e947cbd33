'use strict';

/**
 * report controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const utils = require('@strapi/utils');
// @ts-ignore
const { ValidationError } = utils.errors;
const { getErrorMessage } = require('../../../utils')

module.exports = createCoreController('api::report.report', ({ strapi }) => ({
    async create(ctx) {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const { reported, reasons, attachments } = ctx.request.body;
        if (!reported || !reasons) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        let data = {
            user: authUser.id,
            reporter: authUser.id,
            reported: reported,
            reasons: reasons,
            attachments: attachments
        }
        await strapi.db.query('api::report.report').create({ data: data });
        return {
            ok: true
        }
    }
}));
