{"kind": "collectionType", "collectionName": "asset_certifications", "info": {"singularName": "asset-certification", "pluralName": "asset-certifications", "displayName": "AssetCertification", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"issuedate": {"type": "datetime"}, "expirydate": {"type": "datetime"}, "asset_verifications": {"type": "relation", "relation": "oneToMany", "target": "api::asset-verification.asset-verification"}, "owner": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "inversedBy": "asset_certification"}, "review_users": {"type": "relation", "relation": "oneToMany", "target": "admin::user"}, "asset_result": {"type": "enumeration", "enum": ["risingStar", "oneStar", "twoStar", "threeStar", "fourStar", "fiveStar", "sixStar", "sevenStar"]}}}