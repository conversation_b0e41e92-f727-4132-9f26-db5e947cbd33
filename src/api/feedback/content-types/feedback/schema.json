{"kind": "collectionType", "collectionName": "feedbacks", "info": {"singularName": "feedback", "pluralName": "feedbacks", "displayName": "<PERSON><PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"email": {"type": "email"}, "category": {"type": "relation", "relation": "oneToOne", "target": "api::feedback-category.feedback-category"}, "content": {"type": "text"}, "attachments": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "answer": {"type": "text"}, "processedAt": {"type": "datetime"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}