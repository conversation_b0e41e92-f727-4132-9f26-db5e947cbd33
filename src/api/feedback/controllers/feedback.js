'use strict';

/**
 * feedback controller
 */
const utils = require('@strapi/utils');
// @ts-ignore
const { ApplicationError, ValidationError, ForbiddenError, NotFoundError } = utils.errors;
const { createCoreController } = require('@strapi/strapi').factories;
const { getErrorMessage } = require('../../../utils')

module.exports = createCoreController('api::feedback.feedback', ({ strapi }) => ({
    async create(ctx) {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const { email, content } = ctx.request.body;
        if (!email || !content)
            throw new ValidationError(getErrorMessage('error.validated', locale));

        const data = {
            ...ctx.request.body,
            user: authUser?.id
        }

        await strapi.db.query('api::feedback.feedback').create({
            data,
        });
        return {
            ok: true
        }
    }
}));
