'use strict';

/**
 * rating service
 */

const { createCoreService } = require('@strapi/strapi').factories;
const {
    addRating,
    getAvgRating,
    incrRatingCount,
    clearRatingCount
} = require("../../../utils/redis");
const { PRICES, TRANSACTION } = require("../../../constants");
module.exports = createCoreService('api::rating.rating', ({ strapi }) => ({
    /**
     * @param {any} rater
     * @param {any} rated
     * @param {any} score
     */
    async save(rater, rated, score) {
        const existEntry = await strapi.db.query('api::rating.rating').findOne({
            where: {
                rater: { id: rater },
                rated: { id: rated }
            }
        });
        let bonus = 0;
        if (existEntry) {
            await strapi.entityService.update('api::rating.rating', 1, {
                data: { score },
            });
        } else {
            const data = {
                rater: { disconnect: [], connect: [{ id: rater }] },
                rated: { disconnect: [], connect: [{ id: rated }] },
                score,
                locked: true,
                lockPrice: PRICES.UNLOCK_EVALUATION,
            };
            const entry = await strapi.db.query('api::rating.rating').create({
                data
            });
            // If user evaluate 5 partners (regardless 1,2 or 5 star eval for each partner), then the user will get +1 MC with alarm. And revise the system messages for this logic. This means 1 eval = 0.2 MC, but will get +1 MC at every 5 eval.
            const count = await incrRatingCount(rater);
            if (Number(count) === 5) {
                bonus = 1;
                await clearRatingCount(rater);
                strapi.service("api::transaction.transaction").executeBonus(
                    rater,
                    TRANSACTION.EVALUATE_BONUS,
                    PRICES.EVALUATE_BONUS,
                    `api::friendship-request.rating-rating:${entry.id}`,
                    rated);
            }
        }
        await addRating(rated, rater, score);
        this.updateAvgRating(rated);
        return {
            rated: true,
            ratingScore: score,
            bonus
        };
    },

    async edit(id, params = {}) {
        return await strapi.entityService.update('api::rating.rating', id, {
            data: params
        });
    },

    async updateAvgRating(rated) {
        const score = await getAvgRating(rated);
        await strapi.entityService.update('plugin::users-permissions.user', rated, {
            data: { avgRating: score }
        });
    }
}));
