const _ = require('lodash');

module.exports = {
    async beforeCreate(event) {
        const { rater, rated, score, locked, lockPrice } = event.params.data;
        if (score > 0) {
            event.state = {
                rater: rater.connect[0].id,
                rated: rated.connect[0].id,
                score,
                locked,
                lockPrice
            }
        }
    },
    async afterCreate(event) {
        const { result, state } = event;
        if (state) {
            try {
                await strapi.plugin('meedata').service('ratings').sync({
                    ...state,
                    relatedId: result.id
                });
            } catch (error) {
                strapi.log.error("========= Failed to synchronize the rating data ============");
            }
        }
    },

    async afterUpdate(event) {
        const { result, params } = event;
        if (_.has(params?.data, 'locked')) {
            await strapi.plugin('meedata').service('ratings').updateByRelatedId(result.id, {
                locked: params.data.locked,
            });
        }
    },
}