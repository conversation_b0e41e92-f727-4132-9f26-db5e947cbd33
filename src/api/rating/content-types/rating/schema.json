{"kind": "collectionType", "collectionName": "ratings", "info": {"singularName": "rating", "pluralName": "ratings", "displayName": "Rating", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"rater": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "rated": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "score": {"type": "float"}, "locked": {"type": "boolean", "default": false}, "lockPrice": {"type": "integer"}}}