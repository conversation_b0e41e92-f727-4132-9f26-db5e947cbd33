const { addWhitelist, removeWhitelist } = require('../../../../utils/redis')
module.exports = {
  async beforeCreate(event) {
    const [user] = event.params.data.user.connect;
    if (!user)
      throw new Error("You have to choose a user.");
    const count = await strapi.db.query("api::whitelist.whitelist").count({
      where: {
        user: {
          id: user.id
        }
      }
    });
    if (count > 0) {
      throw new Error("This user is already whitelisted.");
    }
  },
  async afterCreate(event) {
    const [user] = event.params.data.user.connect;
    addWhitelist(user.id);
  },

  async beforeUpdate(event) {
    const [user] = event.params.data.user.connect;
    if (!user) {
      throw new Error("You have to choose a user.");
    }
    const count = await strapi.db.query("api::whitelist.whitelist").count({
      where: {
        user: {
          id: user.id
        }
      }
    });
    if (count > 0) {
      throw new Error("This user is already whitelisted.");
    }
  },
  async afterUpdate(event) {
    const [connectUser] = event.params.data.user.connect;
    const [disconnectUser] = event.params.data.user.disconnect;
    removeWhitelist(disconnectUser.id);
    addWhitelist(connectUser.id);
  },

  async beforeDelete(event) {
    const { id } = event.params.where;
    const entry = await strapi.db.query("api::whitelist.whitelist").findOne({
      where: {
        id: id
      },
      populate: {
        user: {
          select: ['id']
        }
      }
    });
    if (entry?.user) {
      event.state = entry.user.id;
    }
  },

  async afterDelete(event) {
    if (event.state) {
      removeWhitelist(event.state);
    }
  },
  async beforeDeleteMany(event) {
    const ids = event.params.where['$and'][0].id['$in'];
    const entries = await strapi.db.query("api::whitelist.whitelist").findMany({
      where: {
        id: { $in: ids }
      },
      populate: {
        user: {
          select: ['id']
        }
      }
    });
    const userIds = entries.map(entry => entry.user.id);
    if (userIds.length > 0) {
      event.state = userIds;
      removeWhitelist(userIds);
    }
  },

  async afterDeleteMany(event) {
    if (event.state) {
      removeWhitelist(event.state);
    }
  }
}