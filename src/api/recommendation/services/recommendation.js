'use strict';

/**
 * recommendation service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::recommendation.recommendation', ({ strapi }) => ({

    /**
     * @param {any} recommended
     * @param {any[]} recommenders
     * @param {any} recommendType
     * @param {any} sequence
     * @param {any} transactionId
     */
    async bulkAdd(recommended, recommenders, recommendType, sequence, transactionId) {
        const entries = await Promise.all(recommenders.map(async recommender => {
            const data = {
                recommender: { disconnect: [], connect: [{ id: recommender.id }] },
                recommended: { disconnect: [], connect: [{ id: recommended }] },
                type: recommendType,
                sequence,
                locked: false,
                lockPrice: 0,
            };

            //Removed from the rating list. 
            await strapi.plugin('meedata').service('ratings').markAsInvalid(recommender.id, recommended, false);

            return await strapi.db.query('api::recommendation.recommendation').create({
                data
            });
        }));
        if (transactionId) {
            const ids = entries.map(item => item.id).join(',');
            // @ts-ignore
            await strapi.service('api::transaction.transaction').edit(transactionId, {
                related: `api::recommendation.recommendation:${ids}`,
                ex: entries
            });
        }
        return entries;
    },

    /**
     * @param {any} id
     */
    async edit(id, params = {}) {
        return await strapi.entityService.update('api::recommendation.recommendation', id, {
            data: params
        });
    },
}));
