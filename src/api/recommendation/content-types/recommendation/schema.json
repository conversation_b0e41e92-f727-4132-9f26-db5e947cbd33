{"kind": "collectionType", "collectionName": "recommendations", "info": {"singularName": "recommendation", "pluralName": "recommendations", "displayName": "recommendation", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"recommender": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "recommended": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "type": {"type": "enumeration", "enum": ["standard", "premium"]}, "locked": {"type": "boolean"}, "lockPrice": {"type": "integer"}, "sequence": {"type": "biginteger"}}}