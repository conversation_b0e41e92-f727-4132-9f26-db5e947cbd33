const _ = require('lodash');

module.exports = {
    async beforeCreate(event) {
        const { recommender, recommended, type, locked, sequence, lockPrice } = event.params.data;
        event.state = {
            recommender: recommender.connect[0].id,
            recommended: recommended.connect[0].id,
            sequence,
            type,
            locked,
            lockPrice
        }
    },
    async afterCreate(event) {
        const { result, state } = event;
        if (event.state) {
            try {
                await strapi.plugin('meedata').service('recommendations').sync(
                    {
                        ...state,
                        relatedId: result.id
                    }
                );
            } catch (error) {
                strapi.log.error("========= Failed to synchronize the recommendations data ============");
            }
        }
    },

    async afterUpdate(event) {
        const { result, params } = event;
        if (_.has(params?.data, 'locked')) {
            await strapi.plugin('meedata').service('recommendations').updateByRelatedId(result.id, {
                locked: params.data.locked,
            });
        }
    },
}