{"kind": "collectionType", "collectionName": "subscriptions", "info": {"singularName": "subscription", "pluralName": "subscriptions", "displayName": "Subscription", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "startedAt": {"type": "datetime"}, "endedAt": {"type": "datetime"}, "duration": {"type": "integer"}, "subscriptionPlan": {"type": "relation", "relation": "oneToOne", "target": "api::subscription-plan.subscription-plan"}, "ex": {"type": "json"}}}