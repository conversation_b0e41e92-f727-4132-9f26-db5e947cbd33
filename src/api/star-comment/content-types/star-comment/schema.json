{"kind": "collectionType", "collectionName": "star_comments", "info": {"singularName": "star-comment", "pluralName": "star-comments", "displayName": "StarEventComment", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"text": {"type": "text", "required": true}, "creator": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "event": {"type": "relation", "relation": "oneToOne", "target": "api::star-event.star-event"}, "removed": {"type": "boolean", "default": false}, "blocked": {"type": "boolean", "default": false}}}