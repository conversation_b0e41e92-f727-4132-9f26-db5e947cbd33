import { factories } from "@strapi/strapi";
export default factories.createCoreController(
  "api::star-comment.star-comment",
  ({ strapi }) => ({
    async getEventComments(ctx) {
      try {
        const { id } = ctx.params;
        const { offset = 0, limit = 20 } = ctx.request.query;

        var commentsService: any = strapi.service("api::star-comment.star-comment");
        const comments = await commentsService.findWith({
          eventId: id,
          offset: offset,
          limit: limit,
        });
        return comments;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },
    async postEventComments(ctx) {
      try {
        const { id } = ctx.params;
        const { text } = ctx.request.body;
        const authUser = ctx.state.user;
        const createdComment = await strapi.entityService.create(
          "api::star-comment.star-comment",
          {
            data: {
              event: id,
              text,
              creator: authUser.id,
            },
            populate: {
              creator: {
                fields: ["username", "email", "level", "title", "fullname"],
                populate: {
                  avatar: {
                    fields: ["url", "mime"],
                  },
                },
              },
            },
          }
        );
        return createdComment;
      } catch (error) {
        return ctx.badRequest(error.message);
      }
    },
  })
);
