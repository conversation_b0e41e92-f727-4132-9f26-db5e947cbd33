"use strict";

/**
 * star-comment service
 */

const { createCoreService } = require("@strapi/strapi").factories;

module.exports = createCoreService(
  "api::star-comment.star-comment",
  ({ strapi }) => ({
    async findWith({
      eventId,
      offset = 0,
      limit = 100,
      removed = false,
      blocked = false,
    }) {
      var filters = {};
      if (eventId) {
        filters.event = eventId;
      }
      filters.removed = removed;
      filters.blocked = blocked;

      const comments = await strapi.entityService.findMany(
        "api::star-comment.star-comment",
        {
          filters: filters,
          fields: ["text", "createdAt"],
          populate: {
            creator: {
              fields: ["username", "email", "level", "title", "fullname"],
              populate: {
                avatar: {
                  fields: ["url"],
                },
              },
            },
          },
          sort: { createdAt: "desc" },
          limit: limit,
          start: offset,
        }
      );
      return comments;
    },

    async countWithEvent(eventId) {
      const count = await strapi.entityService.count(
        "api::star-comment.star-comment",
        {
          filters: { event: eventId, removed: false },
        }
      );
      return count;
    },
  })
);
