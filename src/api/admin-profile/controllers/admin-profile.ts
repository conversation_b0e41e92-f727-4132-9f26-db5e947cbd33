/**
 * admin-profile controller
 */
import { factories } from "@strapi/strapi";
import _ from "lodash";

export default factories.createCoreController(
  "api::admin-profile.admin-profile",
  ({ strapi }) => ({
    async find(ctx) {
      const { ids } = ctx.query;
      if (!ids) {
        return [];
      }
      const profiles = await strapi.db
        .query("api::admin-profile.admin-profile")
        .findMany({
          where: { adminUser: { id: { $in: ids.split(",") } } },
          select: ["name", "position"],
          populate: {
            // @ts-ignore
            signature: { select: ["url"] },
            avatar: { select: ["url"] },
          },
        });
      if (profiles.length === 0) {
        return [];
      }
      return profiles.map((profile) =>
        _.pick(profile, ["name", "position", "signature", "avatar"])
      );
    },
  })
);
