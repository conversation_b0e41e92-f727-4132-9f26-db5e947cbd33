{"kind": "collectionType", "collectionName": "admin_profiles", "info": {"singularName": "admin-profile", "pluralName": "admin-profiles", "displayName": "Admin Profile"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "position": {"type": "string"}, "signature": {"allowedTypes": ["images"], "type": "media", "multiple": false}, "avatar": {"allowedTypes": ["images"], "type": "media", "multiple": false}, "adminUser": {"type": "relation", "relation": "oneToOne", "target": "admin::user"}}}