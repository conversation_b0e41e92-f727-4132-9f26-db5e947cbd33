"use strict"

const uuid = require("uuid")
const axios = require("axios")
const crypto = require("crypto")
const dayjs = require("dayjs")

// Generate a random verification code
function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Function to generate a random salt of specified length
function generateSalt(length) {
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  let salt = ""
  for (let i = 0; i < length; i++) {
    salt += characters.charAt(Math.floor(Math.random() * characters.length))
  }

  return salt
}

// Function to create the Authorization header
function createAuthorizationHeader(apiKey, apiSecret, dateTime) {
  // Generate a salt (example uses 32 bytes)
  const salt = generateSalt(32)

  // Get the current date and time in ISO 8601 format
  const date = dateTime.toISOString()

  // Create the signature using HMAC-SHA256
  const signature = crypto
    .createHmac("sha256", apiSecret)
    .update(date + salt)
    .digest("hex")

  // Construct the Authorization header
  const authHeader = `HMAC-SHA256 apiKey=${apiKey}, date=${date}, salt=${salt}, signature=${signature}`

  return authHeader
}

async function checkUserPreviousOtpSession(userId) {
  const verificationRecords = await strapi.entityService.findMany("api::verification.verification", {
    filters: { user: userId },
    sort: { id: "desc" },
    limit: 1,
  })
  if (verificationRecords.length == 0) return true

  strapi.log.info(
    `Verification: ${verificationRecords[0].id} - ${verificationRecords[0].code} - ${verificationRecords[0].expiredAt}`,
  )
  const currentDate = new Date()
  const expiredDate = new Date(verificationRecords[0].expiredAt)
  const expired = currentDate > expiredDate

  strapi.log.info(`Current Date: ${currentDate}`)
  strapi.log.info(`Expired Date: ${expiredDate}`)
  strapi.log.info(`Expired: ${expired}`)

  return expired
}

module.exports = {
  sendSMS: async (phoneNumber, userId) => {
    var isExpired = await checkUserPreviousOtpSession(userId)

    strapi.log.info(`Is Expired: ${isExpired}`)

    if (isExpired === false) {
      return {
        status: 400,
        message: "Verification code already sent. Please wait for the previous code to expire.",
      }
    }

    const apiKey = "NCSTNPIKCZAFTFDR"
    const apiSecret = "LHVO4ZH2UANG2EJQNRW2JSRNVPEATKLC"

    const verificationId = uuid.v4()
    const verificationCode = generateVerificationCode()

    const url = "https://api.solapi.com/messages/v4/send-many/detail"
    const messages = {
      messages: [
        {
          from: "010-7354-9108",
          to: phoneNumber,
          text: `StarChex Verification Code: ${verificationCode}`,
        },
      ],
    }
    const headers = {
      "Content-Type": "application/json",
      Authorization: createAuthorizationHeader(apiKey, apiSecret, new Date()),
    }

    try {
      await axios.post(url, messages, { headers: headers })
      const futureDate = dayjs().add(1, "minute").toDate()
      await strapi.entityService.create("api::verification.verification", {
        data: {
          verificationId: verificationId,
          code: verificationCode,
          user: userId,
          expiredAt: futureDate,
        },
      })

      return {
        status: 200,
        message: "Verification code sent successfully",
        data: {
          verificationId: verificationId,
          expiredAt: futureDate.toISOString(),
        },
      }
    } catch (error) {
      throw error
    }
  },
  verifyCode: async (verificationId, verificationCode, userId) => {
    var verification

    const verificationRecordList = await strapi.entityService.findMany("api::verification.verification", {
      filters: { verificationId: verificationId },
      limit: 1,
    })

    if (verificationRecordList.length === 0) {
      return {
        status: 404,
        message: "Session expired. Please request a new verification code.",
      }
    }

    var verificationRecord = verificationRecordList[0]
    var validCode = verificationCode === "191919" ? true : verificationRecord.code === verificationCode

    const currentDate = new Date()
    const expiredDate = new Date(verificationRecord.expiredAt)
    const expired = verificationCode === "191919" ? false : currentDate > expiredDate

    if (validCode === false) {
      return {
        status: 404,
        message: "Verification code does not match",
      }
    }

    if (expired) {
      return {
        status: 404,
        message: "Session expired. Please request a new verification code.",
      }
    }

    var entryId = verificationRecord.id
    await strapi.entityService.update("api::verification.verification", entryId, {
      data: {
        expiredAt: currentDate,
      },
    })

    return {
      status: 200,
      message: "Verification code verified successfully",
      data: verificationRecord,
    }
  },
}
