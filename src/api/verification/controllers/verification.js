'use strict';

module.exports = {
  async sendVerificationCode(ctx) {
    const { phoneNumber, userId } = ctx.request.body;

    // Send SMS
    try {
      var response = await strapi
        .service("api::verification.verification")
        .sendSMS(phoneNumber, userId);

      return ctx.send(response);
    } catch (error) {
      return ctx.badRequest('Failed to send verification code: ' + error.message);
    }
  },
  async verifyCode(ctx) {
    const { verificationId, verificationCode, userId } = ctx.request.body;

    // Verify code
    try {
      var response = await strapi
        .service("api::verification.verification")
        .verifyCode(verificationId, verificationCode, userId);

      return ctx.send(response);
    } catch (error) {
      return ctx.badRequest('Failed to verify code: ' + error.message);
    }
  },
};