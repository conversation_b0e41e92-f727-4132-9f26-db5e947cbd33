{"kind": "collectionType", "collectionName": "verifications", "info": {"singularName": "verification", "pluralName": "verifications", "displayName": "Verification", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"verificationId": {"type": "uid", "required": true}, "code": {"type": "string"}, "expiredAt": {"type": "datetime"}, "user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}