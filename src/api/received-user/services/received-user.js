'use strict';

/**
 * received-user service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService(
  "api::received-user.received-user",
  ({ strapi }) => ({
    /**
     * Add a user to the received users list
     * @param {number} scannerId - ID of the user who scanned the QR code
     * @param {number} scannedId - ID of the user who was scanned
     */
    async addReceivedUser(scannerId, scannedId) {
      // Check if this relationship already exists
      const existing = await strapi.db
        .query("api::received-user.received-user")
        .findOne({
          where: {
            scanner: scannerId,
            scanned: scannedId,
          },
        });

      if (existing) {
        // Update the existing record with new scan time
        return await strapi.db
          .query("api::received-user.received-user")
          .update({
            where: { id: existing.id },
            data: {
              scannedAt: new Date(),
            },
          });
      }

      // Create new record
      return await strapi.db.query("api::received-user.received-user").create({
        data: {
          scanner: { connect: [{ id: scannerId }] },
          scanned: { connect: [{ id: scannedId }] },
          scannedAt: new Date(),
        },
      });
    },

    /**
     * Get received users list for a specific user
     * @param {number} userId - ID of the user whose received list to get
     * @param {object} options - Query options (pagination, sorting, etc.)
     */
    async getReceivedUsers(userId, options = {}) {
      const { start = 0, limit = 25, sort = "scannedAt:desc" } = options;

      // Parse sort parameter to proper format for Strapi database query
      let orderBy = { scannedAt: "desc" }; // default
      if (sort) {
        const [field, direction] = sort.split(":");
        // Validate field name and direction
        const validFields = ["scannedAt", "createdAt", "updatedAt"];
        const validDirections = ["asc", "desc"];

        if (
          field &&
          direction &&
          validFields.includes(field) &&
          validDirections.includes(direction)
        ) {
          orderBy = { [field]: direction };
        }
      }

      return await strapi.db
        .query("api::received-user.received-user")
        .findMany({
          where: { scanner: userId },
          populate: {
            scanned: {
              fields: [
                "id",
                "username",
                "level",
                "gender",
                "title",
                "birthday",
              ],
              populate: {
                fakeAvatar: { fields: ["url", "id"] },
                asset_certification: {
                  fields: ["id", "issuedate", "expirydate", "asset_result"],
                },
              },
            },
          },
          orderBy,
          offset: start,
          limit,
        });
    },

    /**
     * Remove a user from the received users list
     * @param {number} scannerId - ID of the user who owns the list
     * @param {number} scannedId - ID of the user to remove
     */
    async removeReceivedUser(scannerId, scannedId) {
      return await strapi.db.query("api::received-user.received-user").delete({
        where: {
          scanner: scannerId,
          scanned: scannedId,
        },
      });
    },

    /**
     * Get count of received users for a specific user
     * @param {number} userId - ID of the user
     */
    async getReceivedUsersCount(userId) {
      return await strapi.db.query("api::received-user.received-user").count({
        where: { scanner: userId },
      });
    },
  })
); 