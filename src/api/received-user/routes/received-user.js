'use strict';

/**
 * received-user router
 */

const { createCoreRouter } = require('@strapi/strapi').factories;

module.exports = createCoreRouter("api::received-user.received-user", {
  config: {
    find: {
      auth: {
        scope: ["api::received-user.received-user.find"],
      },
    },
    create: {
      auth: {
        scope: ["api::received-user.received-user.create"],
      },
    },
    delete: {
      auth: {
        scope: ["api::received-user.received-user.delete"],
      },
    },
  },
}); 