'use strict';

/**
 * received-user controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController(
  "api::received-user.received-user",
  ({ strapi }) => ({
    /**
     * Get received users list for the authenticated user
     */
    async find(ctx) {
      try {
        const authUser = ctx.state.user;
        if (!authUser) {
          return ctx.unauthorized("Authentication required");
        }

        const { start, limit, sort } = ctx.query;
        const options = {
          start: start ? parseInt(start) : 0,
          limit: limit ? parseInt(limit) : 25,
          sort: sort || "scannedAt:desc",
        };

        const receivedUsers = await strapi
          .service("api::received-user.received-user")
          .getReceivedUsers(authUser.id, options);

        const count = await strapi
          .service("api::received-user.received-user")
          .getReceivedUsersCount(authUser.id);

        ctx.send({
          data: receivedUsers,
          meta: {
            pagination: {
              start: options.start,
              limit: options.limit,
              total: count,
            },
          },
        });
      } catch (error) {
        console.error("[received-user] Find error:", error);
        ctx.internalServerError("Failed to fetch received users");
      }
    },

    /**
     * Add a user to received users list (typically called after scanning)
     */
    async create(ctx) {
      try {
        const authUser = ctx.state.user;
        if (!authUser) {
          return ctx.unauthorized("Authentication required");
        }

        const { scannedUserId } = ctx.request.body;

        if (!scannedUserId) {
          return ctx.badRequest("Scanned user ID is required");
        }

        // Prevent adding self
        if (scannedUserId === authUser.id) {
          return ctx.badRequest("Cannot add yourself to received users list");
        }

        // Check if scanned user exists and is visible
        const scannedUser = await strapi.entityService.findOne(
          "plugin::users-permissions.user",
          scannedUserId,
          {
            fields: ["id", "visibility", "blocked", "deleted"],
          }
        );

        if (
          !scannedUser ||
          scannedUser.blocked ||
          scannedUser.deleted ||
          !scannedUser.visibility
        ) {
          return ctx.notFound("User not found or not visible");
        }

        const result = await strapi
          .service("api::received-user.received-user")
          .addReceivedUser(authUser.id, scannedUserId);

        ctx.send({
          data: result,
          message: "User added to received list successfully",
        });
      } catch (error) {
        console.error("[received-user] Create error:", error);
        ctx.internalServerError("Failed to add user to received list");
      }
    },

    /**
     * Remove a user from received users list
     */
    async delete(ctx) {
      try {
        const authUser = ctx.state.user;
        if (!authUser) {
          return ctx.unauthorized("Authentication required");
        }

        const { id } = ctx.params;

        // Get the received user record to verify ownership
        const receivedUser = await strapi.db
          .query("api::received-user.received-user")
          .findOne({
            where: { id },
            populate: { scanner: true, scanned: true },
          });

        if (!receivedUser) {
          return ctx.notFound("Received user record not found");
        }

        if (receivedUser.scanner.id !== authUser.id) {
          return ctx.forbidden("You can only delete your own received users");
        }

        await strapi
          .service("api::received-user.received-user")
          .removeReceivedUser(authUser.id, receivedUser.scanned.id);

        ctx.send({
          message: "User removed from received list successfully",
        });
      } catch (error) {
        console.error("[received-user] Delete error:", error);
        ctx.internalServerError("Failed to remove user from received list");
      }
    },
  })
); 