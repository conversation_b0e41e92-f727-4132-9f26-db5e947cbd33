// @ts-nocheck
'use strict';

/**
 * black controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const utils = require('@strapi/utils');
// @ts-ignore
const { ApplicationError, ValidationError } = utils.errors;
const { addToBlackList, removeFromBlackList } = require('../../../utils/redis')
const { getErrorMessage } = require('../../../utils')

module.exports = createCoreController('api::black.black', ({ strapi }) => ({
    async create(ctx) {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const { blackUserId } = ctx.request.body;
        if (!blackUserId) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const entry = await strapi.db.query('api::black.black').findOne({
            where: { user: { id: authUser.id }, blockUser: { id: blackUserId } }
        })
        if (!entry) {
            await strapi.db.query('api::black.black').create({
                data: {
                    user: authUser.id,
                    blockUser: blackUserId
                }
            });
        }
        await addToBlackList(authUser.id, blackUserId);
        await strapi.plugin('meedata').service('friendships').updateDeletedStatus(authUser.id, Number(blackUserId), true);
        return {
            ok: true
        }
    },

    async remove(ctx) {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const { blackUserId } = ctx.request.body;
        if (!blackUserId) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const entry = await strapi.db.query('api::black.black').findOne({
            where: { user: { id: authUser.id }, blockUser: { id: blackUserId } }
        })
        if (entry) {
            await strapi.entityService.delete('api::black.black', entry.id);
        }
        await removeFromBlackList(authUser.id, blackUserId);
        await strapi.plugin('meedata').service('friendships').updateDeletedStatus(authUser.id, Number(blackUserId), false);
        return {
            ok: true
        }
    },

    async find(ctx) {
        const authUser = ctx.state.user;

        const { offset = 0, limit = 20 } = ctx.query;
        const result = await strapi.db.query('api::black.black').findMany({
            where: { user: { id: authUser.id } },
            populate: {
                blockUser: {
                    select: ['id', 'username'],
                    populate: {
                        avatar: {
                            select: ['url']
                        },
                    }
                }
            },
            orderBy: { createdAt: 'desc' },
            offset: offset,
            limit: limit
        });
        return result.map(item => item.blockUser);
    }
}));
