'use strict';

/**
 * subscription-plan service
 */

const { createCoreService } = require('@strapi/strapi').factories;
const { sanitizeSubscriptionPlan } = require('../../../utils/locale');

module.exports = createCoreService('api::subscription-plan.subscription-plan', ({ strapi }) => ({
    async findActive(locale) {
        const result = await strapi.db.query('api::subscription-plan.subscription-plan').findMany({
            select: ['id', 'name', 'description', 'price', 'duration', 'locales'],
            where: { status: 1 },
        });
        return await sanitizeSubscriptionPlan(result, locale);
    },

    async findOne(id) {
        return await strapi.db.query('api::subscription-plan.subscription-plan').findOne({
            select: ['id', 'name', 'price', 'duration'],
            where: { id, status: 1 }
        });
    }
}));
