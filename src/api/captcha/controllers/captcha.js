// @ts-nocheck
'use strict';

const _ = require('lodash');
const utils = require('@strapi/utils');
const { ApplicationError, ValidationError } = utils.errors;
const { getErrorMessage } = require('../../../utils');

module.exports = {
  request: async function (ctx) {
    const locale = ctx.state.locale;
    const { countryCode, phone, verificationEmail } = ctx.request.body;

    if (_.has(ctx.request.body, 'verificationEmail')) {
      return await strapi.service("api::captcha.captcha").sendEmailVerificationCode(verificationEmail);

    } else if (_.has(ctx.request.body, 'countryCode') && _.has(ctx.request.body, 'phone')) {
      return await strapi.service("api::captcha.captcha").sendPhoneVerificationCode(countryCode, phone, locale);
    }

    throw new ValidationError(getErrorMessage('error.validated', locale));
  },

  async verify(ctx) {
    const locale = ctx.state.locale;
    const { countryCode, phone, verificationEmail, code } = ctx.request.body;
    if (_.has(ctx.request.body, 'verificationEmail') && _.has(ctx.request.body, 'code')) {
      return await strapi.service("api::captcha.captcha").verifyEmailVerificationCode(verificationEmail, code, locale);

    } else if (_.has(ctx.request.body, 'countryCode') && _.has(ctx.request.body, 'phone') && _.has(ctx.request.body, 'code')) {
      return await strapi.service("api::captcha.captcha").verifyPhoneVerificationCode(countryCode, phone, code, locale);
    }

    throw new ValidationError(getErrorMessage('error.validated', locale));
  }
}
