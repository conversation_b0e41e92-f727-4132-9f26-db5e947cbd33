// @ts-nocheck
'use strict';
const otpGenerator = require('otp-generator');
const fs = require("fs");
const path = require("path");
const x = require("axios");
const sendSMS = require('../../../utils/snsClient');
const dayjs = require('dayjs');
const utils = require('@strapi/utils');
const { getErrorMessage } = require('../../../utils');
const { ApplicationError, ValidationError } = utils.errors;


module.exports = {

    async sendEmailVerificationCode(email) {
        const randomOTP = otpGenerator.generate(6, {
            lowerCaseAlphabets: false,
            upperCaseAlphabets: false,
            specialChars: false
        }).toString();

        const filePath = path.join(__dirname, 'html/email.html');
        let html = fs.readFileSync(filePath, 'utf8');
        html = html.replace('%code', randomOTP);
        await strapi.plugins['email'].services.email.send({
            to: email,
            from: { name: 'Meetok User center', email: '<EMAIL>' },
            subject: ' Account Registration Verification Code',
            html: html
        });
        const expire = 86400;
        await strapi.redis.connections.default.client.setex(`captcha:${email}`, expire, randomOTP);
        return {
            period: expire,
            emailSentAt: dayjs().format('YYYY-MM-DDTHH:mm:ss.SSS[Z]')
        }
    },

    async verifyEmailVerificationCode(verificationEmail, code, locale) {
        //191919 is a super CAPTCHA.
        if (code != '191919') {
            const result = await strapi.redis.connections.default.client.get(`captcha:${verificationEmail}`);
            if (!result || result != code) {
                throw new ValidationError(getErrorMessage('invalid.verification.code', locale));
            }
            await strapi.redis.connections.default.client.setex(`captcha:verify:${verificationEmail}_${code}`, 1800, true);
        }
        return {
            ok: true
        }
    },

    async sendPhoneVerificationCode(countryCode, phone, locale) {
        const result = await strapi.redis.connections.default.client.get(`captcha:${countryCode}${phone}`);
        if (result) {
            throw new ApplicationError(getErrorMessage('rate.limit', locale));
        }
        const randomOTP = otpGenerator.generate(6, {
            lowerCaseAlphabets: false,
            upperCaseAlphabets: false,
            specialChars: false
        }).toString();

        let text = `[MeeTok] verification code: ${randomOTP}`;
        if (countryCode == '82') {
            text = `[MeeTok] Mã kiểm chứng: ${randomOTP}`;
        }
        // await sendSMS(countryCode, phone, text);
        const expire = 180;
        await strapi.redis.connections.default.client.setex(`captcha:${countryCode}${phone}`, expire, randomOTP);
        return {
            period: expire,
        }
    },

    async verifyPhoneVerificationCode(phoneCountryCode, phoneNumber, code, locale) {
        //191919 is a super CAPTCHA.
        if (code != '191919') {
            const result = await strapi.redis.connections.default.client.get(`captcha:${phoneCountryCode}${phoneNumber}`);
            if (!result || result != code) {
                throw new ValidationError(getErrorMessage('invalid.verification.code', locale));
            }
            await strapi.redis.connections.default.client.setex(`captcha:verify:${phoneCountryCode}${phoneNumber}_${code}`, 1800, true);
        }
        return {
            ok: true
        }
    }
}
