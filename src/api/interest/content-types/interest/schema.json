{"kind": "collectionType", "collectionName": "interests", "info": {"singularName": "interest", "pluralName": "interests", "displayName": "Interest", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"tag": {"type": "string"}, "name": {"type": "string"}, "users": {"type": "relation", "relation": "manyToMany", "target": "plugin::users-permissions.user", "mappedBy": "interests"}, "source": {"type": "enumeration", "enum": ["system", "user"], "default": "system"}, "locales": {"type": "json"}, "category": {"type": "relation", "relation": "manyToOne", "target": "api::interest-category.interest-category", "inversedBy": "interests"}, "order": {"type": "integer"}}}