// @ts-nocheck
'use strict';

/**
 * interest controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::interest.interest', ({ strapi }) => ({
    //
    async find(ctx) {
        const locale = ctx.state.locale;
        const entries = await strapi.db.query('api::interest.interest').findMany({
            select: ["id", "name", "tag", "locales", 'source'],
            offset: 0,
            limit: 30
        });
        return entries.map(interest => {
            return {
                id: interest.id,
                name: interest.locales?.[locale] || interest.name,
                tag: interest.tag,
                source: interest.source
            }
        });
    }
}));
