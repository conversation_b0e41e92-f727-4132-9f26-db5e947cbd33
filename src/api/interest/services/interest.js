'use strict';

/**
 * interest service
 */

const { createCoreService } = require('@strapi/strapi').factories;
const { INTEREST_TYPE } = require('../../../constants')

module.exports = createCoreService('api::interest.interest', ({ strapi }) => ({
    async getVailable(data) {
        const interests = [];
        const userInterests = [];
        data.forEach(interest => {
            !interest.id || interest.id === 0 ? userInterests.push(interest) : interests.push(interest);
        })
        if (userInterests.length > 0) {
            const insertInterests = await Promise.all(userInterests.map(async (interest) => {
                return await strapi.db.query('api::interest.interest').create({
                    data: {
                        name: interest.name,
                        tag: interest.name,
                        order: 999,
                        source: INTEREST_TYPE.user
                    }
                });
            }))
            interests.push(...insertInterests);
        }
        return interests;
    },
}));
