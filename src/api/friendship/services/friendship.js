'use strict';

/**
 * friendship service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::friendship.friendship', ({ strapi }) => ({

    /**
     * @param {any} user
     * @param {any} friend
     */
    async save(user, friend) {
        const dataArr = [];
        dataArr.push({
            user: { disconnect: [], connect: [{ id: user }] },
            friend: { disconnect: [], connect: [{ id: friend }] }
        });
        dataArr.push({
            user: { disconnect: [], connect: [{ id: friend }] },
            friend: { disconnect: [], connect: [{ id: user }] }
        });
        const result = await Promise.all(dataArr.map(async data => {
            return await strapi.db.query('api::friendship.friendship').create({
                data
            });
        }));
        //Removed from the got or sent list. 
        await strapi.plugin('meedata').service('requests').markAsInvalid(user, friend);
        //Removed from the rating list. 
        await strapi.plugin('meedata').service('ratings').markAsInvalid(user, friend, true);
        return result;
    },
}));
