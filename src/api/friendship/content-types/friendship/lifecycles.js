module.exports = {
    async beforeCreate(event) {
        const { user, friend } = event.params.data;
        //TODO
        event.state = {
            user: user.connect[0].id,
            friend: friend.connect[0].id
        }
    },
    async afterCreate(event) {
        const { result, state } = event;
        if (state) {
            try {
                await strapi.plugin('meedata').service('friendships').sync({
                    ...state,
                    relatedId: result.id
                });
            } catch (error) {
                strapi.log.error("========= Failed to synchronize the friendship data ============");
            }
        }
    },
}