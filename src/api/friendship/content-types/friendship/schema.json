{"kind": "collectionType", "collectionName": "friendships", "info": {"singularName": "friendship", "pluralName": "friendships", "displayName": "friendship", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "friend": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}}}