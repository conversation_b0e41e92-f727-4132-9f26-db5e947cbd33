{"kind": "collectionType", "collectionName": "transactions", "info": {"singularName": "transaction", "pluralName": "transactions", "displayName": "transaction", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string"}, "targetName": {"type": "string"}, "customer": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "type": {"type": "string"}, "amount": {"type": "integer"}, "balance": {"type": "integer"}, "related": {"type": "string"}, "ex": {"type": "json"}}}