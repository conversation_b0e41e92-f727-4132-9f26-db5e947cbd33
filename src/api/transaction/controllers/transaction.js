'use strict';

/**
 * transaction controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::transaction.transaction', ({ strapi }) => ({
    async find(ctx) {
        const authUser = ctx.state.user;
        const { offset = 0, limit = 20 } = ctx.query;
        return await strapi.db.query('api::transaction.transaction').findMany({
            select: ['type', 'amount', 'balance', 'targetName', 'createdAt'],
            where: { customer: { id: authUser.id } },
            orderBy: { id: 'DESC' },
            offset,
            limit
        });
    }
}));
