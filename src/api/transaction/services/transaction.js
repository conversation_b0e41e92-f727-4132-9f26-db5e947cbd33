'use strict';
const { getBalance, incrBalance } = require('../../../utils/redis')
const { NOTIFICATION_TYPE } = require('../../../plugins/strapi-notification/server/constants')
const utils = require('@strapi/utils');
// @ts-ignore
const { ApplicationError } = utils.errors;
const { getErrorMessage } = require('../../../utils')
/**
 * transaction service
 */

const { createCoreService } = require('@strapi/strapi').factories;
const { TRANSACTION, PRICES } = require("../../../constants");

module.exports = createCoreService('api::transaction.transaction', ({ strapi }) => ({

    async executePayment(customer, transactionType, amount, related, targetdUserId) {
        const locale = strapi.requestContext.get().state.locale;

        const balance = await getBalance(customer);
        if (balance < amount) {
            throw new ApplicationError(getErrorMessage('insufficient.balance', locale));
        }
        let targetName = '';
        if (targetdUserId) {
            const targetdUser = await strapi.plugin('meedata').service('users').findOne(targetdUserId);
            if (targetdUser) {
                targetName = targetdUser.username;
            }
        }
        const latestBalance = await incrBalance(customer, -Math.abs(amount));
        // @ts-ignore
        strapi.service('plugin::users-permissions.user').editBalance(customer, latestBalance);

        const data = {
            name: transactionType.name,
            customer,
            type: transactionType.type,
            amount: -Math.abs(amount),
            balance: latestBalance,
            targetName,
            related
        }
        return await strapi.entityService.create('api::transaction.transaction', {
            data
        });
    },


    async executeBonus(customer, transactionType, amount, related, targetdUserId) {
        let targetName = '';
        if (targetdUserId) {
            const targetdUser = await strapi.plugin('meedata').service('users').findOne(targetdUserId);
            if (targetdUser) {
                targetName = targetdUser.username;
            }
        }
        const latestBalance = await incrBalance(customer, amount);
        // @ts-ignore
        strapi.service('plugin::users-permissions.user').editBalance(customer, latestBalance);
        const data = {
            name: transactionType.name,
            customer,
            type: transactionType.type,
            amount: amount,
            balance: latestBalance,
            targetName,
            related
        }
        const result = await strapi.entityService.create('api::transaction.transaction', {
            data
        });
        //send bonus push
        const msg = {
            type: NOTIFICATION_TYPE.BONUS,
            user: { id: customer }
        }
        await strapi.plugin('mq').service('publish').sendNotificationMessage(msg);
        return result;
    },

    /**
     * @param {any} customer
     * @param {any} related
     * @param {any} targetdUserId
     */
    async executeHeartCompensation(customer, related, targetdUserId) {
        let targetName = '';
        if (targetdUserId) {
            const targetdUser = await strapi.plugin('meedata').service('users').findOne(targetdUserId);
            if (targetdUser) {
                targetName = targetdUser.username;
            }
        }
        const amount = PRICES.DOUBLE_HEART_REFUND;
        const latestBalance = await incrBalance(customer, amount);
        // @ts-ignore
        strapi.service('plugin::users-permissions.user').editBalance(customer, latestBalance);
        const data = {
            name: TRANSACTION.DOUBLE_HEART_REFUND.name,
            customer,
            type: TRANSACTION.DOUBLE_HEART_REFUND.type,
            amount: amount,
            balance: latestBalance,
            targetName,
            related
        }
        return await strapi.entityService.create('api::transaction.transaction', {
            data
        });
    },

    async recharge(customer, recharge) {
        const latestBalance = await incrBalance(customer, recharge.amount);
        // @ts-ignore
        strapi.service('plugin::users-permissions.user').editBalance(customer, latestBalance);
        const data = {
            name: TRANSACTION.RECHARGE.name,
            customer,
            type: TRANSACTION.RECHARGE.type,
            amount: recharge.amount,
            balance: latestBalance,
            related: `api::recharge.recharge:${recharge.id}`
        }
        return await strapi.entityService.create('api::transaction.transaction', {
            data
        });
    },

    async deposit(customer, amount, note) {
        amount = Math.abs(amount);
        const latestBalance = await incrBalance(customer, amount);
        strapi.service('plugin::users-permissions.user').editBalance(customer, latestBalance);
        const data = {
            name: TRANSACTION.DEPOSIT.name,
            customer,
            type: TRANSACTION.DEPOSIT.type,
            amount: amount,
            balance: latestBalance,
            ex: { note }
        }
        const result = await strapi.entityService.create('api::transaction.transaction', {
            data
        });
        //send bonus push
        const msg = {
            type: NOTIFICATION_TYPE.BONUS,
            user: { id: customer },
            note
        }
        await strapi.plugin('mq').service('publish').sendNotificationMessage(msg);
        return result;
    },

    async deduct(customer, amount, note) {
        amount = -Math.abs(amount);
        const latestBalance = await incrBalance(customer, amount);
        strapi.service('plugin::users-permissions.user').editBalance(customer, latestBalance);
        const data = {
            name: TRANSACTION.DEDUCTION.name,
            customer,
            type: TRANSACTION.DEDUCTION.type,
            amount: amount,
            balance: latestBalance,
            ex: { note }
        }
        const result = await strapi.entityService.create('api::transaction.transaction', {
            data
        });
        return result;
    },

    /**
     * @param {any} id
     */
    async edit(id, params = {}) {
        return await strapi.entityService.update('api::transaction.transaction', id, {
            data: params
        });
    },
}));
