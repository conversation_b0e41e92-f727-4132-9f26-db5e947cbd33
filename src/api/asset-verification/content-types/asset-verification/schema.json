{"kind": "collectionType", "collectionName": "asset_verifications", "info": {"singularName": "asset-verification", "pluralName": "asset-verifications", "displayName": "AssetVerification", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"proof": {"type": "media", "multiple": true, "required": true, "allowedTypes": ["images", "files"]}, "type": {"type": "enumeration", "enum": ["RealEstate", "FinancialAssets", "CryptoCurrency", "Others"], "required": true, "default": "Others"}, "name": {"type": "string", "required": true}, "description": {"type": "text"}, "user": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.user", "inversedBy": "asset_verifications"}}}