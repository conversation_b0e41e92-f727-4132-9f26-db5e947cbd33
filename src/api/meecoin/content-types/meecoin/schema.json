{"kind": "collectionType", "collectionName": "me<PERSON>oins", "info": {"singularName": "<PERSON><PERSON><PERSON>n", "pluralName": "me<PERSON>oins", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"productId": {"type": "string"}, "totalAmount": {"type": "integer"}, "amount": {"type": "integer"}, "bonus": {"type": "integer"}, "discount": {"type": "decimal"}, "level": {"type": "enumeration", "enum": ["practical", "popular"]}, "price": {"type": "decimal"}, "status": {"type": "boolean", "default": true, "private": true}, "order": {"type": "integer"}, "name": {"type": "string"}, "recharges": {"type": "relation", "relation": "oneToMany", "target": "api::recharge.recharge", "mappedBy": "product"}}}