'use strict';

/**
 * meecoin controller
 */

const { createCoreController } = require('@strapi/strapi').factories;

module.exports = createCoreController('api::meecoin.meecoin', ({ strapi }) => ({
    async find(ctx) {
        return await strapi.db.query('api::meecoin.meecoin').findMany({
            where: { status: true },
            orderBy: {
                order: 'asc'
            }
        });
    }
}));
