'use strict';

/**
 * recharge service
 */
const { X509Certificate } = require("crypto");
const jose = require("jose");
const { v4: uuidv4 } = require('uuid');


const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::recharge.recharge', ({ strapi }) => ({

    async decodeNotificationPayload(token, rootCertFingerprint = '63:34:3A:BF:B8:9A:6A:03:EB:B5:7E:9B:3F:5F:A7:BE:7C:4F:5C:75:6F:30:17:B3:A8:C4:88:C3:65:3E:91:79') {
        const getKey = async (protectedHeader, _token) => {
            const certs = protectedHeader.x5c?.map(c => `-----BEGIN CERTIFICATE-----\n${c}\n-----END CERTIFICATE-----`) ?? []
            this.validateCertificates(certs, rootCertFingerprint);
            return jose.importX509(certs[0], "ES256")
        }
        const { payload } = await jose.compactVerify(token, getKey)
        const decoded = new TextDecoder().decode(payload);
        return JSON.parse(decoded);
    },

    validateCertificates(certificates, rootCertFingerprint) {
        if (certificates.length === 0) throw new Error()
        const x509certs = certificates.map(c => new X509Certificate(c))
        const now = new Date()
        const datesValid = x509certs.every(c => new Date(c.validFrom) < now && now < new Date(c.validTo))
        if (!datesValid) throw new Error()
        // Check that each certificate, except for the last, is issued by the subsequent one.
        if (certificates.length >= 2) {
            for (let i = 0; i < x509certs.length - 1; i++) {
                const subject = x509certs[i]
                const issuer = x509certs[i + 1]

                if (subject.checkIssued(issuer) === false || subject.verify(issuer.publicKey) === false) {
                    throw new Error()
                }
            }
        }
        // Ensure that the last certificate in the chain is the expected root CA.
        if (x509certs[x509certs.length - 1].fingerprint256 !== rootCertFingerprint) {
            throw new Error()
        }
    },

    async test() {
        const now = new Date();
        const expiry = new Date(now.getTime() + 3600 * 1000);
        const expirySeconds = Math.floor(expiry.getTime() / 1000);
        const payload = {
            bid: 'com.meetok.www',
            nonce: uuidv4()
        }
**************************************************************************************************************************************************************************************************************************************************************************************************************************
        const jwt = await new jose.SignJWT(payload)
            .setProtectedHeader({ alg: "ES256", kid: '', typ: "JWT" })
            .setIssuer('69a6de93-cc9e-47e3-e053-5b8c7c11a4d1')
            .setIssuedAt()
            .setExpirationTime(expirySeconds)
            .setAudience("appstoreconnect-v1")
            .sign(await jose.importPKCS8(privateKey, 'ES256'))
        return jwt;
    }
}));
