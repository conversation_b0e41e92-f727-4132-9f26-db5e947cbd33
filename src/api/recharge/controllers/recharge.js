'use strict';

/**
 * recharge controller
 */

const utils = require('@strapi/utils');
// @ts-ignore
const { ApplicationError, ValidationError, NotFoundError } = utils.errors;
const { createCoreController } = require('@strapi/strapi').factories;
const { PAYMENT_METHOD, PAYMENT_STATUS } = require("../../../constants");
const { getErrorMessage } = require('../../../utils')
const path = require('path');
const { google } = require('googleapis');
const { re } = require('semver');
const publisher = google.androidpublisher('v3');

module.exports = createCoreController('api::recharge.recharge', ({ strapi }) => ({
    async generateOrder(ctx) {
        const locale = ctx.state.locale;
        const authUser = ctx.state.user;
        const { productId, paymentMethod } = ctx.request.body;
        if (!productId || !paymentMethod || ![PAYMENT_METHOD.APPLE, PAYMENT_METHOD.GOOGLE].includes(paymentMethod)) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const product = await strapi.db.query('api::meecoin.meecoin').findOne({
            where: { productId, status: true },
        });
        if (!product) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const entry = await strapi.db.query('api::recharge.recharge').create({
            data: {
                customer: authUser.id,
                product: product.id,
                productBak: product,
                paymentMethod,
                amount: product.totalAmount,
                status: PAYMENT_STATUS.PENDING
            },
        });
        return {
            rechargeId: entry.rechargeId,
            paymentMethod: entry.paymentMethod,
            status: entry.status,
            amount: entry.amount,
            meecoin: product
        }
    },

    async sandboxNotification(ctx) {
        const { signedPayload } = ctx.request.body;
        // @ts-ignore
        const payload = await strapi.service('api::recharge.recharge').decodeNotificationPayload(signedPayload);
        await strapi.entityService.create('api::apple-notification.apple-notification', { data: { signedPayload, payload } });
        if (payload.notificationType === 'REFUND') {

        }
        return { ok: true }
    },

    async notification(ctx) {
        const { signedPayload } = ctx.request.body;
        // @ts-ignore
        const payload = await strapi.service('api::recharge.recharge').decodeNotificationPayload(signedPayload);
        await strapi.entityService.create('api::apple-notification.apple-notification', { data: { signedPayload, payload } });
        if (payload.notificationType === 'REFUND') {

        }
        return { ok: true }
    },

    async verifyReceipt(ctx) {
        const locale = ctx.state.locale;
        const authUser = ctx.state.user;
        const { rechargeId, serverVerificationData } = ctx.request.body;
        if (!rechargeId || !serverVerificationData)
            throw new ValidationError(getErrorMessage('error.validated', locale))

        const recharge = await strapi.db.query('api::recharge.recharge').findOne({
            where: { rechargeId, paymentMethod: PAYMENT_METHOD.APPLE }
        });
        if (!recharge) throw new ValidationError(getErrorMessage('error.validated', locale))

        if ([PAYMENT_STATUS.PAYMENT, PAYMENT_STATUS.REFUND].includes(recharge.status))
            throw new ValidationError(getErrorMessage('error.validated', locale))

        if (recharge.transactionId)
            throw new ValidationError(getErrorMessage('error.validated', locale))

        // @ts-ignore
        let payload = await strapi.service('api::third-party.third-party').appVerify(serverVerificationData, 1);
        if (!payload) throw new ApplicationError(getErrorMessage('error.occurred', locale));

        let status = payload.status;
        if (status == 21007) {
            // @ts-ignore
            payload = await strapi.service('api::third-party.third-party').appVerify(serverVerificationData, 0);
            status = payload.status;
        }
        if (status != 0)
            throw new ApplicationError(getErrorMessage('error.occurred', locale));

        const productId = recharge.productBak.productId;
        const inAppTransactionId = payload.receipt?.in_app[0]?.transaction_id;
        const inAppProductId = payload.receipt?.in_app[0]?.product_id;

        if (inAppProductId != productId)
            throw new ApplicationError(getErrorMessage('error.occurred', locale));

        const sameTransactionId = await strapi
            .query('api::recharge.recharge')
            .findOne({ where: { transactionId: inAppTransactionId } });

        if (sameTransactionId)
            throw new ApplicationError(getErrorMessage('error.occurred', locale));

        await strapi.db.query('api::recharge.recharge').update({
            where: { id: recharge.id },
            data: { status: PAYMENT_STATUS.PAYMENT, ex: { serverVerificationData }, transactionId: inAppTransactionId }
        });
        // @ts-ignore
        const result = await strapi.service('api::transaction.transaction').recharge(authUser.id, recharge);
        return {
            balance: result.balance
        }
    },

    async googleVerify(ctx) {
        const locale = ctx.state.locale;
        const authUser = ctx.state.user;

        const { rechargeId, verificationData } = ctx.request.body;
        if (!rechargeId || !verificationData)
            throw new ValidationError(getErrorMessage('error.validated', locale))

        const recharge = await strapi.db.query('api::recharge.recharge').findOne({
            where: { rechargeId, paymentMethod: PAYMENT_METHOD.GOOGLE }
        });
        if (!recharge) throw new ValidationError(getErrorMessage('error.validated', locale))

        if ([PAYMENT_STATUS.PAYMENT, PAYMENT_STATUS.REFUND].includes(recharge.status))
            throw new ValidationError(getErrorMessage('error.validated', locale))

        if (recharge.transactionId)
            throw new ValidationError(getErrorMessage('error.validated', locale));

        if (recharge.productBak.productId != verificationData.productId)
            throw new ApplicationError(getErrorMessage('error.occurred', locale));

        const auth = new google.auth.GoogleAuth({
            keyFile: path.join(__dirname, 'meetok-396106-47c8a29d989c.json'),
            scopes: ['https://www.googleapis.com/auth/androidpublisher'],
        });
        const client = await auth.getClient();
        const publisher = google.androidpublisher({
            version: "v3",
            auth: client
        })
        const { data } = await publisher.purchases.products.get({ packageName: verificationData.packageName, productId: verificationData.productId, token: verificationData.purchaseToken });
        const { orderId, purchaseState } = data;
        if (purchaseState != 0) {
            throw new ApplicationError(getErrorMessage('error.occurred', locale));
        }
        const sameOrder = await strapi
            .query('api::recharge.recharge')
            .findOne({ where: { transactionId: orderId } });
        if (sameOrder)
            throw new ApplicationError(getErrorMessage('error.occurred', locale));

        await strapi.db.query('api::recharge.recharge').update({
            where: { id: recharge.id },
            data: { status: PAYMENT_STATUS.PAYMENT, ex: { verificationData, data }, transactionId: orderId }
        });
        // @ts-ignore
        const result = await strapi.service('api::transaction.transaction').recharge(authUser.id, recharge);
        return {
            balance: result.balance
        }
    }
}));
