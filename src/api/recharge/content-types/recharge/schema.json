{"kind": "collectionType", "collectionName": "recharges", "info": {"singularName": "recharge", "pluralName": "recharges", "displayName": "recharge", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"customer": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "product": {"type": "relation", "relation": "manyToOne", "target": "api::meecoin.meecoin", "inversedBy": "recharges"}, "rechargeId": {"type": "string"}, "paymentMethod": {"type": "string"}, "transactionId": {"type": "string"}, "amount": {"type": "integer"}, "status": {"type": "string"}, "productBak": {"type": "json"}, "ex": {"type": "json", "private": true}}}