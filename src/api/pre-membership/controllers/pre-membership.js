// @ts-nocheck
'use strict';

/**
 * pre-membership controller
 */

const { createCoreController } = require('@strapi/strapi').factories;
const utils = require('@strapi/utils');
const { ApplicationError, ValidationError } = utils.errors;
const { formatPhoneNumber, getErrorMessage } = require('../../../utils')
const XLSX = require('xlsx');
const dayjs = require("dayjs");


module.exports = createCoreController('api::pre-membership.pre-membership', ({ strapi }) => ({
    async checkNickname(ctx) {
        const locale = ctx.state.locale;
        const { nickname } = ctx.query;
        if (!nickname) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const count = await strapi.db.query('api::pre-membership.pre-membership').count({
            where: { nickname: nickname.trim() }
        });
        return {
            exists: count > 0
        }
    },

    async checkReferrer(ctx) {
        const locale = ctx.state.locale;
        const { referrer } = ctx.query;
        if (!referrer) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        const count = await strapi.db.query('api::pre-membership.pre-membership').count({
            where: { nickname: referrer.trim() }
        });
        return {
            exists: count > 0
        }
    },

    async create(ctx) {
        const locale = ctx.state.locale;
        let { realname, nickname, university, email, countryCode, phone, referrer } = ctx.request.body;
        if (!realname || !nickname || !university || !email || !countryCode || !phone) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }
        nickname = nickname.trim();
        referrer = referrer.trim();
        email = email.trim();

        const { isValid, phoneCountryCode, phoneNumber } = formatPhoneNumber(countryCode, phone);
        if (!isValid) throw new ApplicationError(getErrorMessage('invalid.number', locale));


        //check university and email
        const universityEntry = await strapi.db.query('api::university.university').findOne({
            where: { id: university.id }
        });
        if (!universityEntry) {
            throw new ValidationError(getErrorMessage('error.validated', locale));
        }

        //check referrer
        if (referrer) {
            const referrerEntry = await strapi.db.query('api::pre-membership.pre-membership').findOne({
                where: { nickname: referrer.trim() }
            });
            if (!referrerEntry) {
                throw new ValidationError(getErrorMessage('invalid.referercode', locale));
            }
        }

        //check nickname phone and email
        const entry = await strapi.db.query('api::pre-membership.pre-membership').findOne({
            where: {
                $or: [
                    { nickname },
                    { email },
                    {
                        countryCode: phoneCountryCode,
                        phone: phoneNumber
                    }
                ]
            }
        });
        if (entry) {
            if (entry.nickname === nickname) {
                throw new ApplicationError(getErrorMessage('duplicate.username', locale))
            } else if (entry.email === email) {
                throw new ApplicationError(getErrorMessage('duplicate.email', locale))
            } else {
                throw new ApplicationError(getErrorMessage('duplicate.phone', locale))
            }
        }
        const data = {
            ...ctx.request.body,
            realname,
            nickname,
            email,
            countryCode: phoneCountryCode,
            phone: phoneNumber,
            referrer
        }
        await strapi.entityService.create('api::pre-membership.pre-membership', {
            data: data
        });
        return {
            ok: true
        }
    },

    async exportToExcel(ctx) {
        //users worksheet
        const entries = await strapi.db.query('api::pre-membership.pre-membership').findMany({
            populate: {
                university: true,
                photos: true
            }
        });
        const users = entries.map(entry => {
            return {
                ID: entry.id,
                NICKNAME: entry.nickname,
                REALNAME: entry.realname,
                EMAIL: entry.email,
                COUNTRY_CODE: entry.countryCode,
                PHONE_NUMBER: entry.phone,
                REFERRER: entry.referrer,
                UNIVERSITY: entry.university.name,
                REGISTER_TIME: entry.createdAt,
                PHOTOS: entry.photos?.map(photo => photo.url).join('\n')
            }
        })
        const workbook = XLSX.utils.book_new();
        const worksheet1 = XLSX.utils.json_to_sheet(users);
        worksheet1["!cols"] = [
            { wch: 8 },
            { wch: 20 },
            { wch: 20 },
            { wch: 30 },
            { wch: 20 },
            { wch: 20 },
            { wch: 20 },
            { wch: 30 },
            { wch: 24 },
            { wch: 160 },
        ];
        XLSX.utils.book_append_sheet(workbook, worksheet1, 'users');

        //statistics worksheet
        const res = await strapi.db.connection
            .select('university_id')
            .count('id as count')
            .from('pre_memberships_university_links')
            .groupBy('university_id')
        let universities = await strapi.db.query('api::university.university').findMany({
            select: ["id", "name"]
        });
        universities = universities.map(university => {
            return {
                NAME: university.name,
                COUNT: res.find(ele => ele.university_id == university.id)?.count || "0"
            }
        })
        const worksheet2 = XLSX.utils.json_to_sheet(universities);
        worksheet2["!cols"] = [
            { wch: 60 },
            { wch: 10 }
        ];
        XLSX.utils.book_append_sheet(workbook, worksheet2, 'statistics');

        const excelData = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
        const filename = `MeeTok-pre-registration-${dayjs().format('YYYY-MM-DDHH:mm')}`;
        ctx.set('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        ctx.set('Content-Disposition', `attachment; filename=${filename}.xlsx`);
        return excelData;
    }
}));
