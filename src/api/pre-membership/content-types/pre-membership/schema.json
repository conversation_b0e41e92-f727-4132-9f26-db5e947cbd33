{"kind": "collectionType", "collectionName": "pre_memberships", "info": {"singularName": "pre-membership", "pluralName": "pre-memberships", "displayName": "Pre-membership", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"realname": {"type": "string"}, "nickname": {"type": "string"}, "university": {"type": "relation", "relation": "oneToOne", "target": "api::university.university"}, "email": {"type": "email"}, "countryCode": {"type": "string"}, "phone": {"type": "string"}, "photos": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "referrer": {"type": "string"}}}