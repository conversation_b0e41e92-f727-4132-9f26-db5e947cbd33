// @ts-nocheck
'use strict';

/**
 * pre-membership service
 */

const { createCoreService } = require('@strapi/strapi').factories;

module.exports = createCoreService('api::pre-membership.pre-membership', ({ strapi }) => ({
    async checkPreRegisteredUser(countryCode, phone) {
        const entry = await strapi.db.query('api::pre-membership.pre-membership').findOne({
            where: {
                countryCode, phone
            },
            populate: {
                photos: {
                    count: true
                }
            }
        });
        return {
            isPreRegistered: entry != null,
            photos: entry?.photos?.count || 0,
            id: entry?.id
        }
    }
}));
