"use strict";
const AVATAR = {
  DEFAULT: 1,
  //DEFAULT_URL: "https://an2-dev-atreez.s3.ap-northeast-2.amazonaws.com/2025/03/05/av_e22d4ee4dd.png",
  //LOGO: 3,
  LOGO_URL:
    "https://an2-dev-atreez.s3.ap-northeast-2.amazonaws.com/2025/03/05/logo_4970881c93.png",
  EVAL_URL:
    "https://an2-dev-atreez.s3.ap-northeast-2.amazonaws.com/2025/03/05/highevaluation_d6eccfbddc.png",
};

const VERIFICATION_STATUS = {
  incomplete: "incomplete",
  pending: "pending",
  verified: "verified",
  rejected: "rejected",
};

const VERIFICATION_TYPE = {
  email: "email",
  certificate: "certificate",
};

const LOCKS = {
  HEART: "HEART",
  EVALUATION: "EVAL",
  RECOMMEND: "RRC",
};

const HEART_LEVEL = {
  LOW: "low",
  HIGHT: "high",
};

const RECOMMEND_TYPE = {
  STANDARD: "standard",
  PREMIUM: "premium",
};

const CARD_COLLECTION = {
  TODAY: "today",
  PASSED: "passed",
};

const USER_STATUS = {
  UNDERGRADUATE: "UNDERGRADUATE",
  GRADUATE: "GRADUATE",
};

const TRANSACTION = {
  RECHARGE: {
    type: "RECHARGE",
    name: "Recharge",
  },
  UNLOCK_EVALUATION: {
    type: "UNLOCK_EVAL",
    name: "Unlock High evaluation Card",
  },
  UNLOCK_HEART: {
    type: "UNLOCK_HEART",
    name: "Unlock Heart card",
  },
  UNLOCK_PAST_RECOMMENDATION: {
    type: "UNLOCK_PAST_REC",
    name: "Unlock Given card",
  },
  SENT_HEART: {
    type: "SENT_HEART",
    name: "Sent Heart",
  },
  SENT_DOUBLE_HEART: {
    type: "SENT_DOUBLE_HEART",
    name: "Sent Double Heart",
  },
  PREMIUM_RECOMMENDATION: {
    type: "PREMIUM_REC",
    name: "Buy Premium cards",
  },
  DOUBLE_HEART_REFUND: {
    type: "DOUBLE_HEART_REFUND",
    name: "Double heart payback",
  },
  EVALUATE_BONUS: {
    type: "EVAL_BONUS",
    name: "Evaluate",
  },
  PRE_REG_BONUS: {
    type: "PRE_REG_BONUS",
    name: "Pre-subscribers",
  },
  RECOMMENDER: {
    type: "REF_1_BONUS",
    name: "Recommender",
  },
  RECOMMENDED: {
    type: "REF_2_BONUS",
    name: "Recommended subscriber",
  },
  WITHDRAWAL: {
    type: "WITHDRAWAL",
    name: "withdrawal",
  },
  DEDUCTION: {
    type: "DEDUCTION",
    name: "deduction",
  },
  DEPOSIT: {
    type: "DEPOSIT",
    name: "deposit",
  },
  REFUND: {
    type: "REFUND",
    name: "refund",
  },
  POST_BONUS: {
    type: "POST_BONUS",
    name: "Top Post",
  },
};

const PRICES = {
  UNLOCK_EVALUATION: 20,
  UNLOCK_HEART: 0,
  UNLOCK_PAST_RECOMMENDATION: 1,
  SENT_HEART: 20,
  SENT_DOUBLE_HEART: 100,
  PREMIUM_RECOMMENDATION: 50,
  EVALUATE_BONUS: 1,
  PRE_REG_BONUS_1: 100,
  PRE_REG_BONUS_2: 200,
  RECOMMENDER: 100,
  RECOMMENDED: 100,
  DOUBLE_HEART_REFUND: 20,
  COMMUNITY_DOUBLE_HEART: 100,
};

const PAYMENT_METHOD = {
  GOOGLE: "googlePay",
  APPLE: "applePay",
};

const PAYMENT_STATUS = {
  //待支付
  PENDING: 1,
  //支付成功
  PAYMENT: 2,
  //退款成功
  REFUND: 3,
};

const INTEREST_TYPE = {
  system: "system",
  user: "user",
};

const BLOCK_TYPE = {
  DELETE: "DELETE",
  PERMA_BAN: "PERMA_BAN",
  TEMP_BAN: "TEMP_BAN",
};

const REPORT_ACTION = {
  PERMA_BAN: "PERMA_BAN",
  TEMP_BAN: "TEMP_BAN",
  WARNING: "WARNING",
  NOTHING: "NOTHING",
};

const REPORT_ACTION_LIST = [
  REPORT_ACTION.PERMA_BAN,
  REPORT_ACTION.TEMP_BAN,
  REPORT_ACTION.WARNING,
  REPORT_ACTION.NOTHING,
];

const SETTING_KEYS = {
  DAILY_GIVEN_CARDS: "daily_given_cards",
  DAILY_HIGH_EVAL: "daily_high_eval",
  DAILY_COMMENT_REMINDER_COUNT: "daily_comment_reminder_count",
};

const SOURCE = {
  PROFILE: "PROFILE",
  COMMUNITY: "COMMUNITY",
};

const REQUEST_STATUS = {
  PENDING: "pending",
  MATCHED: "matched",
  REJECTED: "rejected",
  EXPIRED: "expired",
};

module.exports = {
  AVATAR,
  VERIFICATION_STATUS,
  VERIFICATION_TYPE,
  LOCKS,
  HEART_LEVEL,
  RECOMMEND_TYPE,
  CARD_COLLECTION,
  TRANSACTION,
  PRICES,
  PAYMENT_METHOD,
  PAYMENT_STATUS,
  INTEREST_TYPE,
  USER_STATUS,
  REPORT_ACTION,
  REPORT_ACTION_LIST,
  BLOCK_TYPE,
  SETTING_KEYS,
  SOURCE,
  REQUEST_STATUS,
};
