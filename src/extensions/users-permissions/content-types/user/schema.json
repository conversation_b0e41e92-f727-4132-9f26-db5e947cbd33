{"kind": "collectionType", "collectionName": "up_users", "info": {"name": "user", "description": "", "singularName": "user", "pluralName": "users", "displayName": "User"}, "options": {"draftAndPublish": false}, "attributes": {"email": {"type": "email", "minLength": 6, "configurable": false, "required": true}, "provider": {"type": "string", "configurable": false}, "password": {"type": "password", "minLength": 6, "configurable": false, "private": true, "searchable": false}, "resetPasswordToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmationToken": {"type": "string", "configurable": false, "private": true, "searchable": false}, "confirmed": {"type": "boolean", "default": false, "configurable": false}, "blocked": {"type": "boolean", "default": false, "configurable": false}, "role": {"type": "relation", "relation": "manyToOne", "target": "plugin::users-permissions.role", "inversedBy": "users", "configurable": false}, "username": {"type": "string", "minLength": 2, "unique": false, "required": true}, "fullname": {"type": "string"}, "agreed": {"type": "boolean", "default": false}, "deleted": {"type": "boolean", "default": false}, "deletedAt": {"type": "datetime"}, "phone": {"type": "string"}, "birthday": {"type": "date"}, "gender": {"type": "string"}, "introduction": {"type": "text"}, "location": {"type": "string"}, "latitude": {"type": "decimal", "column": {"type": "decimal", "args": [9, 6]}}, "longitude": {"type": "decimal", "column": {"type": "decimal", "args": [9, 6]}}, "avatar": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "photos": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "interests": {"type": "relation", "relation": "manyToMany", "target": "api::interest.interest", "inversedBy": "users"}, "university": {"type": "relation", "relation": "manyToOne", "target": "api::university.university", "inversedBy": "users"}, "certificate": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "verificationEmail": {"type": "email"}, "verificationType": {"type": "enumeration", "enum": ["email", "certificate"]}, "emailSentAt": {"type": "datetime"}, "verificationStatus": {"type": "enumeration", "enum": ["incomplete", "pending", "not_verified_yet", "verified", "rejected"], "default": "incomplete"}, "balance": {"type": "integer", "default": 0}, "totalExpenses": {"type": "integer", "default": 0}, "avgRating": {"type": "float", "default": 0}, "countryCode": {"type": "string"}, "referralCode": {"type": "string"}, "regRefCode": {"type": "string"}, "receiveNotification": {"type": "boolean", "default": true}, "status": {"type": "enumeration", "enum": ["UNDERGRADUATE", "GRADUATE"]}, "fakeName": {"type": "string"}, "visibility": {"type": "boolean", "default": false}, "verificationLogs": {"type": "relation", "relation": "oneToMany", "target": "api::verification-log.verification-log", "mappedBy": "user"}, "level": {"type": "integer", "default": 0}, "orientation": {"type": "integer", "default": 1}, "regionCode": {"type": "customField", "customField": "plugin::country-select.country"}, "financialStatus": {"type": "enumeration", "enum": ["doNotAuthorize", "submitted", "risingStar", "oneStar", "twoStar", "threeStar", "fourStar", "fiveStar", "sixStar", "sevenStar"], "default": "doNotAuthorize"}, "certificateList": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "nationalityCode": {"type": "string"}, "phoneVerificationStatus": {"type": "enumeration", "enum": ["no", "pass", "done"]}, "firstTimeUpdateProfile": {"type": "boolean"}, "title": {"type": "string"}, "fakeRandomAvatar": {"type": "string"}, "fakeAvatar": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "splashImage": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "asset_verifications": {"type": "relation", "relation": "oneToMany", "target": "api::asset-verification.asset-verification", "mappedBy": "user"}, "idcard": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "received_users": {"type": "relation", "relation": "oneToMany", "target": "api::received-user.received-user", "mappedBy": "scanner"}, "asset_certification": {"type": "relation", "relation": "oneToOne", "target": "api::asset-certification.asset-certification", "mappedBy": "owner"}}}