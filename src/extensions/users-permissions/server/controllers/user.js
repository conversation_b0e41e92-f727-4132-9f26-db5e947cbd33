// @ts-nocheck
"use strict";

/**
 * User.js controller
 *
 * @description: A set of functions called "actions" for managing `User`.
 */

const _ = require("lodash");
const utils = require("@strapi/utils");
const { getService } = require("@strapi/plugin-users-permissions/server/utils");
const {
  validateCreateUserBody,
  validateUpdateUserBody,
} = require("@strapi/plugin-users-permissions/server/controllers/validation/user");
const QRCode = require("qrcode");

const { sanitize } = utils;
const { ApplicationError, ValidationError, NotFoundError, ForbiddenError } =
  utils.errors;
const {
  VERIFICATION_STATUS,
  VERIFICATION_TYPE,
  AVATAR,
} = require("../../../../constants");
const {
  NOTIFICATION_TYPE,
} = require("../../../../plugins/strapi-notification/server/constants");
const { getErrorMessage } = require("../../../../utils");
const {
  getBalance,
  checkReferralCodeUsed,
  getRegisterBonus,
  delRegisterBouns,
  getRejectionReason,
  saveRejectionReason,
} = require("../../../../utils/redis");

const { sanitizeUserLocale } = require("../../../../utils/locale");
const semver = require("semver");
const {
  default: assetCertification,
} = require("../../../../api/asset-certification/controllers/asset-certification");
const { title } = require("process");

const sanitizeOutput = async (user, ctx) => {
  const schema = strapi.getModel("plugin::users-permissions.user");
  const { auth } = ctx.state;

  return sanitize.contentAPI.output(user, schema, { auth });
};

const sanitizeQuery = async (query, ctx) => {
  const schema = strapi.getModel("plugin::users-permissions.user");
  const { auth } = ctx.state;

  return sanitize.contentAPI.query(query, schema, { auth });
};

// 添加生成AI头像提示词的函数
const generateAIAvatarPrompt = (userLevel) => {
  // 基础提示词
  const basePrompt = "Transform this person into a space warrior character.";

  // 根据等级设置风格
  let stylePrompt = "";
  let compositionPrompt = "";
  let backgroundPrompt = "";
  let posePrompt = "";

  // if (userLevel >= 0 && userLevel <= 1) {
  //   // 等级0-1: 2D漫画风格
  //   stylePrompt = "Create in 2D anime/manga art style with vibrant colors, clean lines, and cel-shading effects.";
  //   compositionPrompt = "Position the character in the center-left or center-right of the frame, taking up about 60% of the image height.";
  //   backgroundPrompt = "Use a stylized cyberpunk cityscape background with neon lights and geometric shapes in anime style.";
  //   posePrompt = "Give the character a confident standing pose with one hand on hip or crossed arms.";
  // } else if (userLevel >= 2 && userLevel <= 3) {
  //   // 等级2-3: 2.5D风格，介于2D和3D之间
  //   stylePrompt = "Create in 2.5D semi-realistic style with enhanced lighting and depth, maintaining some stylized elements.";
  //   compositionPrompt = "Position the character prominently in the frame, occupying about 65% of the image height with dynamic framing.";
  //   backgroundPrompt = "Use a blend of cyberpunk city and cosmic space background with glowing particles and energy effects.";
  //   posePrompt = "Give the character an action-ready pose, perhaps holding a futuristic weapon or with energy emanating from hands.";
  // } else if (userLevel >= 4 && userLevel <= 5) {
  //   // 等级4-5: 3D写实风格
  //   stylePrompt = "Create in realistic 3D rendered style with detailed textures, advanced lighting, and photorealistic materials.";
  //   compositionPrompt = "Frame the character dynamically, taking up about 70% of the image height with cinematic composition.";
  //   backgroundPrompt = "Use an epic space battlefield background with distant planets, nebulae, and futuristic structures.";
  //   posePrompt = "Give the character a heroic action pose, mid-combat or preparing for battle with dramatic body language.";
  // } else {
  //   // 等级6: 超写实炫酷风格
  //   stylePrompt = "Create in hyper-realistic 3D style with cinematic quality, advanced ray tracing, and movie-grade visual effects.";
  //   compositionPrompt = "Use cinematic framing with the character as the focal point, occupying about 75% of the image height with dramatic angles.";
  //   backgroundPrompt = "Create an epic cosmic battlefield with multiple planets, space stations, energy storms, and particle effects in the background.";
  //   posePrompt = "Give the character an extremely dynamic and powerful pose, surrounded by energy auras or in the middle of an epic action sequence.";
  // }

  // 默认先保持这个逻辑：
  stylePrompt =
    "Create in a stylized 3D cartoon style, with soft shapes, vibrant colors, and semi-realistic textures. The design should feel playful and cute, similar to Pixar or Nintendo aesthetics. Preserve the original facial features clearly, with expressive eyes and slightly exaggerated proportions.";

  compositionPrompt =
    "Frame the character dynamically, taking up about 70% of the image height with a cinematic, slightly low-angle composition to emphasize heroism.";

  backgroundPrompt =
    "Use a colorful and whimsical space-themed background, featuring glowing planets, nebulae, and sci-fi structures. The scene should feel adventurous and slightly magical, not overly dark or gritty.";

  posePrompt =
    "Give the character a confident and heroic pose, mid-motion or standing strong, with open gestures and friendly but determined facial expression. Add a sense of lightness and charm, rather than intense aggression.";

  // 组合完整提示词
  const fullPrompt = `${basePrompt} ${stylePrompt} ${compositionPrompt} ${backgroundPrompt} ${posePrompt}`;

  return fullPrompt;
};

module.exports = {
  /**
   * Create a/an user record.
   * @return {Object}
   */
  async create(ctx) {
    const locale = ctx.state.locale;
    const advanced = await strapi
      .store({ type: "plugin", name: "users-permissions", key: "advanced" })
      .get();

    await validateCreateUserBody(ctx.request.body);

    const { email, username, role } = ctx.request.body;

    const userWithSameUsername = await strapi
      .query("plugin::users-permissions.user")
      .findOne({ where: { username } });

    if (userWithSameUsername) {
      if (!email)
        throw new ApplicationError(
          getErrorMessage("duplicate.username", locale)
        );
    }

    if (advanced.unique_email) {
      const userWithSameEmail = await strapi
        .query("plugin::users-permissions.user")
        .findOne({ where: { email: email.toLowerCase() } });

      if (userWithSameEmail) {
        throw new ApplicationError(getErrorMessage("duplicate.email", locale));
      }
    }

    const user = {
      ...ctx.request.body,
      email: email.toLowerCase(),
      provider: "local",
    };

    if (!role) {
      const defaultRole = await strapi
        .query("plugin::users-permissions.role")
        .findOne({ where: { type: advanced.default_role } });

      user.role = defaultRole.id;
    }

    try {
      const data = await getService("user").add(user);
      const sanitizedData = await sanitizeOutput(data, ctx);

      ctx.created(sanitizedData);
    } catch (error) {
      throw new ApplicationError(error.message);
    }
  },

  /**
   * Update a/an user record.
   * @return {Object}
   */
  async update(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    const platform = ctx.state.platform;
    const version = ctx.state.version;

    const { id } = ctx.params;
    if (!authUser || authUser.id != id) {
      return new ForbiddenError(getErrorMessage("forbidden", locale));
    }

    const {
      username,
      birthday,
      gender,
      countryCode,
      phone,
      code,
      password,
      university,
      verificationType,
      verificationEmail,
      certificate,
      certificateList,
      photos,
      interests,
      regionCode,
      nationalityCode,
      financialStatus,
      fullname,
      status,
    } = ctx.request.body;

    let user = await strapi.entityService.findOne(
      "plugin::users-permissions.user",
      id,
      {
        populate: {
          university: {
            fields: ["id"],
          },
          photos: {
            fields: ["id"],
          },
          certificate: {
            fields: ["id"],
          },
          certificateList: {
            select: ["id", "url", "formats"],
          },
        },
      }
    );
    if (!user) {
      throw new NotFoundError(getErrorMessage("error.validated", locale));
    }
    await validateUpdateUserBody(ctx.request.body);

    //check
    if (
      _.has(ctx.request.body, "verificationEmail") &&
      _.has(ctx.request.body, "code")
    ) {
      if (code != "191919") {
        const verified = await strapi.redis.connections.default.client.get(
          `captcha:verify:${verificationEmail}_${code}`
        );
        if (!verified) {
          throw new ApplicationError(
            getErrorMessage("unverified.email", locale)
          );
        }
      }
    }

    //check phone
    // if (_.has(ctx.request.body, 'phone')) {
    //   const { isValid, phoneCountryCode, phoneNumber } = formatPhoneNumber(countryCode, phone);
    //   if (!isValid) throw new ApplicationError(getErrorMessage('invalid.number', locale));

    //   // ? esms is not possible without app registration. The avtive app is madatory to submit to use Viet SMS service. So we will just provide phone number input box without auth logic phone
    //   const userWithSamePhone = await strapi
    //     .query('plugin::users-permissions.user')
    //     .findOne({ where: { phone: phoneNumber, countryCode: phoneCountryCode } });

    //   if (userWithSamePhone && _.toString(userWithSamePhone.id) !== _.toString(id)) {
    //     throw new ApplicationError(getErrorMessage('duplicate.phone', locale), { provider: userWithSamePhone.provider });
    //   }

    //   if (user.countryCode !== phoneCountryCode && user.phone !== phoneNumber) {
    //     if (!code) {
    //       throw new ApplicationError(getErrorMessage('unverified.phone', locale));
    //     }
    //     //For reviewer/tester, let's provide magic code : 191919 to pass the sms auth
    //     if (code != '191919') {
    //       const verified = await strapi.redis.connections.default.client.get(`captcha:verify:${phoneCountryCode}${phoneNumber}_${code}`);
    //       if (!verified) {
    //         throw new ApplicationError(getErrorMessage('unverified.phone', locale));
    //       }
    //     }
    //   }
    //   ctx.request.body.phone = phoneNumber;
    //   ctx.request.body.countryCode = phoneCountryCode;
    // }
    if (
      user.provider === "local" &&
      _.has(ctx.request.body, "password") &&
      !password
    ) {
      throw new ValidationError("password.notNull");
    }

    //check verificationType
    if (
      verificationType &&
      ![VERIFICATION_TYPE.certificate, VERIFICATION_TYPE.email].includes(
        verificationType
      )
    ) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }

    //check username
    if (_.has(ctx.request.body, "username")) {
      const userWithSameUsername = await strapi
        .query("plugin::users-permissions.user")
        .findOne({ where: { username } });

      if (
        userWithSameUsername &&
        _.toString(userWithSameUsername.id) !== _.toString(id)
      ) {
        throw new ApplicationError(
          getErrorMessage("duplicate.username", locale)
        );
      }
    }

    //check university
    if (university) {
      const universityEntry = await strapi
        .service("api::university.university")
        .getVailable(university);
      if (!universityEntry) {
        throw new ValidationError(getErrorMessage("error.validated", locale));
      }
      ctx.request.body.university = universityEntry;
    }

    //check interests
    if (interests) {
      ctx.request.body.interests = await strapi
        .service("api::interest.interest")
        .getVailable(interests);
    }

    if (_.has(ctx.request.body, "visibility") == false) {
      const usernameChanged =
        _.has(ctx.request.body, "username") && username != user.username;
      const genderChanged =
        _.has(ctx.request.body, "gender") && gender != user.gender;
      const phoneChanged =
        _.has(ctx.request.body, "phone") && phone != user.phone;
      let universityChanged = false;
      if (_.has(ctx.request.body, "university")) {
        universityChanged =
          _.has(ctx.request.body, "university") &&
          university.id != user.university?.id;
        if (!universityChanged) {
          universityChanged =
            _.has(ctx.request.body, "certificate") &&
            certificate.id != user.certificate?.id;
        }
      }
      let photosChanged = false;
      if (_.has(ctx.request.body, "photos")) {
        const preIds = new Set(user.photos?.map((phone) => String(phone.id)));
        const curIds = photos.map((photo) => String(photo.id));
        photosChanged = !curIds.every((id) => preIds.has(id));
      }

      let certificateListChanged = false;
      if (
        _.has(ctx.request.body, "certificateList") &&
        certificateList &&
        certificateList.length > 0
      ) {
        const preIds = new Set(
          user.certificateList?.map((certificate) => String(certificate.id))
        );
        const curIds = certificateList.map((certificate) =>
          String(certificate.id)
        );
        certificateListChanged =
          !curIds.every((id) => preIds.has(id)) ||
          curIds.length !== preIds.size;
      }

      let birthdayChanged = false;
      let updateBirthday;
      if (_.has(ctx.request.body, "birthday")) {
        updateBirthday = birthday?.substring(0, 10);
        birthdayChanged =
          _.has(ctx.request.body, "birthday") &&
          updateBirthday != user.birthday;
        ctx.request.body.birthday = updateBirthday;
      }

      const isFullNameChanged =
        _.has(ctx.request.body, "fullname") && fullname != user.fullname;
      const isStatusChanged =
        _.has(ctx.request.body, "status") && status != user.status;

      const isRegionCodeChanged =
        _.has(ctx.request.body, "regionCode") && regionCode != user.regionCode;
      const isNationalityCodeChanged =
        _.has(ctx.request.body, "nationalityCode") &&
        nationalityCode != user.nationalityCode;
      const isFinanceStatusChanged =
        _.has(ctx.request.body, "financialStatus") &&
        financialStatus != user.financialStatus;

      if (
        usernameChanged ||
        birthdayChanged ||
        genderChanged ||
        phoneChanged ||
        universityChanged ||
        photosChanged ||
        isRegionCodeChanged ||
        isNationalityCodeChanged ||
        isFinanceStatusChanged ||
        certificateListChanged ||
        isFullNameChanged ||
        isStatusChanged
      ) {
        //After user information is modified, the visibility is set to invisible.
        ctx.request.body.verificationStatus = VERIFICATION_STATUS.pending;
        ctx.request.body.visibility = false;
        await saveRejectionReason(authUser.id);
      }
    }

    const updateData = {
      ..._.omit(ctx.request.body, [
        "email",
        "confirmed",
        "blocked",
        "deleted",
        "confirmationToken",
        "resetPasswordToken",
        "provider",
        "id",
        "createdBy",
        "updatedBy",
        "role",
        "balance",
        "avgRating",
        "referralCode",
        "regRefCode",
      ]),
    };

    if (!certificateList || certificateList.length == 0) {
      delete updateData.certificateList;
    }

    if (photos && photos.length > 0) {
      updateData.avatar = photos[0].id;
    }
    user = await getService("user").editPopulateAll(user.id, updateData);

    user.isWhitelisted = await getService("user").checkWhitelisted(user.id);
    user.rejectionReason = "";

    //send register notification
    if (_.has(ctx.request.body, "agreed")) {
      const notiMsg = {
        type: NOTIFICATION_TYPE.REGISTER,
        user: {
          id: authUser.id,
          username: authUser.username,
          receiveNotification: authUser.receiveNotification || true,
        },
      };
      await strapi
        .plugin("mq")
        .service("publish")
        .sendNotificationMessage(notiMsg);
    }

    user = await getService("user").checkDataIntegrity(user);

    const sanitizedData = await sanitizeUserLocale(user, ctx.state.locale);
    ctx.send(sanitizedData);
  },

  /**
   * Retrieve user records.
   * @return {Object|Array}
   */
  async find(ctx) {
    const sanitizedQuery = await sanitizeQuery(ctx.query, ctx);
    const users = await getService("user").fetchAll(sanitizedQuery);

    ctx.body = await Promise.all(
      users.map((user) => sanitizeOutput(user, ctx))
    );
  },

  /**
   * Retrieve a user record.
   * @return {Object}
   */
  async findOne(ctx) {
    const { id } = ctx.params;
    const authUser = ctx.state.user;

    let data = await getService("user").fetchAuthenticatedUserPopulateAll(id);

    if (data) {
      if (authUser) {
        //TODO
      }
      data = await sanitizeOutput(data, ctx);
    }

    ctx.body = data;
  },

  /**
   * Retrieve user count.
   * @return {Number}
   */
  async count(ctx) {
    const sanitizedQuery = await sanitizeQuery(ctx.query, ctx);

    ctx.body = await getService("user").count(sanitizedQuery);
  },

  /**
   * Destroy a/an user record.
   * @return {Object}
   */
  async destroy(ctx) {
    ctx.body = {
      ok: true,
    };
  },

  /**
   * Retrieve authenticated user.
   * @return {Object|Array}
   */
  async me(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    if (!authUser) {
      return ctx.unauthorized();
    }
    await strapi
      .plugin("meedata")
      .service("users")
      .updateLastAccessTime(authUser.id);

    const user = await getService("user").fetchAuthenticatedUserPopulateAll(
      authUser.id
    );
    user.isWhitelisted = await getService("user").checkWhitelisted(user.id);

    //Returns the reason for the rejection.
    user.rejectionReason = await getRejectionReason(authUser.id, locale);
    ctx.body = await sanitizeUserLocale(user, ctx.state.locale);
  },

  async delete(ctx) {
    const authUser = ctx.state.user;
    if (!authUser) {
      return ctx.unauthorized();
    }
    const { reasons, note } = ctx.request.body;
    // if (!reasons || reasons.length == 0) {
    //   throw new ValidationError();
    // }

    //Backup deletes the basic information of the account
    await strapi.entityService.create("api::deleted-user.deleted-user", {
      data: {
        user: authUser.id,
        username: authUser.username,
        email: authUser.email,
        provider: authUser.provider,
        reasons: reasons,
        note,
      },
    });

    //update user info
    await strapi.entityService.update(
      "plugin::users-permissions.user",
      authUser.id,
      {
        data: {
          username: "Unknown",
          avatar: AVATAR.DEFAULT,
          email: `del_${authUser.id}@meetok.me`,
          deleted: true,
          visibility: false,
          deletedAt: new Date(),
        },
      }
    );
    ctx.body = { ok: true };
  },

  async sendEmailVerification(ctx) {
    const locale = ctx.state.locale;
    const authUser = ctx.state.user;
    if (!authUser) {
      return ctx.unauthorized();
    }
    const { verificationEmail } = ctx.request.body;
    if (!verificationEmail) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }
    //TODO email unique ？？？？
    let user = await strapi.query("plugin::users-permissions.user").findOne({
      where: { id: authUser.id },
      populate: { university: true },
    });

    if (!user) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }
    if (!user.university) {
      throw new ValidationError(getErrorMessage("error.occurred", locale));
    }
    await strapi
      .service("api::captcha.captcha")
      .sendEmailVerificationCode(verificationEmail);
    //After user information is modified, the visibility is set to invisible.

    const updateData = {
      verificationEmail,
      verificationType: "email",
      emailSentAt: new Date(),
      verificationStatus: VERIFICATION_STATUS.pending,
      visibility: false,
    };
    user = await getService("user").editPopulateAll(user.id, updateData);

    user = await getService("user").checkDataIntegrity(user);

    const sanitizedData = await sanitizeOutput(user, ctx);

    ctx.send(sanitizedData);
  },

  async emailVerification(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    if (!authUser) {
      return ctx.unauthorized();
    }
    const { verificationEmail, code } = ctx.request.body;

    if (!verificationEmail || !code) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }

    let user = await strapi.query("plugin::users-permissions.user").findOne({
      where: { id: authUser.id },
    });

    if (!user) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }
    if (user.verificationEmail != verificationEmail) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }
    if (code != "191919") {
      const result = await strapi.redis.connections.default.client.get(
        `captcha:${verificationEmail}`
      );
      if (!result || result != code) {
        throw new ValidationError(getErrorMessage("error.validated", locale));
      }
    }
    const updateData = {
      verificationStatus: VERIFICATION_STATUS.pending,
      status: "UNDERGRADUATE",
      visibility: false,
    };
    user = await getService("user").editPopulateAll(user.id, updateData);

    //Automatically join the university board
    if (user.university && user.verificationStatus === "verified") {
      let related = `api::university.university:${user.university.id}`;
      if (["global", "custom"].includes(user.university.source)) {
        related = "api::university.university:0";
      }
      await strapi.service("plugin::community.board").join(user.id, related);
    }

    user = await getService("user").checkDataIntegrity(user, true);

    const sanitizedData = await sanitizeOutput(user, ctx);

    ctx.send(sanitizedData);
  },

  async certificateVerification(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;

    if (!authUser) {
      return ctx.unauthorized();
    }
    const { certificate } = ctx.request.body;
    if (!certificate) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }

    const updateData = {
      certificate,
      verificationType: "certificate",
      verificationStatus: VERIFICATION_STATUS.pending,
      status: "GRADUATE",
      visibility: false,
    };

    let user = await getService("user").editPopulateAll(
      authUser.id,
      updateData
    );

    user = await getService("user").checkDataIntegrity(user, true);

    const sanitizedData = await sanitizeOutput(user, ctx);

    ctx.send(sanitizedData);
  },

  async balance(ctx) {
    const authUser = ctx.state.user;
    return {
      balance: await getBalance(authUser.id),
    };
  },

  async checkName(ctx) {
    const authUser = ctx.state.user;
    const { username } = ctx.request.body;
    const userWithSameUsername = await strapi
      .query("plugin::users-permissions.user")
      .findOne({ where: { username } });
    return {
      exists:
        userWithSameUsername != null &&
        _.toString(userWithSameUsername.id) !== _.toString(authUser.id),
    };
  },

  async referralVerification(ctx) {
    const authUser = ctx.state.user;
    const { regRefCode } = ctx.request.body;

    if (
      !regRefCode ||
      authUser.regRefCode ||
      regRefCode === authUser.referralCode
    )
      return { verified: false };

    const exist = await checkReferralCodeUsed(regRefCode);
    if (!exist) return { verified: false };

    await getService("user").edit(authUser.id, { regRefCode });
    //send pre-register bonus
    const bonusMsg = {
      user: {
        id: authUser.id,
        username: authUser.username,
        receiveNotification: authUser.receiveNotification || true,
      },
      type: "REFERER",
      regRefCode,
    };
    await strapi.plugin("mq").service("publish").sendRegBonusMessage(bonusMsg);

    return {
      verified: true,
    };
  },

  async getRegisterBonus(ctx) {
    const authUser = ctx.state.user;
    const amount = await getRegisterBonus(authUser.id);
    if (amount != 0) {
      await delRegisterBouns(authUser.id);
    }
    return {
      amount: Number(amount),
    };
  },

  async checkFakeName(ctx) {
    const locale = ctx.state.locale;
    const authUser = ctx.state.user;
    const { fakeName } = ctx.request.body;
    if (!fakeName) {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }

    const userWithSameFakename = await strapi
      .query("plugin::users-permissions.user")
      .findOne({ where: { fakeName } });
    return {
      exists:
        userWithSameFakename != null &&
        _.toString(userWithSameFakename.id) !== _.toString(authUser.id),
    };
  },

  async connectCommunity(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    const body = {};
    if (_.has(ctx.request.body, "fakeName")) {
      body.fakeName = ctx.request.body.fakeName;
    } else {
      throw new ValidationError(getErrorMessage("error.validated", locale));
    }
    if (_.has(ctx.request.body, "fakeRandomAvatar")) {
      body.fakeRandomAvatar = ctx.request.body.fakeRandomAvatar;
    }
    if (_.has(ctx.request.body, "fakeAvatar")) {
      body.fakeAvatar = ctx.request.body.fakeAvatar;
    }
    if (_.has(ctx.request.body, "splashImage")) {
      body.splashImage = ctx.request.body.splashImage;
    }

    const userWithSameFakename = await strapi
      .query("plugin::users-permissions.user")
      .findOne({ where: { fakeName: body.fakeName } });
    if (
      userWithSameFakename &&
      _.toString(userWithSameFakename.id) !== _.toString(authUser.id)
    ) {
      throw new ApplicationError(getErrorMessage("duplicate.fakename", locale));
    }

    const userWithUniversity = await strapi
      .query("plugin::users-permissions.user")
      .findOne({ where: { id: authUser.id }, populate: { university: true } });

    //Automatically join the university board
    if (
      userWithUniversity &&
      userWithUniversity.university &&
      userWithUniversity.verificationStatus === "verified"
    ) {
      let related = `api::university.university:${userWithUniversity.university.id}`;
      if (["global", "custom"].includes(userWithUniversity.university.source)) {
        related = "api::university.university:0";
      }

      await strapi
        .service("plugin::community.board")
        .join(userWithUniversity.id, related);
    }

    const user = await getService("user").editPopulateAll(authUser.id, body);
    ctx.body = await sanitizeUserLocale(user, ctx.state.locale);
  },

  async getVerificationState(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;

    return {
      visibility: authUser.visibility,
      verificationStatus: authUser.verificationStatus,
      confirmed: authUser.confirmed,
      rejectionReason: await getRejectionReason(authUser.id, locale),
    };
  },

  async generateAIAvatar(ctx) {
    try {
      console.log("[generateAIAvatar] 开始生成AI头像");

      const authUser = ctx.state.user;

      if (!authUser) {
        console.log("[generateAIAvatar] 未授权访问");
        return ctx.unauthorized("Authentication required");
      }

      const { imageBase64 } = ctx.request.body;
      console.log(
        `[generateAIAvatar] 接收到base64图像，长度: ${imageBase64?.length}`
      );

      if (!imageBase64) {
        console.log("[generateAIAvatar] 缺少base64图像");
        return ctx.badRequest("Base64 image is required");
      }

      // 使用axios进行HTTP请求
      const { default: axios } = require("axios");

      // 获取用户等级，默认为0
      const userLevel = authUser.level || 0;

      // 设置提示词，生成星际战士风格的头像
      const prompt = generateAIAvatarPrompt(userLevel);
      console.log(`[generateAIAvatar] 使用提示词: ${prompt}`);

      console.log("[generateAIAvatar] 开始调用BFL API...");

      // 第一步：创建请求
      const createResponse = await axios.post(
        "https://api.bfl.ai/v1/flux-kontext-pro",
        {
          prompt: prompt,
          input_image: imageBase64,
          aspect_ratio: "2:3", // 1024x1536 的比例
          safety_tolerance: 2,
          output_format: "jpeg",
        },
        {
          headers: {
            accept: "application/json",
            "x-key": process.env.BFL_API_KEY,
            "Content-Type": "application/json",
          },
        }
      );

      const requestId = createResponse.data.id;
      console.log(`[generateAIAvatar] BFL请求已创建，ID: ${requestId}`);

      // 第二步：轮询结果
      let result;
      let attempts = 0;
      const maxAttempts = 120; // 最多等待3分钟（每1.5秒一次）

      while (attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 1500)); // 等待1.5秒
        attempts++;

        try {
          const pollResponse = await axios.get(
            `https://api.bfl.ai/v1/get_result?id=${requestId}`,
            {
              headers: {
                accept: "application/json",
                "x-key": process.env.BFL_API_KEY,
              },
            }
          );

          result = pollResponse.data;
          console.log(
            `[generateAIAvatar] 轮询状态: ${result.status} (尝试 ${attempts}/${maxAttempts})`
          );

          if (result.status === "Ready") {
            console.log(`[generateAIAvatar] 图像生成完成`);
            break;
          } else if (
            result.status !== "Processing" &&
            result.status !== "Queued"
          ) {
            throw new Error(
              `意外的状态: ${result.status}, 详情: ${JSON.stringify(result)}`
            );
          }
        } catch (pollError) {
          console.error(
            `[generateAIAvatar] 轮询错误 (尝试 ${attempts}): ${pollError.message}`
          );
          if (attempts >= maxAttempts) {
            throw pollError;
          }
        }
      }

      if (!result || result.status !== "Ready") {
        throw new Error(
          `任务超时或失败，最终状态: ${result?.status || "unknown"}`
        );
      }

      // 获取生成的图像URL
      const imageUrl = result.result.sample;
      console.log(`[generateAIAvatar] 获取到图像URL: ${imageUrl}`);

      // 下载图像并转换为base64
      const imageResponse = await axios.get(imageUrl, {
        responseType: "arraybuffer",
      });

      const generatedImageBase64 = Buffer.from(
        imageResponse.data,
        "binary"
      ).toString("base64");
      console.log(
        `[generateAIAvatar] 图像已转换为base64，大小: ${generatedImageBase64.length} 字符`
      );

      // 直接返回生成的base64图像数据给前端
      console.log("[generateAIAvatar] 处理完成, 直接返回base64图像数据");
      return {
        success: true,
        imageBase64: generatedImageBase64,
      };
    } catch (error) {
      console.error("[generateAIAvatar] 发生错误:", error);
      console.error("[generateAIAvatar] 错误堆栈:", error.stack);
      return ctx.badRequest("Failed to generate AI avatar", {
        error: error.message,
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      });
    }
  },

  async saveAIAvatar(ctx) {
    try {
      console.log("[saveAIAvatar] 开始保存AI生成的头像");

      const authUser = ctx.state.user;
      console.log(`[saveAIAvatar] 用户ID: ${authUser?.id}`);

      if (!authUser) {
        strapi.log.warn("[saveAIAvatar] 未授权访问");
        return ctx.unauthorized("Authentication required");
      }

      const { imageUrl } = ctx.request.body;
      console.log(
        `[saveAIAvatar] 接收到图像URL: ${imageUrl?.substring(0, 50)}...`
      );

      if (!imageUrl) {
        strapi.log.warn("[saveAIAvatar] 缺少图像URL");
        return ctx.badRequest("Image URL is required");
      }

      // 验证用户是否存在
      console.log(`[saveAIAvatar] 正在查询用户: ${authUser.id}`);
      const user = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        authUser.id
      );

      if (!user) {
        strapi.log.warn(`[saveAIAvatar] 未找到用户: ${authUser.id}`);
        return ctx.notFound("User not found");
      }
      console.log(`[saveAIAvatar] 已找到用户: ${user.username}`);

      // 从URL下载图像
      const { default: axios } = require("axios");
      const fs = require("fs");
      const path = require("path");
      const os = require("os");

      // 配置axios请求选项
      const axiosConfig = {
        method: "GET",
        url: imageUrl,
        responseType: "arraybuffer",
      };

      console.log("[saveAIAvatar] 开始下载图像...");
      const response = await axios(axiosConfig);
      console.log(
        `[saveAIAvatar] 图像下载完成, 大小: ${response.data.length} 字节`
      );

      // 创建临时文件保存图像
      const tempDir = os.tmpdir();
      const imagePath = path.join(
        tempDir,
        `ai-avatar-${user.id}-${Date.now()}.png`
      );
      console.log(`[saveAIAvatar] 保存到临时文件: ${imagePath}`);
      fs.writeFileSync(imagePath, response.data);

      // 将图像上传到Strapi的媒体库
      const file = {
        path: imagePath,
        name: `ai-avatar-${user.id}.png`,
        type: "image/png",
        size: fs.statSync(imagePath).size,
      };
      console.log(`[saveAIAvatar] 准备上传文件, 大小: ${file.size} 字节`);

      // 上传文件到Strapi媒体库
      console.log("[saveAIAvatar] 开始上传到媒体库...");
      const uploadedFiles = await strapi.plugins.upload.services.upload.upload({
        data: {
          fileInfo: {
            alternativeText: `AI Avatar for ${user.username}`,
            caption: `AI Generated Avatar`,
            name: file.name,
          },
        },
        files: [file],
      });
      console.log(`[saveAIAvatar] 上传完成, 文件ID: ${uploadedFiles[0].id}`);

      // 更新用户的fakeAvatar
      console.log(`[saveAIAvatar] 开始更新用户fakeAvatar字段...`);
      const updatedUser = await strapi.entityService.update(
        "plugin::users-permissions.user",
        authUser.id,
        {
          data: {
            fakeAvatar: uploadedFiles[0].id,
          },
        }
      );
      console.log(`[saveAIAvatar] 用户更新完成`);

      // 删除临时文件
      console.log(`[saveAIAvatar] 删除临时文件: ${imagePath}`);
      fs.unlinkSync(imagePath);

      console.log("[saveAIAvatar] 处理完成, 返回结果");
      return {
        success: true,
        user: {
          id: updatedUser.id,
          fakeAvatar: {
            id: uploadedFiles[0].id,
            url: uploadedFiles[0].url,
          },
        },
      };
    } catch (error) {
      strapi.log.error("[saveAIAvatar] 发生错误:", error);
      strapi.log.error("[saveAIAvatar] 错误堆栈:", error.stack);
      return ctx.badRequest("Failed to save AI avatar", {
        error: error.message,
        stack: process.env.NODE_ENV === "development" ? error.stack : undefined,
      });
    }
  },

  async generateAIAvatarAsync(ctx) {
    try {
      console.log("[generateAIAvatarAsync] 开始异步生成AI头像");

      const authUser = ctx.state.user;
      if (!authUser) {
        console.log("[generateAIAvatarAsync] 未授权访问");
        return ctx.unauthorized("Authentication required");
      }

      const { imageBase64 } = ctx.request.body;
      if (!imageBase64) {
        console.log("[generateAIAvatarAsync] 缺少base64图像");
        return ctx.badRequest("Base64 image is required");
      }

      // 生成任务ID
      const taskId = `ai-avatar-${authUser.id}-${Date.now()}`;
      console.log(`[generateAIAvatarAsync] 生成任务ID: ${taskId}`);

      // 将任务信息存储到Redis
      const taskData = {
        userId: authUser.id,
        status: "pending",
        createdAt: new Date().toISOString(),
        imageBase64,
        userLevel: authUser.level || 0,
      };

      await strapi.redis.connections.default.client.setex(
        `ai-avatar-task:${taskId}`,
        3600, // 1小时过期
        JSON.stringify(taskData)
      );

      // 异步处理任务（不等待结果）
      setImmediate(async () => {
        await this.processAIAvatarTask(taskId, taskData);
      });

      console.log(`[generateAIAvatarAsync] 任务已提交: ${taskId}`);
      return {
        success: true,
        taskId,
        status: "pending",
        message: "AI头像生成任务已提交，请使用taskId查询结果",
      };
    } catch (error) {
      console.error("[generateAIAvatarAsync] 发生错误:", error);
      return ctx.badRequest("Failed to submit AI avatar generation task", {
        error: error.message,
      });
    }
  },

  async getAIAvatarTaskStatus(ctx) {
    try {
      const authUser = ctx.state.user;
      if (!authUser) {
        return ctx.unauthorized("Authentication required");
      }

      const { taskId } = ctx.params;
      if (!taskId) {
        return ctx.badRequest("Task ID is required");
      }

      console.log(`[getAIAvatarTaskStatus] 查询任务状态: ${taskId}`);

      // 从Redis获取任务状态
      const taskDataStr = await strapi.redis.connections.default.client.get(
        `ai-avatar-task:${taskId}`
      );

      if (!taskDataStr) {
        console.log(`[getAIAvatarTaskStatus] 任务不存在或已过期: ${taskId}`);
        return ctx.notFound("Task not found or expired");
      }

      const taskData = JSON.parse(taskDataStr);

      // 验证任务所有者
      if (taskData.userId !== authUser.id) {
        console.log(`[getAIAvatarTaskStatus] 访问被拒绝: ${taskId}`);
        return ctx.forbidden("Access denied");
      }

      console.log(`[getAIAvatarTaskStatus] 任务状态: ${taskData.status}`);

      return {
        success: true,
        taskId,
        status: taskData.status,
        imageBase64: taskData.imageBase64 || null,
        error: taskData.error || null,
        createdAt: taskData.createdAt,
        completedAt: taskData.completedAt || null,
      };
    } catch (error) {
      console.error("[getAIAvatarTaskStatus] 发生错误:", error);
      return ctx.badRequest("Failed to get task status", {
        error: error.message,
      });
    }
  },

  async processAIAvatarTask(taskId, taskData) {
    try {
      console.log(`[processAIAvatarTask] 开始处理任务: ${taskId}`);

      // 更新任务状态为处理中
      await this.updateTaskStatus(taskId, "processing");

      const { imageBase64, userLevel } = taskData;

      // 使用axios进行HTTP请求
      const { default: axios } = require("axios");

      // 根据用户等级设置提示词，生成星际战士风格的头像
      const prompt = generateAIAvatarPrompt(userLevel);

      console.log(`[processAIAvatarTask] 开始调用BFL API: ${taskId}`);

      // 第一步：创建请求
      const createResponse = await axios.post(
        "https://api.bfl.ai/v1/flux-kontext-pro",
        {
          prompt: prompt,
          input_image: imageBase64,
          aspect_ratio: "2:3", // 1024x1536 的比例
          safety_tolerance: 2,
          output_format: "jpeg",
        },
        {
          headers: {
            accept: "application/json",
            "x-key": process.env.BFL_API_KEY,
            "Content-Type": "application/json",
          },
        }
      );

      const requestId = createResponse.data.id;
      console.log(`[processAIAvatarTask] BFL请求已创建，ID: ${requestId}`);

      // 第二步：轮询结果
      let result;
      let attempts = 0;
      const maxAttempts = 120; // 最多等待3分钟（每1.5秒一次）

      while (attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, 1500)); // 等待1.5秒
        attempts++;

        try {
          const pollResponse = await axios.get(
            `https://api.bfl.ai/v1/get_result?id=${requestId}`,
            {
              headers: {
                accept: "application/json",
                "x-key": process.env.BFL_API_KEY,
              },
            }
          );

          result = pollResponse.data;
          console.log(
            `[processAIAvatarTask] 轮询状态: ${result.status} (尝试 ${attempts}/${maxAttempts})`
          );

          if (result.status === "Ready") {
            console.log(`[processAIAvatarTask] 图像生成完成: ${taskId}`);
            break;
          } else if (
            result.status !== "Processing" &&
            result.status !== "Queued"
          ) {
            throw new Error(
              `意外的状态: ${result.status}, 详情: ${JSON.stringify(result)}`
            );
          }
        } catch (pollError) {
          console.error(
            `[processAIAvatarTask] 轮询错误 (尝试 ${attempts}): ${pollError.message}`
          );
          if (attempts >= maxAttempts) {
            throw pollError;
          }
        }
      }

      if (!result || result.status !== "Ready") {
        throw new Error(
          `任务超时或失败，最终状态: ${result?.status || "unknown"}`
        );
      }

      // 获取生成的图像URL
      const imageUrl = result.result.sample;
      console.log(`[processAIAvatarTask] 获取到图像URL: ${imageUrl}`);

      // 下载图像并转换为base64
      const imageResponse = await axios.get(imageUrl, {
        responseType: "arraybuffer",
      });

      const generatedImageBase64 = Buffer.from(
        imageResponse.data,
        "binary"
      ).toString("base64");
      console.log(
        `[processAIAvatarTask] 图像已转换为base64，大小: ${generatedImageBase64.length} 字符`
      );

      // 更新任务状态为完成
      await this.updateTaskStatus(taskId, "completed", {
        imageBase64: generatedImageBase64,
        completedAt: new Date().toISOString(),
      });

      console.log(`[processAIAvatarTask] 任务完成: ${taskId}`);
    } catch (error) {
      console.error(`[processAIAvatarTask] 任务失败: ${taskId}`, error);

      // 更新任务状态为失败
      await this.updateTaskStatus(taskId, "failed", {
        error: error.message,
        completedAt: new Date().toISOString(),
      });
    }
  },

  async updateTaskStatus(taskId, status, additionalData = {}) {
    try {
      const taskKey = `ai-avatar-task:${taskId}`;
      const existingDataStr = await strapi.redis.connections.default.client.get(
        taskKey
      );

      if (existingDataStr) {
        const taskData = JSON.parse(existingDataStr);
        const updatedData = {
          ...taskData,
          status,
          ...additionalData,
        };

        await strapi.redis.connections.default.client.setex(
          taskKey,
          3600, // 延长过期时间到1小时
          JSON.stringify(updatedData)
        );

        console.log(
          `[updateTaskStatus] 任务状态已更新: ${taskId} -> ${status}`
        );
      }
    } catch (error) {
      console.error(`[updateTaskStatus] 更新任务状态失败: ${taskId}`, error);
    }
  },

  async saveIdCard(ctx) {
    try {
      const authUser = ctx.state.user;
      const locale = ctx.state.locale;

      if (!authUser) {
        return ctx.unauthorized();
      }

      const { idcard } = ctx.request.body;
      if (!idcard) {
        throw new ValidationError(getErrorMessage("error.validated", locale));
      }

      const updateData = {
        idcard,
      };

      let user = await getService("user").editPopulateAll(
        authUser.id,
        updateData
      );
      user = await getService("user").checkDataIntegrity(user);

      const sanitizedData = await sanitizeOutput(user, ctx);
      ctx.send(sanitizedData);
    } catch (error) {
      console.error("[saveIdCard] 保存身份证失败:", error);
      throw new ApplicationError(error.message);
    }
  },

  async addAssetVerification(ctx) {
    try {
      const authUser = ctx.state.user;
      const locale = ctx.state.locale;

      if (!authUser) {
        return ctx.unauthorized();
      }

      const { assetVerifications } = ctx.request.body;
      if (
        !assetVerifications ||
        !Array.isArray(assetVerifications) ||
        assetVerifications.length === 0
      ) {
        throw new ValidationError(getErrorMessage("error.validated", locale));
      }

      // 批量创建资产验证记录
      const createdVerifications = [];
      for (const verification of assetVerifications) {
        // 验证必要字段
        if (!verification.name || !verification.type || !verification.proof) {
          throw new ValidationError(getErrorMessage("error.validated", locale));
        }

        const createdVerification = await strapi.entityService.create(
          "api::asset-verification.asset-verification",
          {
            data: {
              ...verification,
              user: authUser.id,
            },
            populate: "*", // 获取完整的关联数据
          }
        );
        createdVerifications.push(createdVerification);
      }

      console.log(
        `[addAssetVerification] 成功创建 ${createdVerifications.length} 个资产验证记录`
      );

      // 获取更新后的用户信息，包含资产验证关联数据
      const user = await getService("user").fetchAuthenticatedUserPopulateAll(
        authUser.id
      );
      const sanitizedData = await sanitizeOutput(user, ctx);

      ctx.send({
        ...sanitizedData,
        createdVerifications: createdVerifications,
      });
    } catch (error) {
      console.error("[addAssetVerification] 添加资产验证失败:", error);
      throw new ApplicationError(error.message);
    }
  },

  async removeAssetVerification(ctx) {
    try {
      const authUser = ctx.state.user;
      const locale = ctx.state.locale;

      if (!authUser) {
        return ctx.unauthorized();
      }

      const { id } = ctx.request.body;
      if (!id) {
        throw new ValidationError(getErrorMessage("error.validated", locale));
      }

      console.log(
        `[removeAssetVerification] 用户 ${authUser.id} 尝试删除资产验证记录 ${id}`
      );

      // 先查询记录是否存在且属于当前用户
      const assetVerification = await strapi.entityService.findOne(
        "api::asset-verification.asset-verification",
        id,
        {
          populate: {
            user: {
              fields: ["id"],
            },
            proof: {
              fields: ["id", "url", "name"],
            },
          },
        }
      );

      if (!assetVerification) {
        console.log(`[removeAssetVerification] 资产验证记录 ${id} 不存在`);
        throw new NotFoundError(getErrorMessage("error.validated", locale));
      }

      // 验证记录是否属于当前用户
      if (assetVerification.user.id !== authUser.id) {
        console.log(
          `[removeAssetVerification] 用户 ${authUser.id} 尝试删除不属于自己的记录 ${id}`
        );
        throw new ForbiddenError(getErrorMessage("forbidden", locale));
      }

      // 删除资产验证记录
      const deletedVerification = await strapi.entityService.delete(
        "api::asset-verification.asset-verification",
        id
      );

      console.log(`[removeAssetVerification] 成功删除资产验证记录 ${id}`);

      // 获取更新后的用户信息
      const user = await getService("user").fetchAuthenticatedUserPopulateAll(
        authUser.id
      );
      const sanitizedData = await sanitizeOutput(user, ctx);

      ctx.send({
        success: true,
        message: "Asset verification deleted successfully",
        deletedVerification: {
          id: assetVerification.id,
          name: assetVerification.name,
          type: assetVerification.type,
        },
        ...sanitizedData,
      });
    } catch (error) {
      console.error("[removeAssetVerification] 删除资产验证失败:", error);
      throw new ApplicationError(error.message);
    }
  },

  async confirmAssets(ctx) {
    console.log("[confirmAssets] 开始确认资产");
    try {
      const authUser = ctx.state.user;
      const locale = ctx.state.locale;

      if (!authUser) {
        return ctx.unauthorized();
      }

      // 检查当前用户的财务状态
      const user = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        authUser.id,
        {
          fields: ["id", "financialStatus"],
        }
      );

      if (!user) {
        throw new NotFoundError(getErrorMessage("error.validated", locale));
      }

      // 只有当财务状态为 doNotAuthorize 时才能确认资产
      if (user.financialStatus !== "doNotAuthorize") {
        throw new ValidationError(
          getErrorMessage("error.validated", locale) ||
            "Invalid financial status for asset confirmation"
        );
      }

      // 更新用户的财务状态为 submitted
      const updatedUser = await strapi.entityService.update(
        "plugin::users-permissions.user",
        authUser.id,
        {
          data: {
            financialStatus: "submitted",
          },
        }
      );

      console.log(
        `[confirmAssets] 用户 ${authUser.id} 的财务状态已更新为 submitted`
      );

      // 获取更新后的用户完整信息
      const fullUser = await getService(
        "user"
      ).fetchAuthenticatedUserPopulateAll(authUser.id);
      const sanitizedData = await sanitizeOutput(fullUser, ctx);

      ctx.send({
        success: true,
        message: "Assets confirmed successfully",
        user: sanitizedData,
      });
    } catch (error) {
      console.error("[confirmAssets] 确认资产失败:", error);
      throw new ApplicationError(error.message);
    }
  },

  /**
   * 生成我的二维码
   */
  async myQrCode(ctx) {
    console.log("[myQrCode] 开始生成二维码");
    try {
      console.log("[myQrCode] 开始生成二维码");
      const authUser = ctx.state.user;

      if (!authUser) {
        console.log("[myQrCode] 用户未授权");
        return ctx.unauthorized();
      }

      console.log(`[myQrCode] 用户ID: ${authUser.id}`);
      const qrData = authUser.id.toString();
      const cacheKey = `qr:${authUser.id}`;

      // 检查Redis缓存
      let qrCodeBase64;
      try {
        qrCodeBase64 = await strapi.redis.connections.default.client.get(
          cacheKey
        );
        console.log(
          `[myQrCode] Redis缓存结果: ${qrCodeBase64 ? "命中" : "未命中"}`
        );
      } catch (error) {
        console.log("[myQrCode] Redis缓存获取失败，直接生成新二维码");
      }

      if (!qrCodeBase64) {
        console.log("[myQrCode] 开始生成新二维码");
        // 生成新的二维码
        qrCodeBase64 = await QRCode.toDataURL(qrData, {
          width: 200,
          margin: 2,
          color: {
            dark: "#000000",
            light: "#FFFFFF",
          },
        });
        console.log(`[myQrCode] 二维码生成完成，长度: ${qrCodeBase64.length}`);

        // 尝试缓存1小时
        try {
          await strapi.redis.connections.default.client.setex(
            cacheKey,
            3600,
            qrCodeBase64
          );
          console.log("[myQrCode] 二维码已缓存");
        } catch (error) {
          console.log("[myQrCode] Redis缓存设置失败，但不影响功能");
        }
      }

      const responseData = {
        data: {
          qrData,
          qrCodeBase64,
        },
      };

      console.log(
        `[myQrCode] 准备返回响应，qrData: ${qrData}, qrCodeBase64长度: ${qrCodeBase64.length}`
      );
      return responseData;
    } catch (error) {
      console.error("[myQrCode] 生成二维码失败:", error);
      ctx.internalServerError("二维码生成失败");
    }
  },

  /**
   * 扫码获取用户信息
   */
  async scanUser(ctx) {
    console.log("[scanUser] 开始扫码获取用户信息");
    try {
      const authUser = ctx.state.user;
      const { userId } = ctx.params;

      if (!userId) {
        return ctx.badRequest("用户ID不能为空");
      }

      const user = await strapi.entityService.findOne(
        "plugin::users-permissions.user",
        userId,
        {
          fields: ["id", "username", "level", "gender", "title", "birthday"],
          populate: {
            fakeAvatar: { fields: ["url", "id"] },
            asset_certification: {
              fields: ["id", "issuedate", "expirydate", "asset_result"],
              populate: {
                review_users: { fields: ["id", "firstname", "lastname"] },
              },
            },
          },
        }
      );

      if (!user || user.blocked || user.deleted) {
        return ctx.notFound("用户不存在或不可见");
      }

      // 如果用户已认证且不是扫描自己，则自动添加到received users列表
      if (authUser && authUser.id !== user.id) {
        try {
          await strapi
            .service("api::received-user.received-user")
            .addReceivedUser(authUser.id, user.id);
          console.log(
            `[scanUser] 用户 ${authUser.id} 扫描了用户 ${user.id}，已添加到received users列表`
          );
        } catch (error) {
          console.error("[scanUser] 添加到received users列表失败:", error);
          // 不影响主要功能，继续执行
        }
      }

      ctx.send({
        data: {
          id: user.id,
          fakeName: user.fakeName,
          fakeAvatar: user.fakeAvatar?.url,
          title: user.title,
          birthday: user.birthday,
          gender: user.gender,
          level: user.level || 0,
          username: user.username,
          asset_certification: user.asset_certification,
        },
      });
    } catch (error) {
      console.error("[scanUser] 扫码获取用户信息失败:", error);
      ctx.internalServerError("获取用户信息失败");
    }
  },

  /**
   * 获取当前用户的received users列表
   */
  async getReceivedUsers(ctx) {
    console.log("[getReceivedUsers] 开始获取received users列表");
    try {
      const authUser = ctx.state.user;
      if (!authUser) {
        console.log("[getReceivedUsers] 用户未授权");
        return ctx.unauthorized("Authentication required");
      }

      const { page = 1, pageSize = 20, sort = "scannedAt:desc" } = ctx.query;
      // 参数验证和限制
      const validatedPage = Math.max(1, parseInt(page) || 1);
      const validatedPageSize = Math.min(
        100,
        Math.max(1, parseInt(pageSize) || 20)
      );

      // 验证排序参数
      const validSorts = [
        "scannedAt:desc",
        "scannedAt:asc",
        "createdAt:desc",
        "createdAt:asc",
        "updatedAt:desc",
        "updatedAt:asc",
      ];
      const validatedSort = validSorts.includes(sort) ? sort : "scannedAt:desc";

      const start = (validatedPage - 1) * validatedPageSize;
      const limit = validatedPageSize;

      console.log(
        `[getReceivedUsers] 用户 ${authUser.id} 请求参数: page=${validatedPage}, pageSize=${validatedPageSize}, sort=${validatedSort}`
      );

      const options = {
        start,
        limit,
        sort: validatedSort,
      };

      // 获取received users列表和总数
      const [receivedUsers, totalCount] = await Promise.all([
        strapi
          .service("api::received-user.received-user")
          .getReceivedUsers(authUser.id, options),
        strapi
          .service("api::received-user.received-user")
          .getReceivedUsersCount(authUser.id),
      ]);

      console.log(
        `[getReceivedUsers] 获取到 ${receivedUsers.length} 个received users，总数: ${totalCount}`
      );

      // 格式化返回数据
      const formattedData = receivedUsers.map((item) => ({
        id: item.id,
        scannedAt: item.scannedAt,
        user: {
          id: item.scanned.id,
          username: item.scanned.username,
          level: item.scanned.level || 0,
          fakeAvatar: item.scanned.fakeAvatar || null,
          birthday: item.scanned.birthday || null,
          gender: item.scanned.gender || null,
          title: item.scanned.title || null,
          assetCertification: item.scanned.asset_certification,
        },
      }));

      console.log("formattedData", JSON.stringify(formattedData));

      const totalPages = Math.ceil(totalCount / limit);
      const hasNextPage = validatedPage < totalPages;
      const hasPreviousPage = validatedPage > 1;

      ctx.send({
        data: formattedData,
        meta: {
          pagination: {
            page: validatedPage,
            pageSize: limit,
            pageCount: totalPages,
            total: totalCount,
            hasNextPage,
            hasPreviousPage,
          },
        },
      });
    } catch (error) {
      console.error("[getReceivedUsers] 获取received users列表失败:", error);
      ctx.internalServerError("获取received users列表失败");
    }
  },
};
