'use strict';

const fs = require('fs');
const path = require('path');

const authStrategy = require('./strategies/users-permissions');
const sanitizers = require('@strapi/plugin-users-permissions/server/utils/sanitize/sanitizers');

module.exports = ({ strapi }) => {
  strapi.container.get('auth').register('content-api', authStrategy);
  strapi.sanitizers.add('content-api.output', sanitizers.defaultSanitizeOutput);

  if (strapi.plugin('graphql')) {
    require('@strapi/plugin-users-permissions/server/graphql')({ strapi });
  }

  if (strapi.plugin('documentation')) {
    const specPath = require.resolve('@strapi/plugin-users-permissions/documentation/content-api.yaml');
    const spec = fs.readFileSync(specPath, 'utf8');

    strapi
      .plugin('documentation')
      .service('override')
      .registerOverride(spec, {
        pluginOrigin: 'users-permissions',
        excludeFromGeneration: ['users-permissions'],
      });
  }
};
