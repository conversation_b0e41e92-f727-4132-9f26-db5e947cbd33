// @ts-nocheck
"use strict";

/**
 * User.js service
 *
 * @description: A set of functions similar to controller's actions to avoid code duplication.
 */

const crypto = require("crypto");
const bcrypt = require("bcryptjs");
const urlJoin = require("url-join");

const {
  getAbsoluteAdminUrl,
  getAbsoluteServerUrl,
  sanitize,
} = require("@strapi/utils");
const { getService } = require("@strapi/plugin-users-permissions/server/utils");
const { isWhitelisted, getTotalExpenses } = require("../../../../utils/redis");
const { checkObjectKeys } = require("../../../../utils");

module.exports = ({ strapi }) => ({
  /**
   * Promise to count users
   *
   * @return {Promise}
   */

  count(params) {
    return strapi
      .query("plugin::users-permissions.user")
      .count({ where: params });
  },

  /**
   * Promise to search count users
   *
   * @return {Promise}
   */

  /**
   * Promise to add a/an user.
   * @return {Promise}
   */
  async add(values) {
    return strapi.entityService.create("plugin::users-permissions.user", {
      data: values,
      populate: ["role"],
    });
  },

  /**
   * Promise to edit a/an user.
   * @param {string} userId
   * @param {object} params
   * @return {Promise}
   */
  async edit(userId, params = {}) {
    return strapi.entityService.update(
      "plugin::users-permissions.user",
      userId,
      {
        data: params,
        populate: ["role"],
      }
    );
  },

  /**
   * Promise to fetch a/an user.
   * @return {Promise}
   */
  fetch(id, params) {
    return strapi.entityService.findOne(
      "plugin::users-permissions.user",
      id,
      params
    );
  },

  /**
   * Promise to fetch authenticated user.
   * @return {Promise}
   */
  fetchAuthenticatedUser(id) {
    return strapi
      .query("plugin::users-permissions.user")
      .findOne({ where: { id }, populate: ["role"] });
  },

  fetchAuthenticatedUserPopulateAll(id) {
    return strapi.query("plugin::users-permissions.user").findOne({
      where: { id },
      populate: {
        avatar: {
          select: ["id", "url", "formats"],
        },
        fakeAvatar: {
          select: ["id", "url", "formats"],
        },
        photos: {
          select: ["id", "url", "formats"],
        },
        certificate: {
          select: ["id", "url", "formats"],
        },
        certificateList: {
          select: ["id", "url", "formats"],
        },
        interests: true,
        university: {
          populate: {
            logo: {
              select: ["id", "url", "formats"],
            },
          },
        },
        splashImage: {
          select: ["id", "url", "formats"],
        },
        idcard: {
          select: ["id", "url", "formats"],
        },
        asset_verifications: {
          populate: {
            proof: {
              select: ["id", "url", "formats"],
            },
            type: true,
            name: true,
            description: true,
          },
        },
        asset_certification: {
          populate: {
            // asset_verifications: {
            //   populate: {
            //     proof: {
            //       select: ["id", "url", "formats"],
            //     },
            //     type: true,
            //     name: true,
            //     description: true,
            //   },
            // },
            review_users: {
              select: ["id", "firstname", "lastname"],
            },
            issuedate: true,
            expirydate: true,
            asset_result: true,
          },
        },
      },
    });
  },

  async editPopulateAll(userId, params = {}) {
    return strapi.entityService.update(
      "plugin::users-permissions.user",
      userId,
      {
        data: params,
        populate: {
          role: true,
          avatar: {
            fields: ["id", "url", "formats"],
          },
          fakeAvatar: {
            fields: ["id", "url", "formats"],
          },
          // photos: {
          //   fields: ["id", "url"],
          // },
          splashImage: {
            fields: ["id", "url", "formats"],
          },
          certificate: {
            fields: ["id", "url"],
          },
          certificateList: {
            fields: ["id", "url", "formats"],
          },
          interests: true,
          university: {
            populate: {
              logo: {
                fields: ["id", "url", "formats"],
              },
            },
          },
        },
      }
    );
  },

  /**
   * Promise to fetch all users.
   * @return {Promise}
   */
  fetchAll(params) {
    return strapi.entityService.findMany(
      "plugin::users-permissions.user",
      params
    );
  },

  /**
   * Promise to remove a/an user.
   * @return {Promise}
   */
  async remove(params) {
    return strapi
      .query("plugin::users-permissions.user")
      .delete({ where: params });
  },

  async editBalance(userId, balance) {
    const totalExpenses = await getTotalExpenses(userId);
    await strapi.entityService.update(
      "plugin::users-permissions.user",
      userId,
      {
        data: { balance, totalExpenses },
      }
    );
  },

  validatePassword(password, hash) {
    return bcrypt.compare(password, hash);
  },

  async sendConfirmationEmail(user) {
    const userPermissionService = getService("users-permissions");
    const pluginStore = await strapi.store({
      type: "plugin",
      name: "users-permissions",
    });
    const userSchema = strapi.getModel("plugin::users-permissions.user");

    const settings = await pluginStore
      .get({ key: "email" })
      .then((storeEmail) => storeEmail.email_confirmation.options);

    // Sanitize the template's user information
    const sanitizedUserInfo = await sanitize.sanitizers.defaultSanitizeOutput(
      userSchema,
      user
    );

    const confirmationToken = crypto.randomBytes(20).toString("hex");

    await this.edit(user.id, { confirmationToken });

    const apiPrefix = strapi.config.get("api.rest.prefix");

    try {
      settings.message = await userPermissionService.template(
        settings.message,
        {
          URL: urlJoin(
            getAbsoluteServerUrl(strapi.config),
            apiPrefix,
            "/auth/email-confirmation"
          ),
          SERVER_URL: getAbsoluteServerUrl(strapi.config),
          ADMIN_URL: getAbsoluteAdminUrl(strapi.config),
          USER: sanitizedUserInfo,
          CODE: confirmationToken,
        }
      );

      settings.object = await userPermissionService.template(settings.object, {
        USER: sanitizedUserInfo,
      });
    } catch {
      strapi.log.error(
        '[plugin::users-permissions.sendConfirmationEmail]: Failed to generate a template for "user confirmation email". Please make sure your email template is valid and does not contain invalid characters or patterns'
      );
      return;
    }

    // Send an email to the user.
    await strapi
      .plugin("email")
      .service("email")
      .send({
        to: user.email,
        from:
          settings.from.email && settings.from.name
            ? `${settings.from.name} <${settings.from.email}>`
            : undefined,
        replyTo: settings.response_email,
        subject: settings.object,
        text: settings.message,
        html: settings.message,
      });
  },

  async checkWhitelisted(id) {
    try {
      const isProd = strapi.config.server.isProd;
      return isProd === true ? await isWhitelisted(id) : true;
    } catch (error) {
      strapi.log.info("Failed to obtain the whitelist status.");
      return false;
    }
  },

  async checkDataIntegrity(user) {
    //Check whether the user information is complete
    const confirmed = checkObjectKeys(user, [
      "username",
      "gender",
      "birthday",
      "introduction",
      "photos",
      "interests",
      "university",
    ]);
    if (confirmed !== user.confirmed) {
      user.confirmed = confirmed;
      strapi.entityService.update("plugin::users-permissions.user", user.id, {
        data: { confirmed },
      });
    }
    return user;
  },
});
