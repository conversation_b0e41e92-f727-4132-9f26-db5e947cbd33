# AI头像生成功能

本功能使用OpenAI API来根据用户的现有头像生成AI增强版头像，实现在users-permissions扩展中。

## 前提条件

1. 安装依赖项：
   ```bash
   npm install openai axios
   # 或者使用yarn
   yarn add openai axios
   ```

2. 在环境变量中添加OpenAI API密钥：
   在项目根目录的`.env`文件中添加以下内容：
   ```
   OPENAI_API_KEY=你的OpenAI_API密钥
   ```

## 技术实现

本功能使用OpenAI的官方JavaScript SDK进行图像变体生成。主要流程如下：

1. 用户必须先登录，API使用当前登录用户的信息
2. 下载用户的现有头像
3. 使用OpenAI的`images.createVariation` API生成图像变体
4. 返回生成的图像URL供用户预览
5. 用户选择后将图像保存到Strapi媒体库，并更新用户的`fakeAvatar`字段

## API端点

本功能添加了两个新的API端点：

1. **生成AI头像**
   - 路径: `POST /api/users/generate-ai-avatar`
   - 功能: 根据当前登录用户的头像生成AI增强版的头像
   - 认证: 需要用户登录，使用JWT认证
   - 返回:
     ```json
     {
       "success": true,
       "image": "生成的图像URL"
     }
     ```

2. **保存AI头像**
   - 路径: `POST /api/users/save-ai-avatar`
   - 功能: 将生成的AI头像保存为当前登录用户的fakeAvatar
   - 认证: 需要用户登录，使用JWT认证
   - 参数:
     - `imageUrl`: 生成的图像URL (请求体)
   - 返回:
     ```json
     {
       "success": true,
       "user": {
         "id": "用户ID",
         "fakeAvatar": {
           "id": "媒体ID",
           "url": "媒体URL"
         }
       }
     }
     ```

## 使用流程

1. 确保用户已登录并有个人头像
2. 调用生成AI头像API
3. 在前端展示生成的图像
4. 用户选择后，调用保存AI头像API
5. 头像会被保存为用户的`fakeAvatar`字段

## 注意事项

- 生成的图像默认为1024x1024像素
- 使用的是OpenAI的图像变体API，不需要提供prompt
- 请确保用户的原始头像符合OpenAI的使用政策
- 请确保有足够的OpenAI API额度
- 此功能在用户扩展中实现，而不是plugins中，与用户数据紧密关联 