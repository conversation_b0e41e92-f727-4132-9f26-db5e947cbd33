module.exports = (plugin) => {
  plugin.bootstrap = require("./server/bootstrap");
  plugin.register = require("./server/register");
  plugin.services["providers"] = require("./server/services/providers");
  plugin.services[
    "providers-registry"
  ] = require("./server/services/providers-registry");
  plugin.services["user"] = require("./server/services/user");
  plugin.controllers["auth"] = require("./server/controllers/auth");
  plugin.controllers["user"] = require("./server/controllers/user");
  plugin.controllers[
    "contentmanageruser"
  ] = require("./server/controllers/content-manager-user");

  plugin.routes["content-api"].routes.unshift({
    method: "GET",
    path: "/users/balance",
    handler: "user.balance",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.unshift({
    method: "GET",
    path: "/users/reg-bonus",
    handler: "user.getRegisterBonus",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/delete",
    handler: "user.delete",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/send-email-verification",
    handler: "user.sendEmailVerification",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/email-verification",
    handler: "user.emailVerification",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/certificate-verification",
    handler: "user.certificateVerification",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/check-nickname",
    handler: "user.checkName",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/referral-verification",
    handler: "user.referralVerification",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/check-fakename",
    handler: "user.checkFakeName",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/connect-community",
    handler: "user.connectCommunity",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/users/verification/state",
    handler: "user.getVerificationState",
    config: {
      prefix: "",
    },
  });

  // 添加AI头像生成路由
  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/generate-ai-avatar",
    handler: "user.generateAIAvatar",
    config: {
      prefix: "",
    },
  });

  // 添加AI头像保存路由
  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/save-ai-avatar",
    handler: "user.saveAIAvatar",
    config: {
      prefix: "",
    },
  });

  // 添加异步AI头像生成路由
  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/generate-ai-avatar-async",
    handler: "user.generateAIAvatarAsync",
    config: {
      prefix: "",
    },
  });

  // 添加AI头像任务状态查询路由
  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/users/ai-avatar-task/:taskId",
    handler: "user.getAIAvatarTaskStatus",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "PUT",
    path: "/users/idcard",
    handler: "user.saveIdCard",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/asset-verification",
    handler: "user.addAssetVerification",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "DELETE",
    path: "/users/asset-verification",
    handler: "user.removeAssetVerification",
    config: {
      prefix: "",
    },
  });

  plugin.routes["content-api"].routes.push({
    method: "POST",
    path: "/users/confirm-assets",
    handler: "user.confirmAssets",
    config: {
      prefix: "",
    },
  });

  // 添加二维码生成路由
  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/users/myqrcode/me",
    handler: "user.myQrCode",
    config: {
      prefix: "",
    },
  });

  // 添加扫码获取用户信息路由
  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/users/scan/:userId",
    handler: "user.scanUser",
    config: {
      prefix: "",
    },
  });

  // 添加获取received users列表路由
  plugin.routes["content-api"].routes.push({
    method: "GET",
    path: "/users/assetCertification/received-users",
    handler: "user.getReceivedUsers",
    config: {
      prefix: "",
    },
  });

  return plugin;
};
