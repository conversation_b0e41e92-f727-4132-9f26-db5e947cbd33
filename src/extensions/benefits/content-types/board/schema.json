{"kind": "collectionType", "collectionName": "benefits_boards", "info": {"singularName": "board", "pluralName": "boards", "displayName": "BenefitsBoard", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"name": {"type": "string"}, "locales": {"type": "json"}, "description": {"type": "string"}, "related": {"type": "string"}, "blocked": {"type": "boolean", "default": false}, "type": {"type": "enumeration", "enum": ["public", "private"]}, "creator": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "postingPermission": {"type": "integer"}, "subscribers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "coupons": {"type": "relation", "relation": "oneToMany", "target": "plugin::benefits.coupon", "mappedBy": "board"}, "order": {"type": "integer"}, "isDefault": {"type": "boolean", "default": false}, "userLevelLimited": {"type": "string"}}}