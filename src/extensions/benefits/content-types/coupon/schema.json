{"kind": "collectionType", "collectionName": "benefits_coupons", "info": {"singularName": "coupon", "pluralName": "coupons", "displayName": "BenefitsCoupon", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"title": {"type": "string"}, "content": {"type": "richtext"}, "media": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "pinned": {"type": "boolean", "default": false}, "blocked": {"type": "boolean", "default": false}, "removed": {"type": "boolean", "default": false}, "likeCount": {"type": "integer", "default": 0}, "board": {"type": "relation", "relation": "manyToOne", "target": "plugin::benefits.board", "inversedBy": "coupons"}, "likers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "expiresAt": {"type": "datetime", "default": "2039-12-31T16:00:00.000Z", "required": false}, "phone": {"type": "string"}, "level": {"type": "integer"}, "description": {"type": "text"}}}