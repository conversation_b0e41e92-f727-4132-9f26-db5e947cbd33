{"kind": "collectionType", "collectionName": "comm_posts", "info": {"singularName": "post", "pluralName": "posts", "displayName": "CommunityPost", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"authorUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "title": {"type": "string"}, "content": {"type": "richtext"}, "media": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "pinned": {"type": "boolean", "default": false}, "blocked": {"type": "boolean", "default": false}, "removed": {"type": "boolean", "default": false}, "likeCount": {"type": "integer", "default": 0}, "board": {"type": "relation", "relation": "manyToOne", "target": "plugin::community.board", "inversedBy": "posts"}, "likers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "dislikers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "comments": {"type": "relation", "relation": "oneToMany", "target": "plugin::community.comment", "mappedBy": "post"}, "weight": {"type": "integer", "min": 0}, "adUrl": {"type": "string"}}}