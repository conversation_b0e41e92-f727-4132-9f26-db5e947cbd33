{"kind": "collectionType", "collectionName": "comm_comments", "info": {"singularName": "comment", "pluralName": "comments", "displayName": "CommunityComment", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"content": {"type": "text", "maxLength": 10000}, "blocked": {"type": "boolean", "default": false}, "removed": {"type": "boolean", "default": false}, "threadOf": {"type": "relation", "relation": "oneToOne", "target": "plugin::community.comment"}, "post": {"type": "relation", "relation": "manyToOne", "target": "plugin::community.post", "inversedBy": "comments"}, "authorUser": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user", "configurable": false}, "likers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "dislikers": {"type": "relation", "relation": "oneToMany", "target": "plugin::users-permissions.user"}, "mentions": {"type": "json"}}}