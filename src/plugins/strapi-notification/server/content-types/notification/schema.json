{"info": {"singularName": "notification", "pluralName": "notifications", "collectionName": "notifications", "displayName": "Notification", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {"content-manager": {"visible": true}, "content-type-builder": {"visible": true}}, "attributes": {"user": {"type": "relation", "relation": "oneToOne", "target": "plugin::users-permissions.user"}, "thumbnail": {"allowedTypes": ["images"], "type": "media", "multiple": false}, "thumbnailUrl": {"type": "string"}, "text": {"type": "string"}, "link": {"type": "string"}, "message": {"type": "json"}, "type": {"type": "string"}, "viewed": {"type": "boolean"}, "audience": {"type": "enumeration", "enum": ["PERSONAL", "ALL"]}}}