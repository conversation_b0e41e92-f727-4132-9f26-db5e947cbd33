'use strict';
const dayjs = require('dayjs');

module.exports = ({ strapi }) => ({
  async find(ctx) {
    const authUser = ctx.state.user || { id: 2 };
    const locale = ctx.state.locale;

    const { offset = 0, limit = 20 } = ctx.query;
    const entries = await strapi.db.query('plugin::notification.notification').findMany({
      where: {
        user: { id: authUser.id }
      },
      orderBy: { id: 'DESC' },
      offset: offset,
      limit: limit,
    });

    return await Promise.all(entries.map(item => {
      if (item.message && locale) {
        item.message = item.message[locale] || item.message['ko']
      }
      item.thumbnail = {
        url: item.thumbnailUrl || "https://cdn.meetok.me/2024/01/10/meetok_logo_9156152bb0.png"
      }
      return item;
    }));
  },

  async view(ctx) {
    const authUser = ctx.state.user;
    const { id } = ctx.params;
    const entry = await strapi.db.query('plugin::notification.notification').findOne({
      where: { id: id, user: { id: authUser.id } }
    });
    await strapi.entityService.update('plugin::notification.notification', id, {
      data: {
        viewed: true,
      },
    });
    return { ok: true }
  },

  async viewAll(ctx) {
    const authUser = ctx.state.user;
    const entries = await strapi.db.query('plugin::notification.notification').findMany({
      select: ['id'],
      where: { user: { id: authUser.id }, viewed: false }
    });
    const ids = entries.map(ele => { return ele.id });
    if (ids.length > 0)
      await strapi.db.query("plugin::notification.notification").updateMany({
        where: {
          id: { $in: ids }
        },
        data: {
          viewed: true
        }
      });

    return { ok: true }
  },

  async badge(ctx) {
    const authUser = ctx.state.user;
    const count = await strapi.db.query('plugin::notification.notification').count({
      where: { user: { id: authUser.id }, viewed: false }
    });
    return { count: count }
  },
});
