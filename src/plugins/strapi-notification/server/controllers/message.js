'use strict';

const { getService } = require('../utils')

module.exports = ({ strapi }) => ({
    async find(ctx) {
        const { page = 1, pageSize = 20 } = ctx.query;
        const { results, pagination } = await getService('message').pageAll({
            sort: { id: 'desc' },
            page: page,
            pageSize: pageSize,
        });
        ctx.body = {
            results,
            pagination
        };
    },

    /**
     * The administrator sends a push.
     * @param {*} ctx 
     */
    async post(ctx) {
        const { audienceType, segments, title, content, imageUrl, link = 'meetok://today' } = ctx.request.body;
        await getService('message').add({
            title,
            content,
            imageUrl,
            link,
            audienceType,
            segments,
            status: 'sent'
        });
        let params = {
            headings: {
                en: title,
                vi: title
            },
            contents: {
                en: content,
                vi: content
            },
            audienceType,
            link,
            imageUrl
        }
        if (audienceType === 'segment' && segments) {
            params.audiences = segments.map(item => String(item.id));
        }
        await getService('notification').sendOneSignalNotification(params);
        return { ok: true }
    }
});
