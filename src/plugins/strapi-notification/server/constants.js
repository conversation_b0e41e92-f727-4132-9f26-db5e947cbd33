'use strict';

const DEEP_LINKS = {
  HOME: "starchex://home",
  TODAY: "starchex://today",
  PASSED: "starchex://passed",
  GET_HEARTS: "starchex://get_hearts",
  MATCHED: "starchex://matched",
  CONVERSATIONS: "starchex://conversations",
  TRANSACTION: "starchex://transaction",
  COMMNUNITY: "starchex://community",
};

const NOTIFICATION_TYPE = {
    //Notify immeidately after signing up
    REGISTER: 'REGISTER',
    //Notify immediately when a user's verification passed review.
    VERIFICATION_PASSED: 'VERIFICATION_PASSED',
    VERIFICATION_REJECT: 'VERIFICATION_REJECT',
    //Notifications will be made if user's last login time Is over 24 hours or 72 hours before now.
    ACCESS_REMINDER: 'ACCESS_REMINDER',
    //Notifications will be made as soon as user got a heart card
    GOT_HEART: 'GOT_HEART',
    // Notifications will be made when there're more than 2 Given Cards available. (one time per day for each user)
    GIVEN_REMINDER: 'GIVEN_REMINDER',
    //Notifications will be made after every 24 hours (max 3 times) when there are hearts card unchecked.
    GOT_HEART_REMINDER: 'GOT_HEART_REMINDER',
    //Notify immeidately when there's a match happen.
    MATCHED: 'MATCHED',
    //Notifications will be made every 8:00 am when there's more than one matched friend and they haven't started chat yet.
    MATCHED_REMINDER: 'MATCHED_REMINDER',
    //Notifications will be made every
    HIGH_EVAL: 'HIGH_EVAL',
    //Notification immediately when got earns. (new user registered with refer code or any other earnings)
    BONUS: 'BONUS',
    //Got reply comment in community board / max 10 a day
    COMMENT_REMINDER: 'COMMENT_REMINDER',
    //Once the card is delivered, a push message must arrive at every event so that the user can know it.
    DAILY_GIVEN: 'DAILY_GIVEN',
    COMMENT_MENTION: 'COMMENT_MENTION',
    CHAT_UNREAD_REMINDER: 'CHAT_UNREAD_REMINDER',
    HEART_REJECT: "HEART_REJECT",
}

module.exports = {
    DEEP_LINKS,
    NOTIFICATION_TYPE
};
