'use strict';


module.exports = {
    type: 'content-api',
    routes: [
        {
            method: 'GET',
            path: '/notifications',
            handler: 'notification.find',
            config: {
                prefix: ''
            }
        },
        {
            method: 'GET',
            path: '/notifications/badge',
            handler: 'notification.badge',
            config: {
                prefix: ''
            }
        },
        {
            method: 'GET',
            path: '/notifications/:id/view',
            handler: 'notification.view',
            config: {
                prefix: ''
            }
        },
        {
            method: 'GET',
            path: '/notifications/viewall',
            handler: 'notification.viewAll',
            config: {
                prefix: ''
            }
        },
    ],
};
