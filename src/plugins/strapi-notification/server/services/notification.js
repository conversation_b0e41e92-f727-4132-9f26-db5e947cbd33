'use strict';
const OneSignal = require('@onesignal/node-onesignal');
const {
  getRegisterMsg,
  getVerificationPassedMsg,
  getVerificationRejectedMsg,
  getAccessReminderMsg,
  getDailyGivenMsg,
  getGivenReminderMsg,
  getGotHeartMsg,
  getCheckGotHeartMsg,
  getMatchedMsg,
  getCheckMatchedMsg,
  getHighEvaluationsMsg,
  getBonusMsg,
  getCommentReminderMsg,
  getCommentMentionMsg,
  getChatUnReadMsg,
  getHeartRejectedMsg
} = require('../utils/getNotificationMessage');

const { DEEP_LINKS, NOTIFICATION_TYPE } = require('../constants')
const { AVATAR } = require('../../../../constants')

module.exports = ({ strapi }) => ({
  async processNotificationMessage(msg) {
    msg = JSON.parse(msg.content.toString());
    const data = await this.getNotificationData(msg);
    if (!data) return;
    const { type } = msg;

    if ([NOTIFICATION_TYPE.CHAT_UNREAD_REMINDER, NOTIFICATION_TYPE.DAILY_GIVEN].includes(type)) {
      await this.pushToExternalUsers(data);
    } else {
      await this.saveAndPushNotification(data);
    }
  },

  async getNotificationData(msg) {
    const { type } = msg;
    let data = null;
    switch (type) {
      // case NOTIFICATION_TYPE.REGISTER:
      //   data = await this.getRegisterNotiData(msg);
      //   break;
      // case NOTIFICATION_TYPE.VERIFICATION_PASSED:
      //   data = await this.getVerificationPassedData(msg);
      //   break;
      // case NOTIFICATION_TYPE.VERIFICATION_REJECT:
      //   data = await this.getVerificationRejectedData(msg);
      //   break;
      // case NOTIFICATION_TYPE.ACCESS_REMINDER:
      //   data = await this.getAccessReminderData(msg);
      //   break;
      // case NOTIFICATION_TYPE.DAILY_GIVEN:
      //   data = await this.getDailyGivenData(msg);
      //   break;
      // case NOTIFICATION_TYPE.GIVEN_REMINDER:
      //   data = await this.getGivenReminderData(msg);
      //   break;
      // case NOTIFICATION_TYPE.GOT_HEART:
      //   data = await this.getGotHeartData(msg);
      //   break;
      // case NOTIFICATION_TYPE.GOT_HEART_REMINDER:
      //   data = await this.getGotHeartReminderData(msg);
      //   break;
      // case NOTIFICATION_TYPE.MATCHED:
      //   data = await this.getMatchedData(msg);
      //   break;
      // case NOTIFICATION_TYPE.MATCHED_REMINDER:
      //   data = await this.getMatchedReminderData(msg);
      //   break;
      // case NOTIFICATION_TYPE.HIGH_EVAL:
      //   data = await this.getHighEvalData(msg);
      //   break;
      // case NOTIFICATION_TYPE.BONUS:
      //   data = await this.getBonusData(msg);
      //   break;
      case NOTIFICATION_TYPE.COMMENT_REMINDER:
        data = await this.getCommentReminderData(msg);
        break;
      case NOTIFICATION_TYPE.COMMENT_MENTION:
        data = await this.getCommentMentionData(msg);
        break;
      // case NOTIFICATION_TYPE.CHAT_UNREAD_REMINDER:
      //   data = await this.getChatUnReadReminderData(msg);
      //   break;
      // case NOTIFICATION_TYPE.HEART_REJECT:
      //   data = await this.getHeartRejectedData(msg);
      //   break;
    }
    return data;
  },

  async getRegisterNotiData(msg) {
    let { user, type } = msg;
    if (!user) return null;

    return {
      user,
      type,
      message: getRegisterMsg(user.username),
      link: DEEP_LINKS.HOME,
      thumbnailUrl: AVATAR.LOGO_URL
    }
  },

  async getVerificationPassedData(msg) {
    let { user, type } = msg;
    if (!user) return null;
    user = await strapi.plugin('meedata').service('users').findOne(Number(user.id));

    return {
      user,
      type,
      message: getVerificationPassedMsg(user.username),
      link: DEEP_LINKS.TODAY,
      thumbnailUrl: AVATAR.LOGO_URL
    }
  },

  async getVerificationRejectedData(msg) {
    let { user, type, rejectionReason } = msg;
    if (!user) return null;
    user = await strapi.plugin('meedata').service('users').findOne(Number(user.id));

    return {
      user,
      type,
      message: getVerificationRejectedMsg(user.username, rejectionReason),
      link: DEEP_LINKS.TODAY,
      thumbnailUrl: AVATAR.LOGO_URL
    }
  },

  async getAccessReminderData(msg) {
    let { user, type } = msg;
    if (!user) return null;

    return {
      user,
      type,
      message: getAccessReminderMsg(user.username),
      link: DEEP_LINKS.TODAY,
      thumbnailUrl: AVATAR.LOGO_URL
    }
  },

  async getDailyGivenData(msg) {
    let { type, userIds } = msg;

    return {
      type,
      userIds,
      message: getDailyGivenMsg(),
      link: DEEP_LINKS.TODAY,
      thumbnailUrl: AVATAR.LOGO_URL
    }
  },

  async getGivenReminderData(msg) {
    let { user, type, count } = msg;
    if (!user) return null;

    return {
      user,
      type,
      message: getGivenReminderMsg(count),
      link: DEEP_LINKS.TODAY,
      thumbnailUrl: AVATAR.LOGO_URL
    }
  },

  async getGotHeartData(msg) {
    let { user, type, senderId } = msg;
    if (!user) return null;
    const sender = await strapi.plugin('meedata').service('users').findOne(Number(senderId));
    if (!sender) return null;
    return {
      user,
      type,
      message: getGotHeartMsg(),
      link: DEEP_LINKS.TODAY,
      thumbnailUrl: sender.avatar.url
    }
  },

  async getGotHeartReminderData(msg) {
    let { user, type } = msg;
    if (!user) return null;

    return {
      user,
      type,
      message: getCheckGotHeartMsg(),
      link: DEEP_LINKS.PASSED,
      thumbnailUrl: AVATAR.LOGO_URL
    }
  },

  async getMatchedData(msg) {
    let { user, type, friendId } = msg;
    if (!user) return null;
    user = await strapi.plugin('meedata').service('users').findOne(Number(user.id));
    const friend = await strapi.plugin('meedata').service('users').findOne(Number(friendId));
    if (!friend) return null;
    return {
      user,
      type,
      message: getMatchedMsg(friend.username),
      link: DEEP_LINKS.CONVERSATIONS,
      thumbnailUrl: friend.avatar.url,
    }
  },

  async getMatchedReminderData(msg) {
    let { user, type } = msg;
    if (!user) return null;

    return {
      user,
      type,
      message: getCheckMatchedMsg(),
      link: DEEP_LINKS.CONVERSATIONS,
      thumbnailUrl: AVATAR.LOGO_URL,
    }
  },

  //{type: NOTIFICATION_TYPE.HIGH_EVAL,user: { id: item.userId },count: item.count}
  async getHighEvalData(msg) {
    let { user, type, count } = msg;
    if (!user) return null;
    return {
      user,
      type,
      message: getHighEvaluationsMsg(count),
      link: DEEP_LINKS.PASSED,
      thumbnailUrl: AVATAR.EVAL_URL,
    }
  },

  async getBonusData(msg) {
    let { user, type, note } = msg;
    if (!user) return null;
    return {
      user,
      type,
      message: getBonusMsg(note),
      link: DEEP_LINKS.TRANSACTION,
      thumbnailUrl: AVATAR.LOGO_URL,
    }
  },

  async getCommentReminderData(msg) {
    let { user, type, commentor, board, postId, commentId } = msg;
    if (!user) return null;
    const communityLink = DEEP_LINKS.COMMNUNITY;
    const link = board ? `${communityLink}?boardId=${board.id}&postId=${postId}&commentId=${commentId}` : communityLink;
    return {
      user,
      type,
      message: getCommentReminderMsg(commentor.username),
      link,
      thumbnailUrl: AVATAR.LOGO_URL,
    }
  },

  async getCommentMentionData(msg) {
    let { user, type, commentor, board, postId, commentId } = msg;
    if (!user) return null;
    const communityLink = DEEP_LINKS.COMMNUNITY;
    const link = board ? `${communityLink}?boardId=${board.id}&postId=${postId}&commentId=${commentId}` : communityLink;
    return {
      user,
      type,
      message: getCommentMentionMsg(),
      link,
      thumbnailUrl: AVATAR.LOGO_URL,
    }
  },

  async getChatUnReadReminderData(msg) {
    let { type, userIds } = msg;
    return {
      type,
      userIds,
      message: getChatUnReadMsg(),
      link: DEEP_LINKS.CONVERSATIONS
    }
  },

  async getHeartRejectedData(msg) {
    let { user, type, rejecter, ex } = msg;
    if (!user) return null;
    return {
      user,
      type,
      message: getHeartRejectedMsg(rejecter, ex),
      link: DEEP_LINKS.TODAY,
      thumbnailUrl: AVATAR.LOGO_URL,
    }
  },

  /**
   * @param {{ user: any; thumbnail: any; type: any; link: any; message: any; }} data
   */
  async saveAndPushNotification(data) {
    let { user, thumbnailUrl, type, link, message, imageUrl } = data;
    user = await strapi.plugin('meedata').service('users').findOne(Number(user.id));
    if (!user) return;
    //save notification
    const notification = {
      thumbnailUrl: thumbnailUrl || AVATAR.LOGO_URL,
      user: user,
      viewed: false,
      text: `${message.en.title} ${message.en.desc}`,
      type,
      link,
      message,
      audience: 'PERSONAL'
    }
    await strapi.entityService.create('plugin::notification.notification', {
      data: notification
    });
    if (!user.receiveNotification) return;

    const params = {
      headings: {
        en: message.en.title,
        vi: message.vi.title
      },
      contents: {
        en: message.en.desc,
        vi: message.vi.desc
      },
      link,
      imageUrl,
      audiences: [String(user.id)],
      audienceType: 'segment'
    }
    await this.sendOneSignalNotification(params);
  },

  async pushToExternalUsers(data) {
    let { link, message, userIds } = data;
    const params = {
      headings: {
        en: message.en.title,
        vi: message.vi.title
      },
      contents: {
        en: message.en.desc,
        vi: message.vi.desc
      },
      link,
      audiences: userIds,
      audienceType: 'segment'
    }

    await this.sendOneSignalNotification(params);
  },

  async saveAndPushAll(data) {
    let { thumbnailUrl, type, link, message, imageUrl } = data;

    //And even if users receive the push, please make sure they don't see this type content in their alarm list.
    const audienceType = NOTIFICATION_TYPE.DAILY_GIVEN === type ? 'active48' : 'all';

    if (NOTIFICATION_TYPE.DAILY_GIVEN != type) {
      //save notification
      const notification = {
        thumbnailUrl: thumbnailUrl || AVATAR.LOGO_URL,
        viewed: false,
        text: `${message.en.title} ${message.en.desc}`,
        type,
        link,
        message,
        audience: 'ALL'
      }
      await strapi.entityService.create('plugin::notification.notification', {
        data: notification
      });
    }

    const params = {
      headings: {
        en: message.en.title,
        vi: message.vi.title
      },
      contents: {
        en: message.en.desc,
        vi: message.vi.desc
      },
      link,
      imageUrl,
      audienceType
    }
    await this.sendOneSignalNotification(params);
  },

  /**
   * headings: {en: xxxx, vi: xxxx}
   * contents: {en: xxxx, vi: xxxx}
   * link: deeplink or url
   * audiences: ['id1', 'id2', 'id3', 'id4']
   * audienceType: all, active48, segment
   * @param {*} param0
   */
  async sendOneSignalNotification({ headings, contents, link, imageUrl, audienceType = 'segment', audiences }) {
    try {
      strapi.log.info('================ push onesignal notification message ==================');

      //The test server does not send push to everyone.
      const isProd = strapi.config.server.isProd;

      if (['all', 'ALL', 'active48'].includes(audienceType) && !isProd) return;

      const { appId, appKey } = strapi.config.get('plugin.notification');
      const client = new OneSignal.DefaultApi(OneSignal.createConfiguration({ appKey }));
      const notification = new OneSignal.Notification();
      notification.app_id = appId;

      if (headings) {
        notification.headings = headings;
      }
      if (contents) {
        notification.contents = contents;
      }
      notification.app_url = link || "meetok://today";

      if (audienceType === 'segment') {
        notification.include_external_user_ids = audiences;
      } else {
        notification.included_segments = audienceType == 'all' ? ['All'] : [audienceType];
      }

      if (imageUrl) {
        notification.big_picture = imageUrl;
        notification.ios_attachments = { "1": imageUrl }
      }
      notification.is_android = true;
      notification.is_ios = true;
      notification.priority = 10;
      client.createNotification(notification);
    } catch (error) {
      strapi.log.error('push onesignal notification message failed', error);
    }
  },
});
