// @ts-nocheck
'use strict';

/**
 * User.js service
 *
 * @description: A set of functions similar to controller's actions to avoid code duplication.
 */

module.exports = ({ strapi }) => ({

    async add(values) {
        return strapi.entityService.create('plugin::notification.message', {
            data: values,
        });
    },

    async edit(id, params = {}) {
        return await strapi.entityService.update('plugin::notification.message', id, {
            data: params
        });
    },

    async fetch(id, params) {
        return await strapi.entityService.findOne('plugin::notification.message', id, params);
    },

    async pageAll(params) {
        return await strapi.entityService.findPage('plugin::notification.message', params);
    }
});
