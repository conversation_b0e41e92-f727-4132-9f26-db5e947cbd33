const locales = ["en", "vi"];

const getRegisterMsg = (username) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `${username}, chúc mừng bạn đã tham gia!`;
      desc = "Bạn có thể tìm kiếm bạn bè ngay bây giờ!";
    } else {
      title = `${username}, congratulations on joining!`;
      desc = "You can seek friends now! ";
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getVerificationPassedMsg = (username) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck`;
      desc = `${username}, bạn đã được xác minh. Từ giờ bạn có thể tìm và kết nối với bạn bè rồi đó.`;
    } else {
      title = `Starcheck`;
      desc = `${username}, you have been verified, now you can start looking for friends!`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getVerificationRejectedMsg = (username, rejectionReason) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck`;
      desc =
        rejectionReason?.vi ||
        `${username}, Thông tin nhận dạng của bạn không được xác thực. Vui lòng gửi lại thông tin nhận dạng của bạn. Cảm ơn.`;
    } else {
      title = `Starcheck`;
      desc =
        rejectionReason?.en ||
        `${username}, Your identity information did not pass the verification. Please resubmit your identity information. Thank you.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getAccessReminderMsg = (username) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck`;
      desc = `${username}, Hôm nay bạn chưa dùng app. Quay lại và tìm kiếm một nửa phù hợp ngay thôi nào!`;
    } else {
      title = `Starcheck`;
      desc = `${username}, You haven't been active for a day. Come and find the one in your mind.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getGivenReminderMsg = (count) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck`;
      desc = `Bạn có ${count} thẻ chưa được mở ngày hôm nay. Đi để mở chúng.`;
    } else {
      title = `Starcheck`;
      desc = `You have ${count} given cards that have not been opened today. Go to open them.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getDailyGivenMsg = () => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck`;
      desc = `Bạn đã nhận được một thẻ mới. đi mở nó ra.`;
    } else {
      title = `Starcheck`;
      desc = `You got a new given card. go to open it.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getGotHeartMsg = () => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck`;
      desc = ` Ai đó vừa thả tim cho bạn. Bấm vào để biết người ấy là ai nhé!`;
    } else {
      title = `Starcheck`;
      desc = ` You got a heart. Click here to check it out.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getCheckGotHeartMsg = () => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck`;
      desc = ` Bạn có đoán được ai vừa thả tim cho bạn không. Bấm vào để được bật mí ngay!`;
    } else {
      title = `Starcheck`;
      desc = ` Someone sent you hearts. Click here to check it out.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getMatchedMsg = (friendName) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Chúc mừng!`;
      desc = `Bạn và ${friendName} đã match thành công!`;
    } else {
      title = `Congratulations!`;
      desc = ` You have been matched with ${friendName}.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getHighEvaluationsMsg = (count) => {
  const message = {};
  let title;
  let desc;
  const param = count > 99 ? `${count}+` : count;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Đánh giá cao`;
      desc = `${param} người đã cho bạn điểm cao! Xem họ là ai.`;
    } else {
      title = `High evaluation`;
      desc = `${param} people gave you high scores! See who they are.`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getCheckMatchedMsg = () => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck!`;
      desc = `Bạn và những người bạn match vẫn chưa có cuộc trò chuyện nào. Mở app và bắt đầu nói chuyện thôi nào!`;
    } else {
      title = `Starcheck!`;
      desc = `You have some matched friends that you haven't communicated with yet. Come and talk with them`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getBonusMsg = (note) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Bạn nhận được MeeCoins`;
      desc =
        note || `Số dư MeeCoins của bạn đã tăng lên, bấm vào check ngay thôi!`;
    } else {
      title = `Got MeeCoins`;
      desc = note || `Your MeeCoins have increased, go check it out!`;
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getCommentReminderMsg = (userName) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "ko") {
      title = "커뮤니티";
      desc = "내 게시글에 댓글이 달렸어요. 확인해보세요!";
    } else if (locale === "zh_CN") {
      title = "社区";
      desc = "你的帖子有新评论了，快来看看吧！";
    } else {
      title = "Community";
      desc = "Your post got a comment. Please open app to check it.";
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getCommentMentionMsg = () => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "ko") {
      title = "커뮤니티";
      desc = "댓글에서 멘션을 받았어요. 확인해보세요!";
    } else if (locale === "zh_CN") {
      title = "社区";
      desc = "有人在评论中@了你，快来看看吧！";
    } else {
      title = `Community`;
      desc =
        "Hey there! You've been mentioned.Come to check out the conversation!";
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getChatUnReadMsg = () => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = `Starcheck!`;
      desc =
        "Bạn có tin nhắn chưa đọc đang chờ bạn. Dành chút thời gian để xem những gì bạn đã bỏ qua đi!";
    } else {
      title = `Starcheck!`;
      desc =
        "You have unread messages waiting for you. Take a moment to see what you've missed!";
    }
    message[locale] = { title, desc };
  });
  return message;
};

const getHeartRejectedMsg = (rejecter, ex) => {
  const message = {};
  let title;
  let desc;
  locales.forEach((locale) => {
    if (locale === "vi") {
      title = "Bị từ chối!";
      desc = `${rejecter.username} đã từ chối trái tim bạn gửi.`;
    } else {
      title = "Rejected!";
      desc = `${rejecter.username} has rejected the heart you sent.`;
    }
    message[locale] = { title, desc, ex: ex[locale] };
  });
  return message;
};

module.exports = {
  getRegisterMsg,
  getVerificationPassedMsg,
  getVerificationRejectedMsg,
  getAccessReminderMsg,
  getDailyGivenMsg,
  getGivenReminderMsg,
  getGotHeartMsg,
  getCheckGotHeartMsg,
  getMatchedMsg,
  getCheckMatchedMsg,
  getHighEvaluationsMsg,
  getBonusMsg,
  getCommentReminderMsg,
  getCommentMentionMsg,
  getChatUnReadMsg,
  getHeartRejectedMsg,
};
