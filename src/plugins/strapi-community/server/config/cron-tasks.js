'use strict';
const { getService } = require("../utils");

module.exports = {
  communityTasks: ({ strapi }) => {
    // create cron check
    strapi.cron.add({
      '0 0 4 * * ?': async ({ strapi }) => {
        const res = await strapi.redis.connections.default.client.set('lock:stat_meetok_top_post', 1, 'ex', 120, 'nx');
        if (res === 'OK') {
          await getService('action').statisticsTopPosts();
        }
      }
    });
  },
};
