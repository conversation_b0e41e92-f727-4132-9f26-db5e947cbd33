"use strict";
const dayjs = require("dayjs");
const { getService } = require("../utils");
const _ = require("lodash");
const { incrUserPostCount, addPostCommenter } = require("../utils/redis");

module.exports = {
  async getActiveBoards(ctx) {
    return await getService("board").fetchAll({
      fields: ["id", "name", "description", "related", "type"],
      filters: {
        blocked: false,
      },
      sort: { order: "asc" },
    });
  },

  async findPosts(ctx) {
    let {
      authorName,
      boardId,
      blocked,
      pinned,
      startDate,
      endDate,
      page = 1,
      pageSize = 20,
    } = ctx.query;
    let filters = {};
    if (boardId) {
      filters = { ...filters, board: { id: boardId } };
    }
    if (authorName) {
      filters = {
        ...filters,
        authorUser: { fakeName: { $contains: authorName } },
      };
    }
    if (blocked) {
      filters = { ...filters, blocked: blocked === "1" };
    }
    if (pinned) {
      filters = { ...filters, pinned: pinned === "1" };
    }
    if (startDate) startDate = dayjs(startDate).startOf("day").valueOf();
    if (endDate) endDate = dayjs(endDate).endOf("day").valueOf();
    if (startDate && endDate) {
      filters = { ...filters, createdAt: { $between: [startDate, endDate] } };
    } else if (startDate) {
      filters = { ...filters, createdAt: { $gte: startDate } };
    } else if (endDate) {
      filters = { ...filters, createdAt: { $lte: endDate } };
    }

    const { results, pagination } = await getService("post").findPage({
      filters,
      sort: { id: "desc" },
      populate: {
        authorUser: {
          fields: [
            "username",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "regionCode",
            "financialStatus",
          ],
          populate: {
            fakeAvatar: {
              select: ["id", "url"],
            },
          },
        },
        media: {
          fields: ["url"],
        },
        board: true,
      },
      page: page,
      pageSize: pageSize,
    });
    ctx.body = {
      results: await getService("post").getCommonData(results),
      pagination,
    };
  },

  async postDetail(ctx) {
    const { id } = ctx.request.params;
    const result = await getService("post").fetch(id, {
      populate: {
        authorUser: {
          fields: [
            "username",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "regionCode",
            "financialStatus",
          ],
          populate: {
            fakeAvatar: {
              select: ["id", "url"],
            },
          },
        },
        media: {
          fields: ["name", "url", "ext", "mime", "size", "formats"],
        },
        board: true,
      },
    });
    if (!result) return result;

    const [post] = await getService("post").getCommonData([result]);
    return post;
  },

  async createPost(ctx) {
    const { boardId, authorUser } = ctx.request.body;
    // const isProd = strapi.config.server.isProd;
    // const authUser = isProd ? { id: 10000155 } : { id: 141 };

    const values = {
      ...ctx.request.body,
      board: boardId,
      authorUser: authorUser.id,
      publishedAt: new Date(),
      publicationState: "live",
    };
    const post = await getService("post").add(values);
    await addPostCommenter(post.id, authorUser.id);
    await incrUserPostCount(authorUser.id);
    return {
      ok: true,
    };
  },

  async updatePost(ctx) {
    const { id } = ctx.request.params;
    // const { blocked, pinned } = ctx.request.body;
    // let updateData = {};
    // if (_.has(ctx.request.body, 'blocked')) {
    //     updateData = { ...updateData, blocked }
    // }
    // if (_.has(ctx.request.body, 'pinned')) {
    //     updateData = { ...updateData, pinned }
    // }
    const { boardId } = ctx.request.body;
    if (_.has(ctx.request.body, "boardId")) {
      ctx.request.body.board = boardId;
    }
    await getService("post").edit(id, ctx.request.body);
    return {
      ok: true,
    };
  },

  async updateComment(ctx) {
    const { id } = ctx.request.params;
    const { blocked } = ctx.request.body;
    let updateData = {};
    if (_.has(ctx.request.body, "blocked")) {
      updateData = { ...updateData, blocked };
    }
    await getService("comment").edit(id, updateData);
    return {
      ok: true,
    };
  },

  async findComments(ctx) {
    let {
      postId,
      authorName,
      blocked,
      startDate,
      endDate,
      page = 1,
      pageSize = 20,
    } = ctx.query;
    let filters = {};
    if (postId) {
      filters = { ...filters, post: { id: postId } };
    }
    if (authorName) {
      filters = {
        ...filters,
        authorUser: { fakeName: { $contains: authorName } },
      };
    }
    if (blocked) {
      filters = { ...filters, blocked: blocked === "1" };
    }
    if (startDate) startDate = dayjs(startDate).startOf("day").valueOf();
    if (endDate) endDate = dayjs(endDate).endOf("day").valueOf();
    if (startDate && endDate) {
      filters = { ...filters, createdAt: { $between: [startDate, endDate] } };
    } else if (startDate) {
      filters = { ...filters, createdAt: { $gte: startDate } };
    } else if (endDate) {
      filters = { ...filters, createdAt: { $lte: endDate } };
    }

    const { results, pagination } = await getService("comment").findPage({
      filters,
      sort: { id: "desc" },
      populate: {
        authorUser: {
          fields: [
            "username",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "regionCode",
          ],
          populate: {
            fakeAvatar: {
              select: ["id", "url"],
            },
          },
        },
        post: {
          fields: ["id"],
        },
      },
      page: page,
      pageSize: pageSize,
    });
    ctx.body = {
      results: await getService("comment").getCommonData(results),
      pagination,
    };
  },
};
