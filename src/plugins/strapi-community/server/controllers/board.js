"use strict";

const { getService } = require("../utils");
const { sanitizeLocale } = require("../../../../utils/locale");
/**
 * postingPermission
 * 0: There are no limits.
 * 1: graduates have access.
 * 2: undergraduate have access.
 * 3: people who join.
 */

module.exports = {
  async find(ctx) {
    const authUser = ctx.state.user;
    const authUserLevel = authUser.level;
    const locale = ctx.state.locale;
    const { perm = "access" } = ctx.query;
    let boards = [];
    boards = await getService("board").findAll(
      authUser.id,
      perm,
      authUserLevel
    );
    return await sanitizeLocale(boards, locale);
  },
};
