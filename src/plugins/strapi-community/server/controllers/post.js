"use strict";

const {
  addPostLikeLog,
  removePostLikeLog,
  addPostDislikeLog,
  removePostDislikeLog,
  hasPostLiked,
  hasPostDisliked,
  incrUserPostCount,
  incrUserLikeCount,
  addPostCommenter,
  getPostCommenter,
} = require("../utils/redis");

const { getService } = require("../utils");
const dayjs = require("dayjs");
const _ = require("lodash");
const utils = require("@strapi/utils");
const { ForbiddenError, ApplicationError } = utils.errors;
const { getErrorMessage } = require("../../../../utils");
const semver = require("semver");
const { i } = require("mathjs");

module.exports = ({ strapi }) => ({
  async find(ctx) {
    const version = ctx.state.version;

    if (semver.gte(version, "1.3.2")) {
      // Function to select a random item from a list of items with weights
      function weightedRandomSelection(items, weights) {
        let totalWeight = weights.reduce((acc, weight) => acc + weight, 0);
        let randomWeight = Math.random() * totalWeight;
        let currentWeight = 0;

        for (let i = 0; i < items.length; i++) {
          currentWeight += weights[i];
          if (currentWeight >= randomWeight) {
            return items[i];
          }
        }

        // If no item is selected due to rounding errors, return the last item
        return items[items.length - 1];
      }

      // const existingArticles = Array.isArray(ctx.query['ids']) ? ctx.query['ids'] : [];
      const authUser = ctx.state.user;
      const { boardId, ids } = ctx.query;

      const existingArticles = Array.isArray(ids)
        ? ids
        : Array.isArray(ids.split(","))
        ? ids.split(",")
        : [];

      let postParams;

      if (boardId == 0) {
        const accessBoards = await getService("board").findAccessAll(
          authUser.id,
          authUser.level
        );
        const ids = accessBoards
          .filter((item) => item.name !== "Notice")
          .map((board) => board.id);
        const end = dayjs().valueOf();
        const start = dayjs().subtract(7, "day").valueOf();
        postParams = {
          filters: {
            id: { $notIn: existingArticles },
            board: { id: { $in: ids } },
            blocked: false,
            removed: false,
            createdAt: { $between: [start, end] },
          },
          populate: {
            board: { fields: ["id", "name"] },
            media: { fields: ["url", "ext", "mime", "formats", "size"] },
            authorUser: {
              fields: [
                "id",
                "fakeName",
                "fakeRandomAvatar",
                "level",
                "status",
                "regionCode",
                "financialStatus",
                "verificationStatus",
              ],
              populate: {
                university: {
                  fields: ["id", "name", "source", "regionCode"],
                  populate: {
                    logo: {
                      fields: ["id", "url"],
                    },
                  },
                },
                fakeAvatar: {
                  select: ["id", "url"],
                },
              },
            },
          },
          sort: [{ likeCount: "desc" }],
          limit: 3,
        };
      } else {
        postParams = {
          filters: {
            id: { $notIn: existingArticles },
            board: { id: boardId },
            blocked: false,
            removed: false,
          },
          populate: {
            board: { fields: ["id", "name"] },
            media: { fields: ["url", "ext", "mime", "formats", "size"] },
            authorUser: {
              fields: [
                "id",
                "fakeName",
                "fakeRandomAvatar",
                "level",
                "status",
                "regionCode",
                "financialStatus",
                "verificationStatus",
              ],
              populate: {
                university: {
                  fields: ["id", "name", "source", "regionCode"],
                  populate: {
                    logo: {
                      fields: ["id", "url"],
                    },
                  },
                },
                fakeAvatar: {
                  select: ["id", "url"],
                },
              },
            },
          },
          sort: [{ pinned: "desc" }, { id: "desc" }],
          limit: 3,
        };
      }

      let advertisementParams = {
        filters: {
          id: { $notIn: existingArticles },
          board: { isAdvertisement: true },
        },
        populate: {
          board: { fields: ["id", "name"] },
          media: { fields: ["url", "ext", "mime", "formats", "size"] },
          authorUser: {
            fields: [
              "id",
              "fakeName",
              "fakeRandomAvatar",
              "level",
              "status",
              "regionCode",
              "financialStatus",
              "verificationStatus",
              "status",
            ],
            populate: {
              university: {
                fields: ["id", "name", "source", "regionCode"],
                populate: {
                  logo: {
                    fields: ["id", "url"],
                  },
                },
              },
              fakeAvatar: {
                select: ["id", "url"],
              },
            },
          },
        },
      };

      // Get Posts and Comments
      let posts = await getService("post").fetchAll(postParams);
      posts = await getService("comment").getFirstComments(posts);

      // Get Random Advertisements
      const advertisements = await getService("post").fetchAll(
        advertisementParams
      );
      const weights = advertisements.map((ad) => ad.weight);
      const randomAdvertisement = weightedRandomSelection(
        advertisements,
        weights
      );

      return await getService("post").sanitizePosts(
        [...posts, ...(randomAdvertisement ? [randomAdvertisement] : [])],
        authUser
      );
    } else {
      const authUser = ctx.state.user;
      const { boardId, offset, limit, lastId } = ctx.query;
      let params;
      if (boardId == 0) {
        const accessBoards = await getService("board").findAccessAll(
          authUser.id,
          authUser.level
        );
        const ids = accessBoards
          .filter((item) => item.name !== "Notice")
          .map((board) => board.id);
        const end = dayjs().valueOf();
        const start = dayjs().subtract(7, "day").valueOf();
        params = {
          filters: {
            board: { id: { $in: ids } },
            blocked: false,
            removed: false,
            createdAt: { $between: [start, end] },
          },
          populate: {
            board: { fields: ["id", "name"] },
            media: { fields: ["url", "ext", "mime", "formats", "size"] },
            authorUser: {
              fields: [
                "id",
                "fakeName",
                "fakeRandomAvatar",
                "level",
                "status",
                "regionCode",
                "financialStatus",
                "verificationStatus",
                "status",
              ],
              populate: {
                university: {
                  fields: ["id", "name", "source", "regionCode"],
                  populate: {
                    logo: {
                      fields: ["id", "url"],
                    },
                  },
                },
                fakeAvatar: {
                  select: ["id", "url"],
                },
              },
            },
          },
          sort: [{ likeCount: "desc" }],
          start: offset,
          limit: limit,
        };
      } else {
        params = {
          filters: {
            board: { id: boardId },
            blocked: false,
            removed: false,
          },
          populate: {
            board: { fields: ["id", "name"] },
            media: { fields: ["url", "ext", "mime", "formats", "size"] },
            authorUser: {
              fields: [
                "id",
                "fakeName",
                "fakeRandomAvatar",
                "level",
                "status",
                "regionCode",
                "financialStatus",
                "verificationStatus",
                "status",
              ],
              populate: {
                university: {
                  fields: ["id", "name", "source", "regionCode"],
                  populate: {
                    logo: {
                      fields: ["id", "url"],
                    },
                  },
                },
                fakeAvatar: {
                  select: ["id", "url"],
                },
              },
            },
          },
          sort: [{ pinned: "desc" }, { id: "desc" }],
          start: offset,
          limit: limit,
        };
      }
      let posts = await getService("post").fetchAll(params);
      posts = await getService("comment").getFirstComments(posts);

      return await getService("post").sanitizePosts(posts, authUser);
    }
  },

  //post detail
  async findOne(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    const { id } = ctx.params;
    const { commentId } = ctx.request.query;

    const params = {
      populate: {
        board: { fields: ["id", "name"] },
        media: { fields: ["url", "ext", "mime", "formats", "size"] },
        authorUser: {
          fields: [
            "id",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "status",
            "regionCode",
            "financialStatus",
            "verificationStatus",
          ],
          populate: {
            university: {
              fields: ["id", "name", "source", "regionCode"],
              populate: {
                logo: {
                  fields: ["id", "url"],
                },
              },
            },
            fakeAvatar: {
              select: ["id", "url"],
            },
          },
        },
      },
    };
    const post = await getService("post").fetch(id, params);
    if (!post) return post;

    if (post.removed || post.blocked)
      throw new ApplicationError(getErrorMessage("post.noaccess", locale));

    //common data(like state, like count, comment count)
    post.data = await getService("post").getExtraData(id, authUser);
    //comment list
    const comments = [];

    if (commentId) {
      const specialComment = await getService("comment").fetch(commentId, {
        populate: {
          authorUser: {
            fields: [
              "id",
              "fakeName",
              "fakeRandomAvatar",
              "level",
              "status",
              "regionCode",
              "financialStatus",
              "verificationStatus",
            ],
            populate: {
              university: {
                fields: ["id", "name", "source", "regionCode"],
                populate: {
                  logo: {
                    fields: ["id", "url"],
                  },
                },
              },
              fakeAvatar: {
                select: ["id", "url"],
              },
            },
          },
        },
      });
      comments.push(specialComment);
    }

    const fComments = await getService("comment").fetchAll({
      filters: {
        id: { $ne: commentId || 0 },
        post: { id: id },
        blocked: false,
        removed: false,
      },
      populate: {
        authorUser: {
          fields: [
            "id",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "status",
            "regionCode",
            "financialStatus",
            "verificationStatus",
          ],
          populate: {
            university: {
              fields: ["id", "name", "source", "regionCode"],
              populate: {
                logo: {
                  fields: ["id", "url"],
                },
              },
            },
            fakeAvatar: {
              select: ["id", "url"],
            },
          },
        },
      },
      sort: [{ id: "desc" }],
      start: 0,
      limit: 10,
    });
    if (fComments && fComments.length > 0) comments.push(...fComments);

    post.comments = await getService("comment").sanitizeComments(
      comments,
      authUser
    );

    return post;
  },

  async post(ctx) {
    const authUser = ctx.state.user;
    const { boardId } = ctx.request.body;
    const locale = ctx.state.locale;
    //check post permission

    const boards = await getService("board").findAll(
      authUser.id,
      "post",
      authUser.level
    );
    if (!boards.find((board) => board.id == boardId)) {
      throw new ForbiddenError(getErrorMessage("forbidden", locale));
    }

    const values = {
      ...ctx.request.body,
      board: boardId,
      authorUser: authUser.id,
      publishedAt: new Date(),
      publicationState: "live",
    };
    const post = await getService("post").add(values);
    await incrUserPostCount(authUser.id);
    await addPostCommenter(post.id, authUser.id);

    post.comments = [];
    post.data = {
      liked: false,
      likeCount: 0,
      disliked: false,
      dislikeCount: 0,
      commentCount: 0,
    };
    return post;
  },

  async likeToggle(ctx) {
    const authUser = ctx.state.user;
    const { id } = ctx.params;

    //get post author
    const authorRes = await strapi.db.connection
      .select("user_id")
      .from("comm_posts_author_user_links")
      .where("post_id", id);
    let postAuthorId;
    if (authorRes) {
      postAuthorId = authorRes[0]?.user_id;
    }

    const isDisliked = await hasPostDisliked(id, authUser.id);

    const record = await strapi.db.connection
      .select("id")
      .from("comm_posts_likers_links")
      .where("user_id", authUser.id)
      .andWhere("post_id", id);
    let likeCount = 0;
    if (record.length == 0) {
      await strapi.db.connection
        .insert({ post_id: id, user_id: authUser.id, user_order: 1 })
        .into("comm_posts_likers_links");
      likeCount = await addPostLikeLog(id, authUser.id);
      //Change the number of Likes for the post author.
      if (postAuthorId) await incrUserLikeCount(postAuthorId, 1);
    } else {
      await strapi.db.connection.context.raw(
        `delete from comm_posts_likers_links where (post_id = ${id} and user_id = ${authUser.id})`
      );
      likeCount = await removePostLikeLog(id, authUser.id);
      //Change the number of Likes for the post author.
      if (postAuthorId) await incrUserLikeCount(postAuthorId, -1);
    }
    if (isDisliked === true) {
      await strapi.db.connection.context.raw(
        `delete from comm_posts_dislikers_links where (post_id = ${id} and user_id = ${authUser.id})`
      );
      await removePostDislikeLog(id, authUser.id);
    }
    getService("post").edit(id, { likeCount: likeCount });
    return await getService("post").getExtraData(id, authUser);
  },

  async dislikeToggle(ctx) {
    const authUser = ctx.state.user || { id: 2 };
    const { id } = ctx.params;

    const isLiked = await hasPostLiked(id, authUser.id);

    const record = await strapi.db.connection
      .select("id")
      .from("comm_posts_dislikers_links")
      .where("user_id", authUser.id)
      .andWhere("post_id", id);

    if (record.length == 0) {
      await strapi.db.connection
        .insert({ post_id: id, user_id: authUser.id, user_order: 1 })
        .into("comm_posts_dislikers_links");
      await addPostDislikeLog(id, authUser.id);
    } else {
      await strapi.db.connection.context.raw(
        `delete from comm_posts_dislikers_links where (post_id = ${id} and user_id = ${authUser.id})`
      );
      await removePostDislikeLog(id, authUser.id);
    }
    if (isLiked === true) {
      await strapi.db.connection.context.raw(
        `delete from comm_posts_likers_links where (post_id = ${id} and user_id = ${authUser.id})`
      );
      const likeCount = await removePostLikeLog(id, authUser.id);
      getService("post").edit(id, { likeCount: likeCount });
    }
    return await getService("post").getExtraData(id, authUser);
  },

  async remove(ctx) {
    const authUser = ctx.state.user;
    const { id } = ctx.params;
    const post = await getService("post").fetch(id, {
      populate: { authorUser: true },
    });
    if (!post || post.authorUser.id !== authUser.id)
      throw new ApplicationError('Not provided author"');

    await getService("post").edit(id, { removed: true });
    return { ok: true };
  },

  async update(ctx) {
    const { id } = ctx.params;
    const authUser = ctx.state.user;
    //check post
    const post = await getService("post").fetch(id, {
      populate: {
        authorUser: {
          fields: [
            "id",
            "fakeName",
            "regionCode",
            "financialStatus",
            "verificationStatus",
            "status",
          ],
        },
      },
    });
    if (!post || post.authorUser?.id != authUser?.id) {
      throw new ForbiddenError();
    }

    //update post
    const updateData = {
      ..._.omit(ctx.request.body, [
        "id",
        "board",
        "authorUser",
        "publishedAt",
        "publicationState",
        "likers",
        "dislikers",
        "comments",
        "removed",
        "likeCount",
      ]),
    };
    const updatedPost = await strapi.entityService.update(
      "plugin::community.post",
      id,
      {
        data: updateData,
        populate: {
          board: { fields: ["id", "name"] },
          media: { fields: ["url", "ext", "mime", "formats", "size"] },
          authorUser: {
            fields: [
              "id",
              "fakeName",
              "regionCode",
              "financialStatus",
              "verificationStatus",
              "status",
            ],
          },
        },
      }
    );
    updatedPost.data = await getService("post").getExtraData(id, authUser);
    return updatedPost;
  },

  async findLikes(ctx) {
    const { id } = ctx.params;
    const { offset = 0, limit = 20 } = ctx.query;

    const record = await strapi.db.connection
      .select("user_id")
      .from("comm_posts_likers_links")
      .where("post_id", id)
      .limit(limit)
      .offset(offset);
    const userIds = record.map((el) => el.user_id);
    if (userIds.length == 0) return [];

    return await strapi.query("plugin::users-permissions.user").findMany({
      select: [
        "id",
        "username",
        "fakeName",
        "fakeRandomAvatar",
        "level",
        "status",
        "regionCode",
        "financialStatus",
        "verificationStatus",
      ],
      where: { id: { $in: userIds } },
      populate: {
        university: {
          fields: ["id", "name", "source", "regionCode"],
          populate: {
            logo: {
              select: ["id", "url"],
            },
          },
        },
        fakeAvatar: {
          select: ["id", "url"],
        },
      },
    });
  },

  async findSuggestions(ctx) {
    const { id } = ctx.params;
    const { name, offset = 0, limit = 20 } = ctx.query;

    let scope = [];
    if (!name || name.trim().length < 4) {
      scope = await getPostCommenter(id);
    }
    return await strapi
      .plugin("meedata")
      .service("users")
      .findSuggestions(name, scope, offset, limit);
  },
});
