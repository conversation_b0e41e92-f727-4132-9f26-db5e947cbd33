'use strict';

const { getService } = require('../utils');
const _ = require('lodash');
const utils = require('@strapi/utils');
const { ForbiddenError } = utils.errors;
const { getUserPostAndLikeCount } = require('../utils/redis');

module.exports = ({ strapi }) => ({
    async getUserInfo(ctx) {
        const { id } = ctx.params;
        const authUser = ctx.state.user;

        const userService = getService('user');
        const user = await userService.getLightUser(id);
        if (!user) return null;

        const [posts, commonData] = await Promise.all([
            userService.getUserPosts(id, 0, 20, authUser),
            getUserPostAndLikeCount(user.id)
        ]);
        const updatedPosts = posts.map(post => ({ ...post, authorUser: user }));
        return {
            ...user,
            data: commonData,
            posts: updatedPosts
        };
    },

    async getUserPosts(ctx) {
        const { id } = ctx.params;
        const { offset = 0, limit = 20 } = ctx.request.query;
        const authUser = ctx.state.user;

        const userService = getService('user');
        const user = await userService.getLightUser(id);
        if (!user) return [];

        const posts = await userService.getUserPosts(id, offset, limit, authUser);
        return posts.map(post => ({ ...post, authorUser: user }));
    },

    async getUserComments(ctx) {
        const { id } = ctx.params;
        const { offset = 0, limit = 20 } = ctx.request.query;
        const authUser = ctx.state.user;

        const userService = getService('user');
        const user = await userService.getLightUser(id);
        if (!user) return [];

        const comments = await userService.getUserComments(id, offset, limit, authUser);
        return comments.map(comment => ({ ...comment, authorUser: user }));
    },
});