'use strict';

const { getService } = require("../utils");
const utils = require('@strapi/utils');
const { ApplicationError } = utils.errors;
const { getErrorMessage } = require('../../../../utils')

module.exports = {
    async post(ctx) {
        const authUser = ctx.state.user;
        const locale = ctx.state.locale;
        const { id, reportType, reasons, content, attachments } = ctx.request.body;
        if (!id || !reportType || !reasons) {
            throw new ApplicationError(getErrorMessage('error.validated', locale));
        }
        const params = {
            reporter: authUser.id,
            reportType,
            related: reportType === 'POST' ? `plugin::community.post:${id}` : `plugin::community.comment:${id}`,
            content,
            reasons,
            attachments
        }
        await getService('report').add(params)
        return {
            ok: true
        }
    },
}