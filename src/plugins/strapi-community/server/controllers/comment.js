"use strict";

const utils = require("@strapi/utils");
const { ApplicationError } = utils.errors;
const {
  NOTIFICATION_TYPE,
} = require("../../../strapi-notification/server/constants");

const { getService } = require("../utils");
const { getErrorMessage } = require("../../../../utils");
const {
  addCommentLikeLog,
  removeCommentLikeLog,
  addCommentDislikeLog,
  removeCommentDislikeLog,
  hasCommentDisliked,
  incPostCommentCount,
  hasCommentLiked,
  incrCommentReminderLogs,
  getCommentReminderLog,
  incrUserCommentCount,
  addPostCommenter,
} = require("../utils/redis");

module.exports = {
  async find(ctx) {
    const authUser = ctx.state.user;
    const { postId, offset, limit } = ctx.query;
    const params = {
      filters: {
        post: { id: postId },
        blocked: false,
        removed: false,
      },
      populate: {
        authorUser: {
          fields: [
            "id",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "status",
            "regionCode",
            "financialStatus",
            "verificationStatus",
            "status",
          ],
          populate: {
            university: {
              fields: ["id", "name", "source", "regionCode"],
              populate: {
                logo: {
                  fields: ["id", "url"],
                },
              },
            },
            fakeAvatar: {
              select: ["id", "url"],
            },
          },
        },
      },
      sort: [{ id: "desc" }],
      start: offset,
      limit: limit,
    };
    const results = await getService("comment").fetchAll(params);
    return await getService("comment").sanitizeComments(results, authUser);
  },

  async post(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    const { content, postId, mentions } = ctx.request.body;
    if (content.length > 10000) {
      throw new ApplicationError(getErrorMessage("comment.limit", locale));
    }
    const post = await getService("post").fetch(postId, {
      fields: ["id"],
      populate: {
        authorUser: { fields: ["id"] },
        board: { fields: ["id"] },
      },
    });
    if (!post) {
      throw new ApplicationError();
    }
    const comment = await getService("comment").add({
      content,
      post: postId,
      authorUser: authUser.id,
      mentions,
    });
    const commentCount = await incPostCommentCount(postId);
    await addPostCommenter(postId, authUser.id);
    await incrUserCommentCount(authUser.id);
    comment.data = {
      liked: false,
      likeCount: 0,
      disliked: false,
      dislikeCount: 0,
    };
    //Notification when comments is written for my post. From 10th comment, send every 10th. From 100th comment, send every 100th. 1,2,3,...,9,10,20,30,...90,100,200,300,....900,1000,2000,3000,...
    // if (post.authorUser.id != authUser.id) {
    //     const push = commentCount < 10
    //         || (commentCount < 100 && commentCount % 10 === 0)
    //         || (commentCount < 1000 && commentCount % 100 === 0)
    //         || (commentCount >= 1000 && commentCount % 1000 === 0);
    //     if (push) {
    //         await strapi.plugin('mq').service('publish').sendNotificationMessage({
    //             type: NOTIFICATION_TYPE.COMMENT_REMINDER,
    //             user: { id: post.authorUser.id },
    //             commentor: { id: authUser.id, username: authUser.fakeName }
    //         });
    //     }
    // }
    //notice post author
    //0206 just send a push message(only 1 time per user's post)
    if (post.authorUser.id != authUser.id) {
      const maxNumber = await strapi
        .service("api::setting.setting")
        .getCommentReminderRule();
      const userdCount = await getCommentReminderLog(post.authorUser.id);
      if (userdCount < maxNumber) {
        await strapi
          .plugin("mq")
          .service("publish")
          .sendNotificationMessage({
            type: NOTIFICATION_TYPE.COMMENT_REMINDER,
            user: { id: post.authorUser.id },
            commentor: { id: authUser.id, username: authUser.fakeName },
            board: post.board,
            postId: post.id,
            commentId: comment.id,
          });
        incrCommentReminderLogs(post.authorUser.id);
      }
    }
    //send mention push message
    try {
      if (mentions && Array.isArray(mentions) && mentions.length > 0) {
        Promise.all(
          mentions.map(async (mention) => {
            if (mention.id) {
              await strapi
                .plugin("mq")
                .service("publish")
                .sendNotificationMessage({
                  type: NOTIFICATION_TYPE.COMMENT_MENTION,
                  commentor: { id: authUser.id, username: authUser.fakeName },
                  board: post.board,
                  postId: post.id,
                  commentId: comment.id,
                  user: mention,
                });
            }
          })
        );
      }
    } catch (error) {}

    return comment;
  },

  async likeToggle(ctx) {
    const authUser = ctx.state.user;
    const { id } = ctx.params;

    const isDisliked = await hasCommentDisliked(id, authUser.id);

    const record = await strapi.db.connection
      .select("id")
      .from("comm_comments_likers_links")
      .where("user_id", authUser.id)
      .andWhere("comment_id", id);
    if (record.length == 0) {
      await strapi.db.connection
        .insert({ comment_id: id, user_id: authUser.id, user_order: 1 })
        .into("comm_comments_likers_links");
      await addCommentLikeLog(id, authUser.id);
    } else {
      await strapi.db.connection.context.raw(
        `delete from comm_comments_likers_links where (comment_id = ${id} and user_id = ${authUser.id})`
      );
      await removeCommentLikeLog(id, authUser.id);
    }
    if (isDisliked === true) {
      await strapi.db.connection.context.raw(
        `delete from comm_comments_dislikers_links where (comment_id = ${id} and user_id = ${authUser.id})`
      );
      await removeCommentDislikeLog(id, authUser.id);
    }

    return await getService("comment").getExtraData(id, authUser);
  },

  async dislikeToggle(ctx) {
    const authUser = ctx.state.user;
    const { id } = ctx.params;

    const isLiked = await hasCommentLiked(id, authUser.id);

    const record = await strapi.db.connection
      .select("id")
      .from("comm_comments_dislikers_links")
      .where("user_id", authUser.id)
      .andWhere("comment_id", id);

    if (record.length == 0) {
      await strapi.db.connection
        .insert({ comment_id: id, user_id: authUser.id, user_order: 1 })
        .into("comm_comments_dislikers_links");
      await addCommentDislikeLog(id, authUser.id);
    } else {
      await strapi.db.connection.context.raw(
        `delete from comm_comments_dislikers_links where (comment_id = ${id} and user_id = ${authUser.id})`
      );
      await removeCommentDislikeLog(id, authUser.id);
    }
    if (isLiked === true) {
      await strapi.db.connection.context.raw(
        `delete from comm_comments_likers_links where (comment_id = ${id} and user_id = ${authUser.id})`
      );
      await removeCommentLikeLog(id, authUser.id);
    }
    return await getService("comment").getExtraData(id, authUser);
  },

  async remove(ctx) {
    const authUser = ctx.state.user;
    const { id } = ctx.params;
    const comment = await getService("comment").fetch(id, {
      populate: {
        authorUser: true,
        post: {
          fields: ["id"],
        },
      },
    });
    if (!comment || comment.authorUser.id !== authUser.id)
      throw new ApplicationError('Not provided author"');

    await getService("comment").edit(id, { removed: true });
    //subtract 1
    if (comment.post && !comment.removed) {
      await incPostCommentCount(comment.post.id, -1);
    }
    return { ok: true };
  },
};
