'use strict';

module.exports = [
    {
        method: 'POST',
        path: '/posts',
        handler: 'post.post'
    },
    {
        method: 'GET',
        path: '/posts',
        handler: 'post.find'
    },
    {
        method: 'GET',
        path: '/posts/:id',
        handler: 'post.findOne'
    },
    {
        method: 'POST',
        path: '/posts/:id/like',
        handler: 'post.likeToggle'
    },
    {
        method: 'GET',
        path: '/posts/:id/likes',
        handler: 'post.findLikes'
    },
    {
        method: 'POST',
        path: '/posts/:id/dislike',
        handler: 'post.dislikeToggle'
    },
    {
        method: "DELETE",
        path: "/posts/:id",
        handler: "post.remove"
    },
    {
        method: 'PUT',
        path: '/posts/:id',
        handler: 'post.update'
    },
    {
        method: 'GET',
        path: '/posts/:id/suggestions',
        handler: 'post.findSuggestions'
    },
]