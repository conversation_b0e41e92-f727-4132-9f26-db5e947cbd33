'use strict';

module.exports = [
    {
        method: 'POST',
        path: '/comments',
        handler: 'comment.post'
    },
    {
        method: 'GET',
        path: '/comments',
        handler: 'comment.find'
    },
    {
        method: 'POST',
        path: '/comments/:id/like',
        handler: 'comment.likeToggle'
    },
    {
        method: 'POST',
        path: '/comments/:id/dislike',
        handler: 'comment.dislikeToggle'
    },
    {
        method: "DELETE",
        path: "/comments/:id",
        handler: "comment.remove"
    },
]