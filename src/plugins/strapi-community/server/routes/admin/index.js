'use strict'

module.exports = {
    type: 'admin',
    routes: [
        {
            method: 'GET',
            path: '/active-boards',
            handler: 'admin.getActiveBoards'
        },
        {
            method: 'POST',
            path: '/posts',
            handler: 'admin.createPost'
        },
        {
            method: 'GET',
            path: '/posts',
            handler: 'admin.findPosts'
        },
        {
            method: 'GET',
            path: '/posts/:id',
            handler: 'admin.postDetail'
        },
        {
            method: 'PUT',
            path: '/posts/:id',
            handler: 'admin.updatePost'
        },
        {
            method: 'GET',
            path: '/comments',
            handler: 'admin.findComments'
        },
        {
            method: 'PUT',
            path: '/comments/:id',
            handler: 'admin.updateComment'
        }
    ]
}