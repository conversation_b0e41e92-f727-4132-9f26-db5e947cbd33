'use strict';
const dayjs = require('dayjs');

const addPostLikeLog = async (postId, userId) => {
    await strapi.redis.connections.default.client.sadd(`COMMUNITY_POST:${postId}:LIKEER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_POST:${postId}`, 'LIKE_COUNT', 1);
}

const removePostLikeLog = async (postId, userId) => {
    await strapi.redis.connections.default.client.srem(`COMMUNITY_POST:${postId}:LIKEER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_POST:${postId}`, 'LIKE_COUNT', -1);
}

const addPostDislikeLog = async (postId, userId) => {
    await strapi.redis.connections.default.client.sadd(`COMMUNITY_POST:${postId}:DISLIKER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_POST:${postId}`, 'DISLIKE_COUNT', 1);
}

const removePostDislikeLog = async (postId, userId) => {
    await strapi.redis.connections.default.client.srem(`COMMUNITY_POST:${postId}:DISLIKER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_POST:${postId}`, 'DISLIKE_COUNT', -1);
}

const hasPostLiked = async (postId, userId) => {
    const res = await strapi.redis.connections.default.client.sismember(`COMMUNITY_POST:${postId}:LIKEER`, userId);
    return res === 1;
}

const hasPostDisliked = async (postId, userId) => {
    const res = await strapi.redis.connections.default.client.sismember(`COMMUNITY_POST:${postId}:DISLIKER`, userId);
    return res === 1;
}

const getPostLikeCount = async (postId) => {
    const res = await strapi.redis.connections.default.client.hget(`COMMUNITY_POST:${postId}`, 'LIKE_COUNT');
    return Number(res || 0)
}

const getPostDislikeCount = async (postId) => {
    const res = await strapi.redis.connections.default.client.hget(`COMMUNITY_POST:${postId}`, 'DISLIKE_COUNT');
    return Number(res || 0)
}

const getPostLikerAndCommentsCount = async (postId) => {
    const res = await strapi.redis.connections.default.client.hmget(`COMMUNITY_POST:${postId}`, ['LIKE_COUNT', 'COMMENT_COUNT']);
    return res.reduce((acc, value) => {
        if (value) {
            return acc + Number(value);
        }
        return acc;
    }, 0);
}

const incPostCommentCount = async (postId, number = 1) => {
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_POST:${postId}`, 'COMMENT_COUNT', number);
}

const getPostCommentCount = async (postId) => {
    const res = await strapi.redis.connections.default.client.hget(`COMMUNITY_POST:${postId}`, 'COMMENT_COUNT');
    return Number(res || 0)
}

const addCommentLikeLog = async (commentId, userId) => {
    await strapi.redis.connections.default.client.sadd(`COMMUNITY_COMMENT:${commentId}:LIKEER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_COMMENT:${commentId}`, 'LIKE_COUNT', 1);
}

const removeCommentLikeLog = async (commentId, userId) => {
    await strapi.redis.connections.default.client.srem(`COMMUNITY_COMMENT:${commentId}:LIKEER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_COMMENT:${commentId}`, 'LIKE_COUNT', -1);
}

const addCommentDislikeLog = async (commentId, userId) => {
    await strapi.redis.connections.default.client.sadd(`COMMUNITY_COMMENT:${commentId}:DISLIKER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_COMMENT:${commentId}`, 'DISLIKE_COUNT', 1);
}

const removeCommentDislikeLog = async (commentId, userId) => {
    await strapi.redis.connections.default.client.srem(`COMMUNITY_COMMENT:${commentId}:DISLIKER`, userId);
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_COMMENT:${commentId}`, 'DISLIKE_COUNT', -1);
}

const hasCommentLiked = async (commentId, userId) => {
    const res = await strapi.redis.connections.default.client.sismember(`COMMUNITY_COMMENT:${commentId}:LIKEER`, userId);
    return res === 1;
}

const hasCommentDisliked = async (commentId, userId) => {
    const res = await strapi.redis.connections.default.client.sismember(`COMMUNITY_COMMENT:${commentId}:DISLIKER`, userId);
    return res === 1;
}

const getCommentLikeCount = async (commentId) => {
    const res = await strapi.redis.connections.default.client.hget(`COMMUNITY_COMMENT:${commentId}`, 'LIKE_COUNT');
    return Number(res || 0)
}

const getCommentDislikeCount = async (commentId) => {
    const res = await strapi.redis.connections.default.client.hget(`COMMUNITY_COMMENT:${commentId}`, 'DISLIKE_COUNT');
    return Number(res || 0)
}

const incrCommentReminderLogs = async (userId) => {
    const today = dayjs().format("YYYYMMDD");
    await strapi.redis.connections.default.client.hincrby(`COMMUNITY_COMMENT_REMINDER:${today}`, userId, 1);
}

const getCommentReminderLog = async (userId) => {
    const today = dayjs().format("YYYYMMDD");
    const res = await strapi.redis.connections.default.client.hget(`COMMUNITY_COMMENT_REMINDER:${today}`, userId);
    return Number(res || 0)
}

const setUserPostCount = async (userId, count) => {
    return await strapi.redis.connections.default.client.hset(`COMMUNITY_USER:${userId}`, 'POST_COUNT', count);
}

const incrUserPostCount = async (userId) => {
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_USER:${userId}`, 'POST_COUNT', 1);
}

const setUserCommentCount = async (userId, count) => {
    return await strapi.redis.connections.default.client.hset(`COMMUNITY_USER:${userId}`, 'COMMENT_COUNT', count);
}

const incrUserCommentCount = async (userId) => {
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_USER:${userId}`, 'COMMENT_COUNT', 1);
}

const setUserLikeCount = async (userId, count) => {
    return await strapi.redis.connections.default.client.hset(`COMMUNITY_USER:${userId}`, 'LIKE_COUNT', count);
}

const incrUserLikeCount = async (userId, number = 1) => {
    return await strapi.redis.connections.default.client.hincrby(`COMMUNITY_USER:${userId}`, 'LIKE_COUNT', number);
}

const getUserPostAndLikeCount = async (userId) => {
    const fields = ["POST_COUNT", "LIKE_COUNT"];
    const res = await strapi.redis.connections.default.client.hmget(`COMMUNITY_USER:${userId}`, fields);
    return {
        postCount: Number(res[0] || 0),
        likeCount: Number(res[1] || 0)
    }
}

const addPostCommenter = async (postId, userId) => {
    await strapi.redis.connections.default.client.sadd(`COMMUNITY_POST:${postId}:COMMENTER`, userId);
}

const getPostCommenter = async (postId) => {
    const res = await strapi.redis.connections.default.client.smembers(`COMMUNITY_POST:${postId}:COMMENTER`) || [];
    return res.map(Number)
}

module.exports = {
    addPostLikeLog,
    removePostLikeLog,
    addPostDislikeLog,
    removePostDislikeLog,
    hasPostLiked,
    hasPostDisliked,
    getPostLikeCount,
    getPostDislikeCount,
    addCommentLikeLog,
    removeCommentLikeLog,
    addCommentDislikeLog,
    removeCommentDislikeLog,
    hasCommentLiked,
    hasCommentDisliked,
    getCommentLikeCount,
    getCommentDislikeCount,
    incPostCommentCount,
    getPostCommentCount,
    getPostLikerAndCommentsCount,
    incrCommentReminderLogs,
    getCommentReminderLog,
    setUserPostCount,
    incrUserPostCount,
    setUserCommentCount,
    incrUserCommentCount,
    setUserLikeCount,
    incrUserLikeCount,
    getUserPostAndLikeCount,
    addPostCommenter,
    getPostCommenter
}