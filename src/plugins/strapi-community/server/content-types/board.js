'use strict';

module.exports = {
    collectionName: "comm_boards",
    info: {
        singularName: "board",
        pluralName: "boards",
        displayName: "CommunityBoard",
        description: ""
    },
    options: {
        draftAndPublish: false
    },
    pluginOptions: {
        "content-manager": {
            visible: true,
        },
        "content-type-builder": {
            visible: true,
        },
    },
    attributes: {
        name: {
            type: "string"
        },
        locales: {
            type: "json"
        },
        description: {
            type: "string"
        },
        related: {
            type: "string"
        },
        blocked: {
            type: "boolean",
            default: false
        },
        type: {
            type: "enumeration",
            enum: [
                "public",
                "private"
            ]
        },
        postingPermission: {
            type: "integer"
        },
        creator: {
            type: "relation",
            relation: "oneToOne",
            target: "plugin::users-permissions.user"
        },
        subscribers: {
            type: "relation",
            relation: "oneToMany",
            target: "plugin::users-permissions.user"
        },
        posts: {
            type: "relation",
            relation: "oneToMany",
            target: "plugin::community.post",
            mappedBy: "board"
        },
        order: {
            type: "integer"
        },
        isDefault: {
            type: "boolean",
            default: false
        },
    }
}
