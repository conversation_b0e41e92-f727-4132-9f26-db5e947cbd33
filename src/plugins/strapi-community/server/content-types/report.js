'use strict';

module.exports = {
    collectionName: "comm_reports",
    info: {
        singularName: "report",
        pluralName: "reports",
        displayName: "CommunityReports",
        description: "Community post or comment reports."
    },
    options: {
        draftAndPublish: false,
    },
    pluginOptions: {
        "content-manager": {
            visible: true,
        },
        "content-type-builder": {
            visible: true,
        },
    },
    attributes: {
        reporter: {
            type: "relation",
            relation: "oneToOne",
            target: "plugin::users-permissions.user"
        },
        reportType: {
            type: "enumeration",
            enum: [
                "POST",
                "COMMENT"
            ]
        },
        related: {
            type: "string",
            configurable: false,
        },
        reasons: {
            type: "json"
        },
        content: {
            type: "text",
            configurable: false,
        },
        attachments: {
            allowedTypes: [
                "images"
            ],
            type: "media",
            multiple: true
        },
        resolved: {
            type: "boolean",
            default: false,
            configurable: false,
        },
        resolvedAt: {
            type: "datetime"
        }
    }
}