'use strict';
const { TRANSACTION } = require('../../../../constants');
const { getPostLikerAndCommentsCount } = require('../utils/redis');
const { getService } = require('../utils')
const dayjs = require('dayjs');

module.exports = ({ strapi }) => ({
    async statisticsTopPosts() {
        //role
        const bonusRoles = [50, 50, 50, 50, 50, 30, 30, 30, 30, 30];

        //Get articles within 24 hours.
        const end = dayjs().valueOf();
        const start = dayjs().subtract(1, 'day').valueOf();
        const params = {
            fields: ['id'],
            filters: {
                createdAt: { $between: [start, end] },
                board: { name: { $ne: 'Notice' } }
            },
        }
        let posts = await getService('post').fetchAll(params);
        if (posts.length == 0) return;

        //counting the number of likes and comments on posts.
        const postIds = posts.map(post => post.id);
        const postWithCounts = await Promise.all(postIds.map(async postId => {
            return { postId: postId, count: await getPostLikerAndCommentsCount(postId) };
        }));
        //rank and get the top.
        postWithCounts.sort((a, b) => b.count - a.count);
        const topPosts = postWithCounts.slice(0, bonusRoles.length);

        //Get the author of the post.
        const postUserLinks = await strapi.db.connection
            .select('post_id', 'user_id')
            .from('comm_posts_author_user_links')
            .whereIn('post_id', topPosts.map(el => el.postId));

        //send bonus
        topPosts.forEach(async (item, index) => {
            const link = postUserLinks.find(el => el.post_id == item.postId);
            if (link) {
                await strapi.service("api::transaction.transaction").executeBonus(
                    link.user_id,
                    TRANSACTION.POST_BONUS,
                    bonusRoles[index],
                    `plugin::'plugin::community.post:${item.postId}`);
            }
        });
    },
});