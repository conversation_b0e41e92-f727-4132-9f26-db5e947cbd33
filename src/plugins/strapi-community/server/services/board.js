"use strict";

module.exports = ({ strapi }) => ({
  async initBoards() {
    const universities = await strapi.entityService.findMany(
      "api::university.university",
      {
        filters: {
          source: { $notIn: ["global", "custom"] },
        },
      }
    );
    const boards = await strapi.entityService.findMany(
      "plugin::community.board",
      {}
    );
    const result = universities.filter(
      (university) =>
        !boards.some(
          (board) =>
            board.related === `api::university.university:${university.id}`
        )
    );
    if (result.length > 0) {
      const insertBoards = result.map((university) => {
        return {
          name: "My University",
          locales: { en: "My University", vi: "Trường tôi" },
          description: `${university.name} Board`,
          related: `api::university.university:${university.id}`,
          blocked: 0,
          type: "private",
          postingPermission: 3,
          order: 40,
          isDefault: 0,
        };
      });
      await strapi.db.query("plugin::community.board").createMany({
        data: insertBoards,
      });
    }
    return { result: result.length };
  },
  async edit(id, params = {}) {
    return strapi.entityService.update("plugin::community.board", id, {
      data: params,
    });
  },

  fetch(id, params) {
    return strapi.entityService.findOne("plugin::community.board", id, params);
  },

  fetchAll(params) {
    return strapi.entityService.findMany("plugin::community.board", params);
  },

  async findAccessAll(userId, userLevel) {
    const joinedIds = await this.findJoined(userId);
    const params = {
      filters: {
        blocked: false,
        $or: [
          {
            type: "public",
            $or: [
              { userLevelLimited: { $null: true } },
              { userLevelLimited: { $eq: "" } },
              { userLevelLimited: { $contains: userLevel } },
            ],
          },
          { id: { $in: joinedIds } },
        ],
      },
      sort: { order: "asc" },
    };
    const boards = await this.fetchAll(params);
    const bestBoard = {
      id: 0,
      name: "Best",
      locales: {
        en: "Best",
        vi: "Best",
      },
      default: false,
    };
    // boards.splice(1, 0, bestBoard);
    return boards;
  },

  async findPostAll(userId, permIds, userLevel) {
    const joinedIds = await this.findJoined(userId);
    const params = {
      filters: {
        blocked: false,
        $or: [
          {
            type: "public",
            postingPermission: 0,
            $or: [
              { userLevelLimited: { $null: true } },
              { userLevelLimited: { $eq: "" } },
              { userLevelLimited: { $contains: userLevel } },
            ],
          },
          { id: { $in: joinedIds }, postingPermission: { $in: permIds } },
        ],
      },
      sort: { order: "asc" },
    };
    return await this.fetchAll(params);
  },

  async findAll(userId, type, userLevel) {
    let boards = [];
    if (type === "access") {
      // when access board
      boards = await this.findAccessAll(userId, userLevel);
    } else if (type === "post") {
      // when create post
      const permIds = [0, 3];
      // authUser.status === 'GRADUATE' ? permIds.push(2) : permIds.push(1);
      boards = await this.findPostAll(userId, permIds, userLevel);
    }
    return boards;
  },

  async findJoined(userId) {
    const res = await strapi.db.connection
      .select("board_id")
      .from("comm_boards_subscribers_links")
      .where("user_id", userId);
    return res.map((item) => item.board_id);
  },

  async join(userId, related) {
    if (!related) return;

    const relatedBoard = await strapi.db
      .query("plugin::community.board")
      .findOne({
        where: { related, blocked: false },
      });
    if (!relatedBoard) return;

    try {
      const joinedBoardIds = await this.findJoined(userId);
      if (joinedBoardIds && joinedBoardIds.length > 0) {
        const leaveBoards = await strapi.entityService.findMany(
          "plugin::community.board",
          {
            filters: {
              id: { $in: joinedBoardIds },
              related: { $startsWith: "api::university.university" },
            },
          }
        );
        if (leaveBoards.length > 0) {
          const leaveBoardIds = leaveBoards.map((board) => board.id);
          await strapi.db.connection.context.raw(
            `delete from comm_boards_subscribers_links where (board_id in(${leaveBoardIds}) and user_id = ${userId})`
          );
        }
      }
      await strapi.db.connection
        .insert([
          {
            board_id: relatedBoard.id,
            user_id: userId,
            user_order: 1,
          },
        ])
        .into("comm_boards_subscribers_links");
    } catch (error) {}
  },

  async leave(userId, related) {
    try {
      const joinedBoardIds = await this.findJoined(userId);
      if (!joinedBoardIds || joinedBoardIds.length === 0) return;

      const leaveBoards = await strapi.entityService.findMany(
        "plugin::community.board",
        {
          filters: {
            id: { $in: joinedBoardIds },
            related: { $startsWith: "api::university.university" },
          },
        }
      );

      if (!leaveBoards || leaveBoards.length === 0) return;

      const leaveBoardIds = leaveBoards.map((board) => board.id);
      await strapi.db.connection.context.raw(
        `delete from comm_boards_subscribers_links where (board_id in(${leaveBoardIds}) and user_id = ${userId})`
      );
    } catch (error) {}
  },

  /**
     * Merge University and Graduate boards. 
        We realized that it's hard to distinguish the users from graduated or not. 
        So just make board for each univ and gradu or undergradu can use it. 
        Display the board name as univ name.
     * @param {*} ctx 
     */
  async mergeUniversityBoards(ctx) {
    const boards = await strapi.entityService.findMany(
      "plugin::community.board",
      {
        filters: {
          related: {
            $startsWith: "api::university.university:",
          },
        },
      }
    );

    const { graduateBoardIds, universityBoardIds } = boards.reduce(
      (accumulator, item) => {
        console.log("======= ", item.postingPermission);
        if (item.postingPermission === 1) {
          accumulator.universityBoardIds.push(item.id);
        } else if (item.postingPermission === 2) {
          accumulator.graduateBoardIds.push(item.id);
        }
        return accumulator;
      },
      { graduateBoardIds: [], universityBoardIds: [] }
    );

    //blocked the boards
    if (graduateBoardIds && graduateBoardIds.length > 0) {
      await strapi.db.query("plugin::community.board").updateMany({
        where: {
          id: { $in: graduateBoardIds },
        },
        data: {
          blocked: true,
        },
      });
    }

    //update name and permission
    if (universityBoardIds && universityBoardIds.length > 0) {
      await strapi.db.query("plugin::community.board").updateMany({
        where: {
          id: { $in: universityBoardIds },
        },
        data: {
          postingPermission: 3,
          name: "My University",
          locales: {
            en: "My University",
            vi: "Trường tôi",
          },
        },
      });
    }
  },
});
