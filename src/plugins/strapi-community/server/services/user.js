"use strict";
const {
  setUserCommentCount,
  setUserPostCount,
  setUserLikeCount,
} = require("../utils/redis");
const { getService } = require("../utils");

module.exports = ({ strapi }) => ({
  async syncUserPostCount() {
    const result = await strapi.db.connection
      .select("user_id")
      .count("id as count")
      .from("comm_posts_author_user_links")
      .groupBy("user_id");
    if (result.length > 0) {
      result.forEach((el) => {
        setUserPostCount(el.user_id, Number(el.count));
      });
    }
  },

  async syncUserCommentCount() {
    const result = await strapi.db.connection
      .select("user_id")
      .count("id as count")
      .from("comm_comments_author_user_links")
      .groupBy("user_id");
    if (result.length > 0) {
      result.forEach((el) => {
        setUserCommentCount(el.user_id, Number(el.count));
      });
    }
  },

  async syncUserLikeCount() {
    const result = await strapi.db.connection
      .select("comm_posts_author_user_links.user_id")
      .count("comm_posts_likers_links.id as count")
      .from("comm_posts_likers_links")
      .leftJoin(
        "comm_posts_author_user_links",
        "comm_posts_likers_links.post_id",
        "comm_posts_author_user_links.post_id"
      )
      .groupBy("comm_posts_author_user_links.user_id");
    if (result.length > 0) {
      result.forEach((el) => {
        setUserLikeCount(el.user_id, Number(el.count));
      });
    }
  },

  async getLightUser(id) {
    const user = await strapi.query("plugin::users-permissions.user").findOne({
      select: [
        "id",
        "username",
        "fakeName",
        "fakeRandomAvatar",
        "level",
        "status",
        "regionCode",
        "financialStatus",
        "verificationStatus",
      ],
      where: { id },
      populate: {
        university: {
          fields: ["id", "name", "source", "regionCode"],
          populate: {
            logo: {
              select: ["id", "url"],
            },
          },
        },
        fakeAvatar: {
          select: ["id", "url"],
        },
      },
    });
    return user;
  },

  async getUserPosts(userId, offset, limit, authUser) {
    const params = {
      filters: {
        authorUser: { id: userId },
        removed: false,
        blocked: false,
      },
      populate: {
        board: { fields: ["id", "name"] },
        media: { fields: ["url", "ext", "mime", "formats", "size"] },
      },
      sort: [{ id: "desc" }],
      start: offset,
      limit: limit,
    };
    const posts = await getService("post").fetchAll(params);
    return await getService("post").sanitizePosts(posts, authUser);
  },

  async getUserComments(userId, offset, limit, authUser) {
    const params = {
      filters: {
        authorUser: { id: userId },
        removed: false,
        blocked: false,
      },
      populate: {
        post: {
          populate: {
            media: { fields: ["url", "ext", "mime", "formats", "size"] },
          },
        },
      },
      sort: [{ id: "desc" }],
      start: offset,
      limit: limit,
    };
    const results = await getService("comment").fetchAll(params);
    return await getService("comment").sanitizeComments(results, authUser);
  },
});
