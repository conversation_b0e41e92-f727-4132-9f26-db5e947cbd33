"use strict";

const { hasCommentLiked, getCommentLikeCount } = require("../utils/redis");

module.exports = ({ strapi }) => ({
  async add(values) {
    return strapi.entityService.create("plugin::community.comment", {
      data: values,
      populate: {
        authorUser: {
          fields: [
            "id",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "status",
            "regionCode",
            "financialStatus",
            "verificationStatus",
          ],
          populate: {
            fakeAvatar: {
              fields: ["id", "url"],
            },
          },
        },
      },
    });
  },

  fetch(id, params) {
    return strapi.entityService.findOne(
      "plugin::community.comment",
      id,
      params
    );
  },

  fetchAll(params) {
    return strapi.entityService.findMany("plugin::community.comment", params);
  },

  findPage(params) {
    return strapi.entityService.findPage("plugin::community.comment", params);
  },

  async edit(id, params = {}) {
    return strapi.entityService.update("plugin::community.comment", id, {
      data: params,
    });
  },

  async getFirstComments(posts) {
    if (!posts || posts.length == 0) return posts;
    const postIds = posts.map((post) => post.id);
    const [postCommentRelations] = await strapi.db.connection.raw(
      "select post_id, comment_id from comm_comments_post_links where id in (SELECT max(rel.id) FROM comm_comments_post_links rel where rel.post_id in (" +
        postIds.join(",") +
        ") group by rel.post_id)"
    );
    const commentIds = postCommentRelations.map((item) => item.comment_id);
    const comments = await strapi.entityService.findMany(
      "plugin::community.comment",
      {
        filters: { id: { $in: commentIds }, blocked: false, removed: false },
        populate: {
          authorUser: {
            fields: [
              "id",
              "fakeName",
              "fakeRandomAvatar",
              "level",
              "status",
              "regionCode",
              "financialStatus",
              "verificationStatus",
            ],
            populate: {
              fakeAvatar: {
                fields: ["id", "url"],
              },
            },
          },
        },
      }
    );
    return Promise.all(
      posts.map((post) => {
        const rel = postCommentRelations.find((rel) => rel.post_id == post.id);
        const comment = rel
          ? comments.find((comment) => comment.id == rel.comment_id)
          : null;
        post.comments = comment ? [comment] : [];
        return post;
      })
    );
  },

  async getExtraData(commentId, authUser) {
    return {
      liked: authUser ? await hasCommentLiked(commentId, authUser.id) : false,
      likeCount: await getCommentLikeCount(commentId),
    };
  },

  async sanitizeComments(comments, authUser) {
    if (!comments) return comments;
    return await Promise.all(
      comments.map(async (comment) => {
        comment.data = {
          liked: authUser
            ? await hasCommentLiked(comment.id, authUser.id)
            : false,
          likeCount: await getCommentLikeCount(comment.id),
        };
        return comment;
      })
    );
  },

  async getCommonData(comments) {
    if (!comments) return comments;
    return await Promise.all(
      comments.map(async (comment) => {
        comment.data = {
          likeCount: await getCommentLikeCount(comment.id),
        };
        return comment;
      })
    );
  },
});
