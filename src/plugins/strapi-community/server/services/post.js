"use strict";

const { getService } = require("../utils");
const {
  hasPostLiked,
  getPostLikeCount,
  getPostCommentCount,
} = require("../utils/redis");

module.exports = ({ strapi }) => ({
  async add(values) {
    return strapi.entityService.create("plugin::community.post", {
      data: values,
      populate: {
        media: { fields: ["url", "ext", "mime", "formats"] },
        authorUser: {
          fields: [
            "id",
            "fakeName",
            "fakeRandomAvatar",
            "level",
            "status",
            "regionCode",
            "financialStatus",
            "verificationStatus",
          ],
          populate: {
            fakeAvatar: {
              fields: ["id", "url"],
            },
          },
        },
      },
    });
  },

  fetch(id, params) {
    return strapi.entityService.findOne("plugin::community.post", id, params);
  },

  fetchAll(params) {
    return strapi.entityService.findMany("plugin::community.post", params);
  },

  findPage(params) {
    return strapi.entityService.findPage("plugin::community.post", params);
  },

  async edit(postId, params = {}) {
    return strapi.entityService.update("plugin::community.post", postId, {
      data: params,
    });
  },

  async getExtraData(postId, authUser) {
    return {
      liked: authUser ? await hasPostLiked(postId, authUser.id) : false,
      likeCount: await getPostLikeCount(postId),
      commentCount: await getPostCommentCount(postId),
    };
  },

  async sanitizePosts(posts, authUser) {
    if (!posts) return posts;
    return await Promise.all(
      posts.map(async (post) => {
        post.comments = await getService("comment").sanitizeComments(
          post.comments,
          authUser
        );
        post.data = {
          liked: authUser ? await hasPostLiked(post.id, authUser.id) : false,
          likeCount: await getPostLikeCount(post.id),
          commentCount: await getPostCommentCount(post.id),
        };
        return post;
      })
    );
  },

  async getCommonData(posts) {
    if (!posts) return posts;
    return await Promise.all(
      posts.map(async (post) => {
        post.data = {
          likeCount: await getPostLikeCount(post.id),
          commentCount: await getPostCommentCount(post.id),
        };
        return post;
      })
    );
  },
});
