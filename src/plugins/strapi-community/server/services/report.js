'use strict';

module.exports = ({ strapi }) => ({
    async add(values) {
        return strapi.entityService.create('plugin::community.report', {
            data: values
        });
    },

    fetch(id, params) {
        return strapi.entityService.findOne('plugin::community.report', id, params);
    },

    fetchAll(params) {
        return strapi.entityService.findMany('plugin::community.report', params);
    },

    async edit(id, params = {}) {
        return strapi.entityService.update('plugin::community.report', id, {
            data: params
        });
    }
});