'use strict';
const { Rating } = require('../models');
const { LOCKS } = require("../../../../constants");
const dayjs = require("dayjs");
const { getStartDate } = require('../../../../utils/date')

module.exports = ({ strapi }) => ({
    /**
     * @param {any} rater
     * @param {any} rated
     */
    async findOne(rater, rated) {
        return await Rating.findOne({ rater, rated }).lean();
    },

    async sync(data) {
        const { rater, rated } = data;
        const [isFriend, isGotOrSent, isRecommended] =
            await Promise.all([
                await strapi.plugin("meedata").service("friendships").isMatched(rater, rated),
                await strapi.plugin("meedata").service("requests").isGotOrSent(rater, rated),
                await strapi.plugin("meedata").service("recommendations").isRecommended(rater, rated),
            ]);
        console.log(isFriend, isGotOrSent, isRecommended);
        const status = !isFriend && !isGotOrSent && !isRecommended;
        const params = {
            ...data,
            status,
            expireAt: dayjs().add(7, 'day').startOf('day').toDate()
        }
        const query = { rater, rated }
        const update = {
            $set: params
        }
        const options = { upsert: true }
        await Rating.updateOne(query, update, options);
    },

    /**
     * @param {any} relatedId
     * @param {any} data
     */
    async updateByRelatedId(relatedId, data) {
        await Rating.updateOne({ relatedId }, { $set: data })
    },

    /**
     * @param {any} rater
     * @param {any} rated
     */
    async markAsInvalid(rater, rated, both) {
        const filter = both ? { $or: [{ rater, rated }, { rater: rated, rated: rater }] } : { rater, rated };
        await Rating.updateMany(filter, { $set: { status: 0 } })
    },

    /**
     * @param {any}  rated
     */
    async findRatings(rated, offset = 0, limit = 10) {
        let match = { rated, status: 1, score: 5, createdAt: { '$lt': getStartDate() } };
        let result = await Rating.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$skip': Number(offset) },
            { '$limit': Number(limit) },
            { '$lookup': { 'from': 'users', 'localField': 'rater', 'foreignField': 'id', 'as': 'rater' } },
            { '$addFields': { 'rater': { '$arrayElemAt': ['$rater', 0] } } }
        ]);
        return result.map((item) => {
            return {
                id: item.rater.id,
                username: !item.locked ? item.rater.username : '-',
                avatar: !item.locked ? item.rater.avatar : null,
                createdAt: item.createdAt,
                data: {
                    locked: item.locked,
                    lockName: LOCKS.EVALUATION,
                    lockPrice: item.lockPrice,
                    level: item.level
                }
            }
        });
    },

    /**
     * @param {any} rater
     * @param {any} rated
     */
    async findSingleRating(rater, rated) {
        let match = { rater, rated };
        let result = await Rating.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$limit': 1 },
            { '$lookup': { 'from': 'users', 'localField': 'rater', 'foreignField': 'id', 'as': 'rater' } },
            { '$addFields': { 'rater': { '$arrayElemAt': ['$rater', 0] } } }
        ]);
        if (result.length > 0) {
            const item = result[0];
            return {
                id: item.rater.id,
                username: !item.locked ? item.rater.username : '-',
                avatar: !item.locked ? item.rater.avatar : null,
                createdAt: item.createdAt,
                data: {
                    locked: item.locked,
                    lockName: LOCKS.EVALUATION,
                    lockPrice: item.lockPrice,
                    level: item.level
                }
            }
        }
        return null;
    },

    async statHighEvals() {
        const start = new Date();
        start.setDate(start.getDate() - 1);
        start.setHours(0, 0, 0, 0);

        const end = new Date();
        end.setDate(end.getDate() - 1);
        end.setHours(23, 59, 59, 999);
        const pipeline = [
            {
                $match: {
                    score: 5,
                    locked: true,
                    status: 1,
                    createdAt: {
                        $gte: start,
                        $lt: end
                    }
                }
            },
            {
                $group: {
                    _id: "$rated",
                    count: { $sum: 1 }
                }
            },
            {
                $project: {
                    _id: 0,
                    userId: "$_id",
                    count: 1
                }
            }
        ];
        return await Rating.aggregate(pipeline);
    }
});