'use strict';
const { Recommendation, User } = require('../models')
const dayjs = require("dayjs");
const utc = require('dayjs/plugin/utc')
const { LOCKS, RECOMMEND_TYPE, CARD_COLLECTION, PRICES, SETTING_KEYS } = require('../../../../constants');
const { getStartDate } = require("../../../../utils/date");
const { getRecommendQuotas, getPassedRecommendQuotas } = require('../../../../utils/redis');

module.exports = ({ strapi }) => ({

    /**
     * @param {{ recommender: any; recommended: any; }} data
     */
    async sync(data) {
        data = {
            ...data,
            expireAt: dayjs().add(7, 'day').startOf('day').toDate()
        }
        await Recommendation.updateOne({ recommender: data.recommender, recommended: data.recommended }, { $set: data }, { upsert: true })
    },

    /**
     * @param {any} relatedId
     * @param {any} data
     */
    async updateByRelatedId(relatedId, data) {
        await Recommendation.updateOne({ relatedId }, { $set: data })
    },

    /**
     * @param {any} recommender
     * @param {any} recommended
     */
    async markAsInvalid(recommender, recommended) {
        await Recommendation.updateMany({ $or: [{ recommender, recommended }, { recommender: recommended, recommended: recommender }] }, { $set: { status: 0 } })
    },

    /**
     * @param {any} recommender
     * @param {any} recommended
     */
    async isRecommended(recommender, recommended) {
        const result = await Recommendation.findOne({ recommender, recommended }).lean();
        return result != null;
    },

    /**
     * @param {any} recommended
     */
    async findRecommendedIds(recommended) {
        const result = await Recommendation.find({ recommended }, { _id: 0, recommender: 1 });
        return result.map((item) => item.recommender);
    },

    /**
     * @param {any} recommended
     * @param {string} coll
     */
    async findPremiumRecommend(recommended, coll) {
        let match = { 'recommended': recommended, type: RECOMMEND_TYPE.PREMIUM, status: 1 };
        if (coll === CARD_COLLECTION.PASSED) {
            match = {
                ...match,
                "createdAt": { '$lt': getStartDate() }
            }
        } else {
            match = {
                ...match,
                "createdAt": { '$gte': getStartDate() }
            }
        }
        let result = await Recommendation.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$lookup': { 'from': 'users', 'localField': 'recommender', 'foreignField': 'id', 'as': 'recommender' } },
            { '$addFields': { 'recommender': { '$arrayElemAt': ['$recommender', 0] } } }
        ]);
        return result.map((item) => {
            return {
                id: item.recommender.id,
                username: !item.locked ? item.recommender.username : '-',
                avatar: !item.locked ? item.recommender.avatar : null,
                createdAt: item.createdAt,
                data: {
                    locked: item.locked,
                    lockName: LOCKS.RECOMMEND,
                    lockPrice: item.lockPrice
                }
            }
        });
    },
    /**
     * @param {any} recommended
     * @param {string} coll
     * @param {number} days
     */
    async findStandardRecommend(recommended, coll, days) {
        return coll === CARD_COLLECTION.TODAY ? await this.findToadyStandardRecommend(recommended) : await this.findPassedStandardRecommend(recommended, days)
    },

    /**
     * @param {any} recommended
     */
    async findToadyStandardRecommend(recommended) {
        dayjs.extend(utc);
        const current = dayjs();
        const dayStr = current.format('YYYYMMDD');
        const day = Number(dayStr);

        let result = await Recommendation.aggregate([
            { '$match': { 'recommended': recommended, type: RECOMMEND_TYPE.STANDARD, status: 1, sequence: day } },
            { '$sort': { '_id': -1 } },
            { '$lookup': { 'from': 'users', 'localField': 'recommender', 'foreignField': 'id', 'as': 'recommender' } },
            { '$addFields': { 'recommender': { '$arrayElemAt': ['$recommender', 0] } } }
        ]);
        result = result.map((item) => {
            return {
                id: item.recommender.id,
                username: !item.locked ? item.recommender.username : '-',
                avatar: !item.locked ? item.recommender.avatar : null,
                createdAt: item.createdAt,
                data: {
                    locked: item.locked,
                    lockName: LOCKS.RECOMMEND,
                    lockPrice: item.lockPrice,
                    sequence: item.sequence
                }
            }
        });
        const dailyGivenRules = await strapi.service('api::setting.setting').getDailyGivenRules();
        const remainingQuota = await getRecommendQuotas(dayStr, recommended, dailyGivenRules.length);
        const unactivatedList = dailyGivenRules.filter(num => num > current.hour());
        const activatedCount = remainingQuota - unactivatedList.length;
        const createdAt = dayjs().utc().format();
        result.push(...Array.from({ length: activatedCount }, () => ({
            id: day,
            username: '-',
            avatar: null,
            createdAt: createdAt,
            data: {
                locked: true,
                lockPrice: 0,
                lockName: LOCKS.RECOMMEND,
                release: true,
                sequence: day
            }
        })))
        for (const hour of unactivatedList) {
            const releaseAt = dayjs().hour(hour).minute(0).second(0).utc().format();
            result.push({
                id: day,
                username: '-',
                avatar: null,
                createdAt: createdAt,
                data: {
                    locked: true,
                    lockPrice: 0,
                    lockName: LOCKS.RECOMMEND,
                    release: false,
                    sequence: day,
                    releaseAt: releaseAt,
                }
            })
        }
        return result;
    },

    /**
     * @param {any} recommended
     */
    async findPassedStandardRecommend(recommended, days = 0) {
        dayjs.extend(utc);
        const start = Number(dayjs().format('YYYYMMDD'));
        const end = Number(dayjs().subtract(6, 'day').format('YYYYMMDD'));

        let match = {
            'recommended': recommended, type: RECOMMEND_TYPE.STANDARD, status: 1, sequence: {
                $lt: start,
                $gte: end
            }
        };
        let result = await Recommendation.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$lookup': { 'from': 'users', 'localField': 'recommender', 'foreignField': 'id', 'as': 'recommender' } },
            { '$addFields': { 'recommender': { '$arrayElemAt': ['$recommender', 0] } } }
        ]);
        result = result.map((item) => {
            const createdAt = item.sequence ? dayjs(String(item.sequence)).hour(12).utc().format() : item.createdAt;
            return {
                id: item.recommender.id,
                username: !item.locked ? item.recommender.username : '',
                avatar: !item.locked ? item.recommender.avatar : null,
                createdAt: createdAt,
                data: {
                    locked: item.locked,
                    lockName: LOCKS.RECOMMEND,
                    sequence: item.sequence,
                    lockPrice: item.lockPrice
                }
            }
        });
        const dailyGivenRules = await strapi.service('api::setting.setting').getDailyGivenRules();

        const passedQuotas = await getPassedRecommendQuotas(recommended, days, dailyGivenRules.length);

        for (const quota of passedQuotas) {
            if (quota.count > 0) {
                const id = Number(quota.day);
                const releaseTime = dayjs(quota.day).hour(12).minute(0).second(0).utc().format();

                const createRecommendationData = () => ({
                    id,
                    username: '',
                    avatar: null,
                    createdAt: releaseTime,
                    data: {
                        locked: true,
                        lockName: LOCKS.RECOMMEND,
                        lockPrice: PRICES.UNLOCK_PAST_RECOMMENDATION,
                        sequence: Number(quota.day),
                        release: true,
                        releaseAt: releaseTime
                    }
                });

                const recommendationData = Array.from({ length: quota.count }, createRecommendationData);
                result.push(...recommendationData);
            }
        }
        return result.sort((a, b) => b.data.sequence - a.data.sequence);
    },
});