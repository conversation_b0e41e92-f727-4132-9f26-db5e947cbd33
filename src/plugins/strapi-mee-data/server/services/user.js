'use strict';
const { User } = require('../models');
const { LOCKS } = require('../../../../constants');
const dayjs = require('dayjs');
const { getRecommendQuotas, setBalance, getUserRecommendations, addUserRecommendations } = require('../../../../utils/redis');

module.exports = ({ strapi }) => ({
  /**
   * @param {any} id
   */
  async findOne(id) {
    return await User.findOne({ id: id }, { _id: 0, __v: 0 }).lean();
  },

  /**
   * @param {any} ids
   */
  async findMany(ids) {
    return await User.find({ id: { $in: ids } }, { _id: 0, __v: 0 }).lean();
  },

  async findOneByName(username) {
    return await User.findOne({ username }, { _id: 0, __v: 0 }).lean();
  },

  async importUsers() {
    const totalCount = await strapi.db.query("plugin::users-permissions.user").count({});
    console.log(`The number of users to be imported is : ${totalCount}`);
    if (totalCount == 0) return;
    //update
    let offset = 0;
    let updatedCount = 0;
    const batchSize = 50;
    while (updatedCount < totalCount) {
      const remainingCount = totalCount - updatedCount;
      const currentBatchSize = Math.min(batchSize, remainingCount);

      let users = await strapi.query("plugin::users-permissions.user").findMany({
        populate: {
          avatar: { select: ['id', 'name', 'url', 'ext', 'mime', 'size', 'formats'] },
          fakeAvatar: { select: ['id', 'name', 'url', 'ext', 'mime', 'size', 'formats'] },
          interests: { select: ["tag"] },
          university: { select: ["id", "name"] },
        },
        offset: offset,
        limit: batchSize
      });
      await Promise.all(users.map(async source => {
        if (source.interests) {
          const interestTags = source.interests.map((ele) => ele.tag);
          source.interests = interestTags;
        }
        if (source.latitude && source.longitude) {
          source.coordinates = [source.latitude, source.longitude];
        }
        if (!source.balance) {
          source.balance = 0;
        }
        if (!source.totalExpenses) {
          source.totalExpenses = 0;
        }
        const query = { id: source.id };
        const update = { $set: source };
        const options = { upsert: true };
        await User.updateOne(query, update, options);
        await setBalance(source.id, source.balance);
      }))
      updatedCount += currentBatchSize;
      offset += currentBatchSize;
      console.log(`User import progress: ${updatedCount}/${totalCount}`);
    }
  },
  /**
   * @param {any} id
   */
  async syncUserInfo(id) {
    let source = await strapi.query("plugin::users-permissions.user").findOne({
      where: { id: id },
      populate: {
        avatar: { select: ['id', 'name', 'url', 'ext', 'mime', 'size', 'formats'] },
        fakeAvatar: { select: ['id', 'name', 'url', 'ext', 'mime', 'size', 'formats'] },
        interests: { select: ["tag"] },
        university: { select: ["id", "name"] },
      },
    });
    if (source) {
      if (source.interests) {
        const interestTags = source.interests.map((ele) => ele.tag);
        source.interests = interestTags;
      }
      if (source.latitude && source.longitude) {
        source.coordinates = [source.latitude, source.longitude];
      }
      if (!source.balance) {
        source.balance = 0;
      }
      if (!source.totalExpenses) {
        source.totalExpenses = 0;
      }
      const query = { id: source.id };
      const update = { $set: source };
      const options = { upsert: true };
      await User.updateOne(query, update, options);
    }
  },

  /**
   * @param {Number} id
   */
  async updateLastAccessTime(id) {
    await User.findOneAndUpdate({ id }, { $set: { lastAccessAt: new Date() } });
  },

  async statUnAccessUsers(day) {
    return await User.find(
      {
        lastAccessAt: {
          $gte: dayjs().subtract(30, "day").toDate(),
          $lt: dayjs().subtract(day, "day").toDate(),
        },
      },
      { _id: 0, __v: 0 }
    ).lean();
  },

  async statUnViewGivenCards() {
    const users = await User.find(
      { lastAccessAt: { $gte: dayjs().subtract(7, "day").toDate() } },
      { _id: 0, __v: 0 }
    ).lean();
    const day = dayjs().format("YYYYMMDD");
    const dailyGivenRules = await strapi.service('api::setting.setting').getDailyGivenRules();
    const result = await Promise.all(
      users.map(async (user) => {
        const limit = await getRecommendQuotas(day, user.id, dailyGivenRules.length);
        return {
          ...user,
          limit,
        };
      })
    );
    return result.filter((item) => item.limit >= 2);
  },

  /**
   * @param {any} recommended
   * @param {any} limit
   * @param {any} premium
   */
  async recommendUsers(premium, recommended, limit) {
    const [currentUser, mathchedIds, recommendedIds, requestedIds] =
      await Promise.all([
        this.findOne(recommended),
        strapi
          .plugin("meedata")
          .service("friendships")
          .findMathchedIds(recommended),
        // await strapi
        //   .plugin("meedata")
        //   .service("recommendations")
        //   .findRecommendedIds(recommended),
        getUserRecommendations(recommended),
        strapi
          .plugin("meedata")
          .service("requests")
          .findRequestedIds(recommended),
      ]);
    if (!currentUser || !currentUser.gender) {
      return [];
    }
    const ids = [
      currentUser.id,
      ...mathchedIds,
      ...recommendedIds,
      ...requestedIds,
    ];
    let query = {
      id: { $nin: ids },
      verificationStatus: "verified",
      visibility: true,
      deleted: false,
      blocked: false,
      gender: { $ne: currentUser.gender },
      lastAccessAt: {
        $gte: dayjs().subtract(14, "day").toDate()
      }
    };
    let source = [];
    if (premium) {
      query = {
        ...query,
        level: { $in: [4, 5] }
      }
      source = await User.aggregate([
        { $match: query },
        { $sort: { avgRating: -1 } },
        { $limit: limit },
      ]);
      if (source && source.length != limit) {
        delete query.lastAccessAt;
        source = await User.aggregate([
          { $match: query },
          { $sort: { avgRating: -1 } },
          { $limit: limit },
        ]);
      }
    } else {
      const isNew = dayjs(currentUser.createdAt).isAfter(dayjs().subtract(3, 'day'));
      const levelArr = isNew ? [4, 5] : [2, 3];
      query = {
        ...query,
        level: { $in: levelArr }
      }
      source = await User.aggregate([
        { $match: query },
        { $sample: { size: limit } },
      ]);
      if (source && source.length != limit) {
        delete query.lastAccessAt;
        source = await User.aggregate([
          { $match: query },
          { $sample: { size: limit } },
        ]);
      }
    }
    const score = dayjs().valueOf();
    if (source && source.length === limit) {
      const params = [].concat(...source.map(el => [score, el.id]));
      await addUserRecommendations(recommended, params);
    }
    return source.map((item) => {
      return {
        id: item.id,
        username: item.username,
        avatar: item.avatar,
        createdAt: item.createdAt,
        data: {
          locked: false,
          lockName: LOCKS.RECOMMEND,
          lockPrice: 0,
        },
      };
    });
  },

  async pageAll(query, page, pageSize) {
    const total = await User.countDocuments(query);
    const pageCount = Math.ceil(total / pageSize);
    const results = await User.find(query, { _id: 0, __v: 0 }).sort({ id: -1 }).skip((page - 1) * pageSize)
      .limit(pageSize).lean();
    return {
      results,
      pagination: {
        page,
        pageCount,
        pageSize,
        total
      }
    }
  },

  async findAll(query) {
    return await User.find(query, { _id: 0, __v: 0 }).sort({ id: -1 }).lean();
  },

  async findSuggestions(name, scope, offset, limit) {
    let filter = {};
    if (name) {
      filter.fakeName = { $regex: name, $options: 'i' };
    } else {
      filter.fakeName = { $ne: null };
    }
    if (scope && scope.length > 0) {
      filter.id = { $in: scope };
    }
    const sort = { fakeName: 1 };
    return await User.find(filter, { fakeName: 1, id: 1, _id: 0 })
      .sort(sort)
      .skip(Number(offset))
      .limit(Number(limit))
      .lean();
  },

  async findRecentAccessUsers(lastAccessAt) {
    const query = {
      deleted: false,
      blocked: false,
      lastAccessAt: {
        $gte: lastAccessAt
      }
    };
    return await User.find(query, { _id: 0, id: 1 }).lean();
  }

});
