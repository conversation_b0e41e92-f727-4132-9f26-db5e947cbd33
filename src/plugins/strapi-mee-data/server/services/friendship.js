'use strict';
const { Friendship } = require('../models')
const dayjs = require("dayjs");

module.exports = ({ strapi }) => ({
    /**
     * @param {{ user: any; friend: any; }} data
     */
    async sync(data) {
        const query = { user: data.user, friend: data.friend }
        const update = {
            $set: data
        }
        const options = { upsert: true }
        await Friendship.updateOne(query, update, options);
    },

    /**
     * @param {any} user
     * @param {any} friend
     */
    async connected(user, friend) {
        await Friendship.updateMany({ $or: [{ user, friend }, { user: friend, friend: user }] }, { $set: { connected: true } });
    },

    /**
     * @param {any} user
     * @param {any} friend
     */
    async remove(user, friend) {
        await Friendship.deleteMany({ $or: [{ user, friend }, { user: friend, friend: user }] });
    },

    async updateDeletedStatus(user, friend, state) {
        await Friendship.updateOne({ user, friend }, { $set: { deleted: state } });
    },

    /**
     * @param {any} user
     * @param {any} friend
     */
    async isMatched(user, friend) {
        const result = await Friendship.findOne({ user, friend }).lean();
        return result != null
    },

    /**
     * @param {any} authorId
     */
    async findMatched(authorId, offset = 0, limit = 20, status = 'unconnected') {
        if (!authorId) return [];

        let match = { user: authorId, deleted: { $ne: true } };
        if (status === "unconnected") {
            match = { ...match, connected: false }
        } else if (status === "connected") {
            match = { ...match, connected: true }
        }
        let result = await Friendship.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$skip': Number(offset) },
            { '$limit': Number(limit) },
            { '$lookup': { 'from': 'users', 'localField': 'friend', 'foreignField': 'id', 'as': 'friend' } },
            { '$addFields': { 'friend': { '$arrayElemAt': ['$friend', 0] } } }
        ]);
        return result.map((item) => {
            return {
                id: item.friend.id,
                username: item.friend.username,
                avatar: item.friend.avatar,
                data: {
                    locked: false,
                    matched: true,
                }
            }
        })
    },

    /**
     * @param {any} user
     */
    async findMathchedIds(user) {
        const result = await Friendship.find({ user }, { _id: 0, friend: 1 });
        return result.map((item) => item.friend);
    },

    async checkUnConnected() {
        let result = await Friendship.aggregate([
            {
                '$match': {
                    connected: false,
                    createdAt: { $lt: dayjs().subtract(1, 'day').endOf('day').toDate() }
                }
            },
            { '$group': { '_id': '$user' } },
        ]);
        return result.map(item => item._id);
    }
});