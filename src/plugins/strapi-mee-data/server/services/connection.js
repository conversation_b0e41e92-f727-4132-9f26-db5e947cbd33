'use strict';
const mongoose = require('mongoose');
const amqplib = require('amqplib');

module.exports = ({ strapi }) => ({
  async buildAll({ mongodb, rabbitmq }) {
    if (mongodb) {
      await this.buildMongodb(mongodb);
    }
  },
  async buildMongodb(config) {
    strapi.log.info("=========MogoDB connecting============");
    try {
      const { host, port, name, user, pass, tls } = config;
      let opts = {
        user, pass, dbName: name
      }
      if (tls) {
        opts = {
          ...opts,
          ssl: true,
          sslValidate: true,
          sslCA: tls.ca,
          retryWrites: false
        }
      }
      mongoose.set('strictQuery', true);
      mongoose.connect(`${host}:${port}`, opts);
      strapi.$mongoose = mongoose;
      strapi.log.info("=========MogoDB connected============");
    } catch (error) {
      strapi.log.error("=========MogoDB conncect Failed============", error);
    }
  },
});
