'use strict';
const { FriendshipRequest } = require('../models');
const { LOCKS, PRICES, CARD_COLLECTION, REQUEST_STATUS } = require('../../../../constants');
const { getStartDate } = require("../../../../utils/date");
const dayjs = require("dayjs");
const { findOne } = require('../../../strapi-manager/server/controllers/report');

module.exports = ({ strapi }) => ({
    /**
     * @param {any} sender
     * @param {any} receiver
     */
    async findOne(sender, receiver) {
        return await FriendshipRequest.findOne({ sender, receiver }).lean();
    },

    async sync(data) {
        data = {
            ...data,
            expireAt: dayjs().add(7, 'day').startOf('day').toDate()
        }
        await FriendshipRequest.updateOne({ sender: data.sender, receiver: data.receiver }, { $set: data }, { upsert: true })
    },

    /**
     * @param {any} relatedId
     * @param {any} data
     */
    async updateByRelatedId(relatedId, data) {
        await FriendshipRequest.updateOne({ relatedId }, { $set: data })
    },

    /**
     * If two users match successfully. Then their request data is invalid.
     * @param {any} sender
     * @param {any} receiver
     */
    async markAsInvalid(sender, receiver) {
        await FriendshipRequest.updateMany({ $or: [{ sender, receiver }, { sender: receiver, receiver: sender }] }, { $set: { status: 0 } })
    },

    /**
     * @param {any} sender
     * @param {any} receiver
     * @param {any} data
     */
    async updateOne(sender, receiver, data) {
        await FriendshipRequest.updateOne({ sender, receiver }, { $set: data });
    },

    /**
     * @param {any} sender
     */
    async findRequestedIds(sender) {
        const result = await FriendshipRequest.find({ $or: [{ sender }, { receiver: sender }] }, { _id: 0, sender: 1, receiver: 1 });
        return result.map((item) => {
            return item.sender == sender ? item.receiver : item.sender;
        });
    },

    /**
     * @param {any} receiver
     * @param {string} coll
     */
    async findGot(receiver, coll, offset = 0, limit = 10) {
        let match = { receiver, status: 1 };
        if (coll === CARD_COLLECTION.PASSED) {
            match = {
                ...match,
                "createdAt": { '$lt': getStartDate() }
            }
        } else {
            match = {
                ...match,
                "createdAt": { '$gte': getStartDate() }
            }
        }
        let result = await FriendshipRequest.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$skip': Number(offset) },
            { '$limit': Number(limit) },
            { '$lookup': { 'from': 'users', 'localField': 'sender', 'foreignField': 'id', 'as': 'sender' } },
            { '$addFields': { 'sender': { '$arrayElemAt': ['$sender', 0] } } }
        ]);
        return Promise.all(result.map(async (item) => {

            const sender = item.sender.id;

            const sent = await FriendshipRequest.findOne({ sender, receiver }).lean();
            const got = await FriendshipRequest.findOne({ sender: receiver, receiver: sender }).lean();

            return {
                id: item.sender.id,
                username: !item.locked ? item.sender.username : '-',
                avatar: !item.locked ? item.sender.avatar : null,
                createdAt: item.createdAt,
                data: {
                    rejected: item.rejected,
                    locked: item.locked,
                    lockName: LOCKS.HEART,
                    lockPrice: PRICES.UNLOCK_HEART,
                    got: true,
                    level: item.level,
                    rejected: sent?.handleResult === REQUEST_STATUS.REJECTED || got?.handleResult === REQUEST_STATUS.REJECTED,
                    rejectedReason: sent?.handleMsg || got?.handleMsg || null
                }
            }
        }));
    },

    /**
     * @param {any} sender
     * @param {any} receiver
     */
    async findSingleGot(sender, receiver) {
        let match = { sender, receiver };
        let result = await FriendshipRequest.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$limit': 1 },
            { '$lookup': { 'from': 'users', 'localField': 'sender', 'foreignField': 'id', 'as': 'sender' } },
            { '$addFields': { 'sender': { '$arrayElemAt': ['$sender', 0] } } }
        ]);
        if (result.length > 0) {
            const item = result[0];
            return {
                id: item.sender.id,
                username: !item.locked ? item.sender.username : '-',
                avatar: !item.locked ? item.sender.avatar : null,
                createdAt: item.createdAt,
                data: {
                    locked: item.locked,
                    lockName: LOCKS.HEART,
                    lockPrice: item.lockPrice,
                    got: true,
                    level: item.level
                }
            }
        }
        return null;
    },

    /**
     * @param {any} sender
     * @param {string} coll
     */
    async findSent(sender, coll, offset = 0, limit = 10) {
        let match = { sender, status: 1 };
        if (coll === CARD_COLLECTION.PASSED) {
            match = {
                ...match,
                "createdAt": { '$lt': getStartDate() }
            }
        } else {
            match = {
                ...match,
                "createdAt": { '$gte': getStartDate() }
            }
        }
        let result = await FriendshipRequest.aggregate([
            { '$match': match },
            { '$sort': { '_id': -1 } },
            { '$skip': Number(offset) },
            { '$limit': Number(limit) },
            { '$lookup': { 'from': 'users', 'localField': 'receiver', 'foreignField': 'id', 'as': 'receiver' } },
            { '$addFields': { 'receiver': { '$arrayElemAt': ['$receiver', 0] } } }
        ]);

        return Promise.all(result.map(async (item) => {
            const receiver = item.receiver.id;

            const sent = await FriendshipRequest.findOne({ sender, receiver }).lean();
            const got = await FriendshipRequest.findOne({ sender: receiver, receiver: sender }).lean();

            return {
                id: item.receiver.id,
                username: item.receiver.username,
                avatar: item.receiver.avatar,
                createdAt: item.createdAt,
                data: {
                    locked: false,
                    sent: true,
                    level: item.level,
                    rejected: sent?.handleResult === REQUEST_STATUS.REJECTED || got?.handleResult === REQUEST_STATUS.REJECTED,
                    rejectedReason: sent?.handleMsg || got?.handleMsg || null
                }
            }
        }));
    },

    /**
     * @param {any} sender
     * @param {any} receiver
     */
    async getSentAndGotData(sender, receiver) {
        if (!sender || !receiver) {
            return {
                hearted: false,
                heartedLevel: null,
                gotHeart: false,
                gotLevel: null,
                rejected: false,
                rejectedReason: null,
                sendRelatedId: null,
                gotRelatedId: null
            };
        }
        const sent = await FriendshipRequest.findOne({ sender, receiver }).lean();
        const got = await FriendshipRequest.findOne({ sender: receiver, receiver: sender }).lean();
        return {
            //sent status
            hearted: sent != null,
            heartedLevel: sent?.level || null,
            sendRelatedId: sent?.relatedId,
            //got status
            gotHeart: got != null,
            gotLevel: got?.level || null,
            gotRelatedId: got?.relatedId,
            rejected: sent?.handleResult === REQUEST_STATUS.REJECTED || got?.handleResult === REQUEST_STATUS.REJECTED,
            rejectedReason: sent?.handleMsg || got?.handleMsg || null
        };
    },

    async getGotDoubleHeart(sender, receiver) {
        return await FriendshipRequest.findOne({ sender, receiver, level: 'high' }).lean();
    },

    async isGotOrSent(sender, receiver) {
        const result = await FriendshipRequest.find({ $or: [{ sender, receiver }, { receiver: sender, sender: receiver }] }).lean();
        return result.length > 0;
    },

    async findOneByRelatedId(relatedId) {
        return await FriendshipRequest.findOne({ relatedId }).lean();
    },
});