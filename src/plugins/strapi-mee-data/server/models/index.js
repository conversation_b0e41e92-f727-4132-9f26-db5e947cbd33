'use strict';

const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const userSchema = new Schema({
  id: { type: Number, required: true, index: true },
  username: { type: String },
  fullname: { type: String },
  fullname: { type: String },
  email: { type: String },
  avatar: Schema.Types.Mixed,
  fakeAvatar: Schema.Types.Mixed,
  birthday: { type: String },
  gender: { type: String, index: true },
  countryCode: { type: String },
  phone: { type: String },
  location: { type: String },
  coordinates: { type: [Number] },
  verificationType: { type: String },
  verificationStatus: { type: String },
  status: { type: String },
  visibility: { type: Boolean, default: false },
  deleted: { type: Boolean, default: false },
  blocked: { type: Boolean, default: false },
  confirmed: { type: Boolean, default: false },
  avgRating: { type: Number, default: 0, index: true },
  interests: { type: [String], index: true },
  university: Schema.Types.Mixed,
  receiveNotification: { type: <PERSON>olean, default: true },
  createdAt: { type: Date, default: Date.now },
  lastAccessAt: { type: Date, index: true },
  level: { type: Number, default: 1 },
  orientation: { type: Number, default: 1 },
  referralCode: { type: String },
  regRefCode: { type: String },
  totalExpenses: { type: Number, index: true },
  balance: { type: Number, index: true },
  fakeName: { type: String, index: true },
  regionCode: { type: String },
  financialStatus: { type: String },
  nationalityCode: { type: String },
  phoneVerificationStatus: { type: String },
  title: { type: String },
});
const User = mongoose.model('user', userSchema);

const recommendationSchema = new Schema({
    recommender: { type: Number, required: true, index: true },
    recommended: { type: Number, required: true, index: true },
    type: { type: String, index: true },
    locked: { type: Boolean, default: false },
    lockPrice: { type: Number, default: 0 },
    status: { type: Number, default: 1, index: true },
    sequence: { type: Number, default: 0 },
    relatedId: { type: Number },
    expireAt: { type: Date },
    createdAt: { type: Date, default: Date.now, index: true }
});
recommendationSchema.index({ expireAt: 1 }, { "expireAfterSeconds": 0 });
const Recommendation = mongoose.model("recommendation", recommendationSchema);

const ratingSchema = new Schema({
    rater: { type: Number, required: true, index: true },
    rated: { type: Number, required: true, index: true },
    score: { type: Number, default: 0 },
    locked: { type: Boolean, default: false },
    lockPrice: { type: Number, default: 0 },
    status: { type: Number, default: 1, index: true },
    relatedId: { type: Number },
    expireAt: { type: Date },
    createdAt: { type: Date, default: Date.now, index: true },
})
ratingSchema.index({ expireAt: 1 }, { "expireAfterSeconds": 0 });
const Rating = mongoose.model("rating", ratingSchema);

const friendshipRequestSchema = new Schema({
    sender: { type: Number, required: true, index: true },
    receiver: { type: Number, required: true, index: true },
    level: { type: String, required: true },
    locked: { type: Boolean, default: false },
    lockPrice: { type: Number, default: 0 },
    status: { type: Number, default: 1, index: true },
    relatedId: { type: Number },
    expireAt: { type: Date },
    createdAt: { type: Date, default: Date.now, index: true },
    handleResult: { type: String },
    handleMsg: { type: String }
})
friendshipRequestSchema.index({ expireAt: 1 }, { "expireAfterSeconds": 0 });
const FriendshipRequest = mongoose.model("friendshipRequest", friendshipRequestSchema);


const friendshipSchema = new Schema({
    user: { type: Number, required: true, index: true },
    friend: { type: Number, required: true, index: true },
    connected: { type: Boolean, default: false },
    deleted: { type: Boolean, default: false },
    relatedId: { type: Number },
    createdAt: { type: Date, default: Date.now, index: true }
})
const Friendship = mongoose.model("friendship", friendshipSchema);

module.exports = {
    User,
    Recommendation,
    Rating,
    FriendshipRequest,
    Friendship
};
