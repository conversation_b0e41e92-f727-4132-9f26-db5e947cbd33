{"name": "mee-data", "version": "0.0.0", "description": "This is the description of the plugin.", "strapi": {"name": "meedata", "description": "Description of Mee Data plugin", "kind": "plugin", "displayName": "Mee <PERSON>"}, "dependencies": {"@strapi/design-system": "^1.6.3", "@strapi/helper-plugin": "^4.6.0", "@strapi/icons": "^1.6.3", "mongoose": "^7.4.4", "prop-types": "^15.7.2"}, "devDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^5.3.4", "styled-components": "^5.3.6"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0", "react-dom": "^17.0.0 || ^18.0.0", "react-router-dom": "^5.3.4", "styled-components": "^5.3.6"}, "author": {"name": "A Strapi developer"}, "maintainers": [{"name": "A Strapi developer"}], "engines": {"node": ">=14.19.1 <=18.x.x", "npm": ">=6.0.0"}, "license": "MIT"}