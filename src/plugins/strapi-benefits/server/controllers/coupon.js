// "use strict";

const { getService } = require("../utils");
const utils = require("@strapi/utils");
const { ValidationError, ForbiddenError } = utils.errors;
const { getErrorMessage } = require("../../../../utils");

module.exports = ({ strapi }) => ({
  async find(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    const { boardId = 0, offset = 0, limit = 20 } = ctx.query;
    var filters = {};

    const accessBoards = await getService("board").findAccessAll(
      authUser.id,
      authUser.level
    );
    const ids = accessBoards.map((board) => board.id);
    const bId = parseInt(boardId);
    if (bId) {
      if (ids.includes(bId)) {
        filters.board = { id: { $eq: bId } };
      } else {
        throw new ForbiddenError(getErrorMessage("forbidden", locale));
      }
    } else {
      filters.board = { id: { $in: ids } };
    }
    const coupons = await getService("coupon").fetchAll({
      filters: filters,
      populate: {
        media: {
          fields: ["id", "url", "mime"],
        },
      },
      sort: { createdAt: "desc" },
      start: offset,
      limit: limit,
    });
    return coupons;
  },

  async findOne(ctx) {
    const { id } = ctx.params;
    const coupon = await getService("coupon").fetch(id, {
      populate: {
        media: {
          fields: ["id", "url", "mime"],
        },
      },
    });
    return coupon;
  },
});
