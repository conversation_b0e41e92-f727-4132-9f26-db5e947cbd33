"use strict";

const { getService } = require("../utils");
const { sanitizeLocale } = require("../../../../utils/locale");
const utils = require("@strapi/utils");
const { ValidationError, ForbiddenError } = utils.errors;
const { getErrorMessage } = require("../../../../utils");

/**
 * postingPermission
 * 0: There are no limits.
 * 1: graduates have access.
 * 2: undergraduate have access.
 * 3: people who join.
 */

module.exports = {
  async find(ctx) {
    const authUser = ctx.state.user;
    const locale = ctx.state.locale;
    if (!authUser) {
      throw new ForbiddenError(getErrorMessage("forbidden", locale));
    }
    const authUserLevel = authUser?.level;
    const { perm = "access" } = ctx.query;
    let boards = [];
    boards = await getService("board").findAll(
      authUser?.id,
      perm,
      authUserLevel
    );
    return await sanitizeLocale(boards, locale);
  },
};
