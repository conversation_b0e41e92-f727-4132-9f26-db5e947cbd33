"use strict";

module.exports = ({ strapi }) => ({
  fetch(id, params) {
    return strapi.entityService.findOne("plugin::benefits.board", id, params);
  },

  fetchAll(params) {
    return strapi.entityService.findMany("plugin::benefits.board", params);
  },

  async findJoined(userId) {
    const res = await strapi.db.connection
      .select("board_id")
      .from("benefits_boards_subscribers_links")
      .where("user_id", userId);
    return res.map((item) => item.board_id);
  },

  async findAccessAll(userId, userLevel) {
    const joinedIds = await this.findJoined(userId);
    const params = {
      filters: {
        blocked: false,
        $or: [
          {
            type: "public",
            $or: [
              { userLevelLimited: { $null: true } },
              { userLevelLimited: { $eq: "" } },
              { userLevelLimited: { $contains: userLevel } },
            ],
          },
          { id: { $in: joinedIds } },
        ],
      },
      sort: { order: "asc" },
    };
    const boards = await this.fetchAll(params);
    const bestBoard = {
      id: 0,
      name: "All",
      locales: {
        en: "All",
        ko: "All",
      },
      default: false,
    };
    boards.splice(0, 0, bestBoard);
    return boards;
  },

  async findPostAll(userId, permIds, userLevel) {
    const joinedIds = await this.findJoined(userId);
    const params = {
      filters: {
        blocked: false,
        $or: [
          {
            type: "public",
            postingPermission: 0,
            $or: [
              { userLevelLimited: { $null: true } },
              { userLevelLimited: { $eq: "" } },
              { userLevelLimited: { $contains: userLevel } },
            ],
          },
          { id: { $in: joinedIds }, postingPermission: { $in: permIds } },
        ],
      },
      sort: { order: "asc" },
    };
    return await this.fetchAll(params);
  },

  async findAll(userId, type, userLevel) {
    let boards = [];
    if (type === "access") {
      // when access board
      boards = await this.findAccessAll(userId, userLevel);
    } else if (type === "post") {
      // when create post
      const permIds = [0, 3];
      // authUser.status === 'GRADUATE' ? permIds.push(2) : permIds.push(1);
      boards = await this.findPostAll(userId, permIds, userLevel);
    }
    return boards;
  },
});
