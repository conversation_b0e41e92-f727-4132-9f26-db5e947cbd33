const getIMToken = async (userId, platformID) => {
    // @ts-ignore
    return await strapi.redis.connections.default.client.get(`IM_TOKEN:${userId}:${platformID}`);
}

const setIMToken = async (userId, platformID, token, expireTimeSeconds) => {
    // @ts-ignore
    return await strapi.redis.connections.default.client.setex(`IM_TOKEN:${userId}:${platformID}`, expireTimeSeconds, token);
}

module.exports = {
    getIMToken,
    setIMToken
}