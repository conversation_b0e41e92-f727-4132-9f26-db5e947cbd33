"use strict";
const axios = require("axios");
const { v4: uuidv4 } = require("uuid");
const { getIMToken } = require("./redis");

// @ts-ignore
const openimAxiosInstance = axios.create({
  timeout: 5000,
  headers: {
    "Content-Type": "application/json",
    operationID: uuidv4(),
  },
});
openimAxiosInstance.request;

openimAxiosInstance.interceptors.request.use(
  async (config) => {
    const { host, adminUserId } = strapi.config.get("plugin.openim");
    config.baseURL = host;
    config.headers.token = await getIMToken(adminUserId, 1);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

openimAxiosInstance.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    return Promise.reject(error);
  }
);

module.exports = openimAxiosInstance;
