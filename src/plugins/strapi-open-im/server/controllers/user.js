"use strict";
const axios = require("axios");
const { getService } = require("../utils");

module.exports = ({ strapi }) => ({
  async getToken(ctx) {
    const authUser = ctx.state.user;
    // const token = await getService("user").getToken({
    //   userId: authUser.id,
    //   isAdmin: false,
    //   forceRefresh: false,
    //   platformId: ctx.state.platformId,
    // });
    const token = "";
    return { imJwt: token };
  },

  async connect(ctx) {
    const authUser = ctx.state.user;
    const { receiver } = ctx.request.body;
    const isNumeric = /^\d+$/.test(receiver);
    if (isNumeric) {
      await strapi
        .plugin("meedata")
        .service("friendships")
        .connected(authUser.id, Number(receiver));
    }
    return { ok: true };
  },

  async importUsers(ctx) {
    await strapi.plugin("openim").service("user").importUsers();
    await strapi.plugin("meedata").service("users").importUsers();

    return { ok: true };
  },
});
