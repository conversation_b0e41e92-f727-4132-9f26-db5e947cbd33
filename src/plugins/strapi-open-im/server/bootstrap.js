"use strict";
const { getService } = require("./utils");

module.exports = async ({ strapi }) => {
  const { adminUserId } = strapi.config.get("plugin.openim");
  //refresh the admin's im token.
  await getService("user").getToken({
    userId: adminUserId,
    isAdmin: true,
    forceRefresh: true,
    platformId: 1,
  });
  //Import all users into the im system.
  // await getService("user").importUsers()
};
