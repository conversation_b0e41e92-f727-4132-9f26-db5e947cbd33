"use strict";
const openimAxiosInstance = require("../utils/axiosInstance");

module.exports = ({ strapi }) => ({
  async setConversations([user1Id, user2Id]) {
    const { errCode, errMsg } = await openimAxiosInstance.post(
      "/conversation/set_conversations",
      {
        userIDs: [String(user1Id), String(user2Id)],
        conversation: {
          conversationID: "sg_1012596513",
        },
      }
    );
    if (errCode === 0) {
    } else {
      strapi.log.error(`openim errCode:${errCode}, errMsg:${errMsg}`);
    }
    return null;
  },
});
