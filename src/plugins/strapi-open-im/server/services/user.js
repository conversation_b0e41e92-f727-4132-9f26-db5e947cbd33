"use strict";
const openimAxiosInstance = require("../utils/axiosInstance");
const { setIMToken, getIMToken } = require("../utils/redis");
module.exports = ({ strapi }) => ({
  /**
   * @param {any} userId
   */
  async getToken({
    userId,
    isAdmin = false,
    forceRefresh = false,
    platformId = 1,
  }) {
    try {
      userId = String(userId);
      if (!forceRefresh) {
        const token = await getIMToken(userId, platformId);
        if (token) return token;
      }
      const { secret } = strapi.config.get("plugin.openim");
      const url = isAdmin ? "/auth/get_admin_token" : "/auth/get_user_token";
      const { errCode, errMsg, data } = await openimAxiosInstance.post(url, {
        secret: secret,
        platformID: platformId,
        userID: userId,
      });
      if (errCode == 0) {
        await setIMToken(
          userId,
          platformId,
          data.token,
          data.expireTimeSeconds
        );
        return data.token;
      }
      strapi.log.error(
        `openim url:${url}, errCode:${errCode}, errMsg:${errMsg}`
      );
      return null;
    } catch (error) {
      strapi.log.error(
        "=============refresh token im token fail!!===========",
        error
      );
      return null;
    }
  },

  /**
   * @param {any} userID
   */
  async accountCheck(userID) {
    const url = "/user/account_check";
    const { errCode, errMsg, data } = await openimAxiosInstance.post(url, {
      checkUserIDs: [String(userID)],
    });
    if (errCode == 0) {
      const { accountStatus } = data.results[0];
      return { registered: accountStatus === "registered" };
    }
    strapi.log.error(`openim url:${url}, errCode:${errCode}, errMsg:${errMsg}`);
    return { registered: false };
  },

  async register({ userID, nickname, faceURL }) {
    userID = String(userID);
    const result = await this.accountCheck(userID);
    if (result.registered) {
      return result;
    }
    const { secret } = strapi.config.get("plugin.openim");
    const url = "/user/user_register";
    const { errCode, errMsg } = await openimAxiosInstance.post(url, {
      secret,
      users: [{ userID, nickname, faceURL }],
    });
    if (errCode == 0) {
      return { registered: true };
    }
    strapi.log.error(`openim url:${url}, errCode:${errCode}, errMsg:${errMsg}`);
    return { registered: false };
  },

  async update({ userID, nickname, faceURL }) {
    userID = String(userID);
    await openimAxiosInstance.post("/user/update_user_info", {
      userInfo: {
        userID,
        nickname,
        faceURL,
      },
    });
  },

  /**
   * Import all users into the im system.
   */
  async importUsers() {
    try {
      const { secret } = strapi.config.get("plugin.openim");
      const entries = await strapi.entityService.findMany(
        "plugin::users-permissions.user",
        {
          populate: {
            avatar: {
              select: ["id", "url"],
            },
          },
        }
      );
      const users = entries.map((entry) => {
        return {
          userID: String(entry.id),
          nickname: entry.username,
          faceURL: entry.avatar?.url,
        };
      });
      const userIds = users.map((user) => user.userID);
      const account_check_url = "/user/account_check";
      const { errCode, errMsg, data } = await openimAxiosInstance.post(
        account_check_url,
        {
          checkUserIDs: userIds,
        }
      );
      if (errCode != 0) {
        strapi.log.error(
          `openim url:${account_check_url}, errCode:${errCode}, errMsg:${errMsg}`
        );
        return false;
      }

      const unregisteredIds = data.results
        .filter((item) => item.accountStatus === "unregistered")
        .map((item) => item.userID);
      if (unregisteredIds.length == 0) return true;

      const unregisteredUsers = users.filter((item) =>
        unregisteredIds.includes(item.userID)
      );

      if (unregisteredUsers.length == 0) return true;
      const url = "/user/user_register";
      const registerResult = await openimAxiosInstance.post(url, {
        secret,
        users: unregisteredUsers,
      });
      if (registerResult.errCode != 0) {
        strapi.log.error(
          `openim url:${url}, errCode:${registerResult.errCode}, errMsg:${registerResult.errMsg}`
        );
      }
      return registerResult.errCode == 0;
    } catch (error) {
      console.log(error);
      return false;
    }
  },

  async getUnReadUserIds() {
    const userIds = [];
    try {
      let cursor = "0";
      do {
        const [nextCursor, keys] =
          await strapi.redis.connections.openim.client.scan(
            cursor,
            "MATCH",
            "USER_BADGE_UNREAD_COUNT_SUM:*"
          );
        for (const key of keys) {
          const unreadCount = await strapi.redis.connections.openim.client.get(
            key
          );
          if (parseInt(unreadCount) > 0) {
            const userId = key.split(":")[1];
            userIds.push(Number(userId));
          }
        }
        cursor = nextCursor;
      } while (cursor !== "0");
    } catch (error) {}
    return userIds;
  },

  async checkUsersUnRead(userIds) {
    try {
      const keys = userIds.map(
        (userId) => `USER_BADGE_UNREAD_COUNT_SUM:${userId}`
      );

      const values = await strapi.redis.connections.openim.client.mget(keys);
      if (!values || values.length === 0) {
        return [];
      }

      return userIds.filter(
        (_, index) => values[index] != null && values[index] > 0
      );
    } catch (error) {
      return [];
    }
  },
});
