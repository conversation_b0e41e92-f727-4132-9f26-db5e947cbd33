import { useIntl } from "react-intl";

import getTrad from "../../../utils/getTrad";

const filterSchema = () => {
  const { formatMessage } = useIntl();

  return [
    {
      name: "origin",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.origin"),
          defaultMessage: "origin",
        }),
      },
      fieldSchema: {
        type: "enumeration",
        options: [
          'APP', 'BACKEND'
        ],
      },
    },
    {
      name: "operation",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.operation"),
          defaultMessage: "operation",
        }),
      },
      fieldSchema: {
        type: "enumeration",
        options: [
          "EXPORE",
          "SAVE",
          "DELETE",
          "UPDATE",
          "LAUNCH",
          "LOGIN",
          "SEND_SINGLE_HEART",
          "SEND_DOUBLE_HEART",
          "ACCEPT_HEART",
          "OPEN_GIVEN_CARD",
          "OPEN_PREMIUM_CARD",
          "UNLOCK_GOT_CARD",
          "UNLOCK_HIGH_RATE_CARD",
          "RATE_CARD",
          "VIEW_CARD",
          "UPDATE_USER",
          "DELETE_USER",
          "REFRESH_IM_TOKEN",
          "PUBLISH_POST",
          "LIKE_POST",
          "UPDATE_POST",
          "DELETE_POST",
          "PUBLISH_COMMENT",
          "LIKE_COMMENT",
          "DELETE_COMMENT"
        ]
      },
    },
    {
      name: "createdAt",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.createdat"),
          defaultMessage: "Date",
        }),
      },
      fieldSchema: { type: "datetime" },
    },
    {
      name: "operatorId",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.operatorId"),
          defaultMessage: "Operator Id",
        }),
      },
      fieldSchema: { type: "integer" },
    },
    {
      name: "operatorName",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.operatorName"),
          defaultMessage: "Operator Name",
        }),
      },
      fieldSchema: { type: "string" },
    },
    {
      name: "operatorEmail",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.operatorEmail"),
          defaultMessage: "Operator Email",
        }),
      },
      fieldSchema: { type: "string" },
    },
    {
      name: "ip_address",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.ip_address"),
          defaultMessage: "IP Address",
        }),
      },
      fieldSchema: { type: "string" },
    },
    {
      name: "device_id",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.device_id"),
          defaultMessage: "Device ID",
        }),
      },
      fieldSchema: { type: "string" },
    },
    {
      name: "url",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.url"),
          defaultMessage: "URL",
        }),
      },
      fieldSchema: { type: "string" },
    },
    {
      name: "http_method",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.http_method"),
          defaultMessage: "HTTP Method",
        }),
      },
      fieldSchema: { type: "string" },
    },
    {
      name: "http_status",
      metadatas: {
        label: formatMessage({
          id: getTrad("content.http_status"),
          defaultMessage: "HTTP Status",
        }),
      },
      fieldSchema: { type: "string" },
    }
  ];
};

export default filterSchema;
