{"fetch.success": "Logs have been loaded.", "fetch.error": "Failed to retrieve the logs.", "title": "<PERSON><PERSON>", "subtitle": "entries found", "content.empty": "You don't have any logs yet!", "content.empty.search": "No logs match the search.", "content.modal.title": "Log entry", "content.filter.label": "Filters", "content.search.label": "Search logs", "content.id": "ID", "content.user": "User", "content.operatorId": "Operator Id", "content.operatorName": "Operator Name", "content.operatorEmail": "Operator <PERSON><PERSON>", "content.ip_address": "IP Address", "content.url": "URL", "content.http_method": "HTTP Method", "content.http_status": "HTTP Status", "content.request_header": "Request Header", "content.request_body": "Request Body", "content.response_body": "Response Body", "content.createdat": "Created At", "section.logs.label": "Logs", "section.log-settings.label": "Log Settings", "settings.fetch.success": "Log settings have been loaded", "settings.fetch.error": "Failed to retrieve the log settings", "settings.put.success": "Setting<PERSON> saved successfully", "settings.put.error": "Failed to save settings", "settings.title": "<PERSON>t Log Settings", "settings.subtitle": "All settings related to the audit log plugin", "settings.save": "Save", "settings.box.warning": "The settings will be saved only for this session. Enter these settings into the configuration file for persistence. check the documentation here", "settings.box.header": "Delete frequency", "settings.box.subheading": "This settings block determines the frequency at which the auditlogs will be deleted, and based on what criteria.", "settings.box.frequency-input.before": "Delete based on", "settings.box.frequency-input.logAge": "Log Age", "settings.box.frequency-input.logNumber": "Number of Logs", "settings.box.frequency-input.after.logAge": ", deleting logs older than", "settings.box.logAge-input.error": "Please enter a value greater than 1", "settings.box.frequency-input.after.logCount": ", persisting only the latest", "settings.box.logCount-input.error": "Please enter a value greater than 1", "settings.box.logCount-input.after": "log records.", "settings.box.interval-input.day": "day", "settings.box.interval-input.week": "week", "settings.box.interval-input.month": "month", "settings.box.interval-input.year": "year"}