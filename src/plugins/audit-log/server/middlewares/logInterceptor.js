const _ = require("lodash");
const pluginId = require("../utils/pluginId");

const getFilterResult = (filter, actualValue, checkFunction) => {
  let result = true;
  let check = (val, valToCheck) => val === valToCheck;
  if (checkFunction) check = checkFunction;
  if (filter) {
    if (filter.include) {
      result = filter.include.some((val) => check(val, actualValue));
    } else if (filter.exclude) {
      result = !filter.exclude.some((val) => check(val, actualValue));
    }
  }
  return result;
};

const replaceContents = (obj, excludedValues) =>
  _.mapValues(obj, (value, key) => {
    if (excludedValues.includes(key)) return "#_REDACTED_#";
    if (typeof value === "object")
      return replaceContents(value, excludedValues);
    return value;
  });

const getOperationName = (method, url, request) => {
  if (/\/api\/auth\/(.*?)\/callback/.test(url)) {
    return "LOGIN";
  } else if (url === "/api/check-version") {
    return "LAUNCH";
  } else if (url === "/api/openim/token") {
    return "REFRESH_IM_TOKEN";
  } else if (url.startsWith("/api/cards")) {
    const cardPatterns = [
      {
        name: "SEND_HEART",
        method: "POST",
        url: "/api/cards/:id/heart",
        pattern: /^\/api\/cards\/(\d+)\/heart$/,
      },
      {
        name: "ACCEPT_HEART",
        method: "POST",
        url: "/api/cards/:id/accept",
        pattern: /^\/api\/cards\/(\d+)\/accept$/,
      },
      {
        name: "OPEN_PREMIUM_CARD",
        method: "POST",
        url: "/api/cards/premium",
        pattern: /^\/api\/cards\/premium$/,
      },
      {
        name: "UNLOCK_CARD",
        method: "POST",
        url: "/api/cards/:id/unlock",
        pattern: /^\/api\/cards\/(\d+)\/unlock$/,
      },
      {
        name: "RATE_CARD",
        method: "POST",
        url: "/api/cards/:id/rating",
        pattern: /^\/api\/cards\/(\d+)\/rating$/,
      },
      {
        name: "VIEW_CARD",
        method: "GET",
        url: "/api/cards/:id",
        pattern: /^\/api\/cards\/(\d+)$/,
      },
    ];
    let matchedCardPattern = null;
    for (const cardPattern of cardPatterns) {
      if (method === cardPattern.method && cardPattern.pattern.test(url)) {
        matchedCardPattern = cardPattern;
        break;
      }
    }
    if (matchedCardPattern) {
      if (matchedCardPattern.name === "SEND_HEART") {
        const { level } = request;
        return level && level === "high"
          ? "SEND_DOUBLE_HEART"
          : "SEND_SINGLE_HEART";
      } else if (matchedCardPattern.name === "UNLOCK_CARD") {
        const { lock } = request;
        if (lock === "HEART") {
          return "UNLOCK_GOT_CARD";
        } else if (lock === "HEART") {
          return "UNLOCK_HIGH_RATE_CARD";
        } else {
          return "OPEN_GIVEN_CARD";
        }
      }
      return matchedCardPattern.name;
    }
  } else if (url.startsWith("/api/cards")) {
    if (url === "/api/users/delete") {
      return "DELETE_USER";
    } else if (method === "PUT" && /^\/api\/users\/(\d+)$/.test(url)) {
      return "UPDATE_USER";
    }
  } else if (url.startsWith("/api/community")) {
    const communityPatterns = [
      {
        name: "PUBLISH_POST",
        method: "POST",
        url: "/api/community/posts",
        pattern: /^\/api\/community\/posts$/,
      },
      {
        name: "LIKE_POST",
        method: "POST",
        url: "/api/community/posts/:id/like",
        pattern: /^\/api\/community\/posts\/(\d+)\/like$/,
      },
      {
        name: "UPDATE_POST",
        method: "PUT",
        url: "/api/community/posts/:id",
        pattern: /^\/api\/community\/posts\/(\d+)$/,
      },
      {
        name: "DELETE_POST",
        method: "DELETE",
        url: "/api/community/posts/:id",
        pattern: /^\/api\/community\/posts\/(\d+)$/,
      },
      {
        name: "PUBLISH_COMMENT",
        method: "POST",
        url: "/api/community/comments",
        pattern: /^\/api\/community\/comments$/,
      },
      {
        name: "LIKE_COMMENT",
        method: "POST",
        url: "/api/community/comments/:id/like",
        pattern: /^\/api\/community\/comments\/(\d+)\/like$/,
      },
      {
        name: "DELETE_COMMENT",
        method: "DELETE",
        url: "/api/community/comments/:id",
        pattern: /^\/api\/community\/comments\/(\d+)$/,
      },
    ];
    let matchedCommunityPattern = null;
    for (const communityPattern of communityPatterns) {
      if (
        method === communityPattern.method &&
        communityPattern.pattern.test(url)
      ) {
        matchedCommunityPattern = communityPattern;
        break;
      }
    }
    if (matchedCommunityPattern) return matchedCommunityPattern.name;
  }

  switch (method) {
    case "POST":
      return "SAVE";
    case "PUT":
      return "UPDATE";
    case "DELETE":
      return "UPDATE";
    default:
      return "EXPORE";
  }
};

module.exports = ({ strapi }) => {
  strapi.server.use(async (ctx, next) => {
    await next();
    try {
      const config = strapi.config.get(`plugin.${pluginId}`);

      const endpoint = getFilterResult(
        config.filters.endpoint,
        ctx.url,
        (val, valToCheck) => valToCheck.startsWith(val)
      );
      const status = getFilterResult(config.filters.status, ctx.status);
      const method = getFilterResult(config.filters.method, ctx.method);

      let url = ctx.url;
      if (url.startsWith("/api") && endpoint && status && method) {
        let request = {};
        let response = {};
        //ER_DATA_TOO_LONG: Data too long for column 'url' at row 1
        const match = url.match(/\/api\/auth\/(.*?)\/callback/);
        if (match) {
          url = `/api/auth/${match[1]}/callback`;
        }
        const postsMatch = url.match(/\/api\/community\/posts\?boardId=\d+/);
        if (postsMatch) {
          url = postsMatch[0];
        }

        if (ctx.request.body) {
          request = replaceContents(
            JSON.parse(JSON.stringify(ctx.request.body)),
            config.redactedValues
          );
        }

        const operation = getOperationName(ctx.method, url, request);

        const saveResponse = operation === "LOGIN" || ctx.method != "GET";

        if (ctx.response.body && saveResponse) {
          response = replaceContents(
            JSON.parse(JSON.stringify(ctx.response.body)),
            config.redactedValues
          );
        }
        let header = {};
        let userAgent = "";
        let deviceId = "";
        if (ctx.request.header) {
          header = _.pick(ctx.request.header, config.pickHeaders);
          userAgent = header["user-agent"];
          deviceId = header["x-starcheck-deviceid"];
        }
        const origin = url.startsWith("/api") ? "APP" : "BACKEND";
        let user = ctx.state.user;
        if (operation === "LOGIN" && response.user) {
          user = response.user;
        }
        const data = {
          origin,
          operation,
          operatorId: user?.id || 0,
          operatorName: user?.username || "Anonymous",
          operatorEmail: user?.email || "",
          url,
          ip_address: ctx.ip,
          http_method: ctx.method,
          http_status: ctx.status,
          device_id: deviceId,
          user_agent: userAgent,
          request_header: header,
          request_body: request,
          response_body: response,
        };

        strapi.entityService.create(`plugin::${pluginId}.log`, {
          data,
        });
      }
    } catch (error) {
      strapi.log.error(
        "========= failed to save the visit log ============",
        error
      );
    }
  });
};
