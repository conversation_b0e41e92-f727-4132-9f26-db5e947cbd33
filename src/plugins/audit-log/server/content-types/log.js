"use strict";

module.exports = {
  kind: "collectionType",
  collectionName: "log",
  info: {
    singularName: "log",
    pluralName: "logs",
    displayName: "Logs",
  },
  options: {
    draftAndPublish: false,
    comment: "",
  },
  pluginOptions: {
    "content-manager": {
      visible: false,
    },
    "content-type-builder": {
      visible: false,
    },
  },
  attributes: {
    origin: {
      type: "enumeration",
      enum: [
        "APP",
        "BACKEND"
      ]
    },
    operatorId: {
      type: "integer",
    },
    operatorName: {
      type: "string",
    },
    operatorEmail: {
      type: "string",
    },
    operation: {
      type: "enumeration",
      enum: [
        "LOGIN",
        "EXPORE",
        "SAVE",
        "DELETE",
        "UPDATE",
        "LAUNCH",
        "SEND_SINGLE_HEART",
        "SEND_DOUBLE_HEART",
        "ACCEPT_HEART",
        "OPEN_GIVEN_CARD",
        "OPEN_PREMIUM_CARD",
        "UNLOCK_GOT_CARD",
        "UNLOCK_HIGH_RATE_CARD",
        "RATE_CARD",
        "VIEW_CARD",
        "UPDATE_USER",
        "DELETE_USER",
        "REFRESH_IM_TOKEN",
        "PUBLISH_POST",
        "LIKE_POST",
        "UPDATE_POST",
        "DELETE_POST",
        "PUBLISH_COMMENT",
        "LIKE_COMMENT",
        "DELETE_COMMENT"
      ]
    },
    url: {
      type: "string",
    },
    ip_address: {
      type: "string",
    },
    http_method: {
      type: "string",
    },
    http_status: {
      type: "integer",
    },
    device_id: {
      type: "string",
    },
    user_agent: {
      type: "string",
    },
    request_header: {
      type: "json",
    },
    request_body: {
      type: "json",
    },
    response_body: {
      type: "json",
    },
  },
};
