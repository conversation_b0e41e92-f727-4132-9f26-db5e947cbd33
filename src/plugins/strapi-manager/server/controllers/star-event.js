"use strict";

/**
 * star-event controller
 */

module.exports = ({ strapi }) => ({
  /**
   * Find all star events
   * @param {Object} ctx - Koa context
   */
  async find(ctx) {
    try {
      const { startDate, endDate, page = 1, pageSize = 20 } = ctx.query;
      let filters = {};

      // Date filtering
      if (startDate) {
        filters = { ...filters, startDate: { $gte: new Date(startDate) } };
      }

      if (endDate) {
        filters = { ...filters, startDate: { $lte: new Date(endDate) } };
      }

      // Use findPage instead of findMany to support pagination
      const { results, pagination } = await strapi.entityService.findPage(
        "api::star-event.star-event",
        {
          filters,
          sort: { startDate: "desc" },
          populate: ["photos", "star"],
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      // Return results and pagination info
      ctx.body = {
        results,
        pagination,
      };
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Find a single star event
   * @param {Object} ctx - Koa context
   */
  async findOne(ctx) {
    try {
      const { id } = ctx.params;
      const event = await strapi.entityService.findOne(
        "api::star-event.star-event",
        id,
        {
          populate: ["photos", "star", "previewImage"],
        }
      );

      if (!event) {
        return ctx.notFound("Event not found");
      }

      return event;
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Create a star event
   * @param {Object} ctx - Koa context
   */
  async create(ctx) {
    try {
      const { body } = ctx.request;
      const user = ctx.state.user;

      // Create event
      const newEvent = await strapi.entityService.create(
        "api::star-event.star-event",
        {
          data: body,
        }
      );

      return newEvent;
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Update a star event
   * @param {Object} ctx - Koa context
   */
  async update(ctx) {
    try {
      const { id } = ctx.params;
      const { body } = ctx.request;
      const user = ctx.state.user;

      // Check if event exists
      const existingEvent = await strapi.entityService.findOne(
        "api::star-event.star-event",
        id,
        {
          populate: ["star"],
        }
      );

      if (!existingEvent) {
        return ctx.notFound("Event not found");
      }
      // Update event
      const updatedEvent = await strapi.entityService.update(
        "api::star-event.star-event",
        id,
        {
          data: body,
        }
      );

      return updatedEvent;
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Delete a star event
   * @param {Object} ctx - Koa context
   */
  async delete(ctx) {
    try {
      const { id } = ctx.params;
      const user = ctx.state.user;

      // Check if event exists
      const existingEvent = await strapi.entityService.findOne(
        "api::star-event.star-event",
        id,
        {
          populate: ["star"],
        }
      );

      if (!existingEvent) {
        return ctx.notFound("Event not found");
      }

      // Delete event
      await strapi.entityService.delete("api::star-event.star-event", id);

      return { message: "Event successfully deleted" };
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Get event categories
   * @param {Object} ctx - Koa context
   */
  async getEventSections(ctx) {
    try {
      // Get all event types as categories
      const events = await strapi.entityService.findMany(
        "api::star-event.star-event",
        {
          fields: ["type"],
        }
      );

      // Extract unique types
      const types = [...new Set(events.map((event) => event.type))];
      const sections = types.map((type) => ({
        id: type,
        name: type === "normal" ? "Regular Event" : "Special Guest Event",
      }));

      return sections;
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Get event list (supports filtering and pagination)
   * @param {Object} ctx - Koa context
   */
  async getEvents(ctx) {
    try {
      const { type, page = 1, pageSize = 10 } = ctx.query;

      // Build query conditions
      const query = {
        populate: ["photos", "star"],
        pagination: {
          page,
          pageSize,
        },
        sort: { startDate: "desc" },
      };

      // If type is specified, add filter condition
      if (type) {
        query.filters = { type };
      }

      const events = await strapi.entityService.findMany(
        "api::star-event.star-event",
        query
      );
      const count = await strapi.entityService.count(
        "api::star-event.star-event",
        query
      );

      return {
        data: events,
        meta: {
          pagination: {
            page: parseInt(page),
            pageSize: parseInt(pageSize),
            pageCount: Math.ceil(count / pageSize),
            total: count,
          },
        },
      };
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Get past events that the user has participated in
   * @param {Object} ctx - Koa context
   */
  async getMyPastEvents(ctx) {
    try {
      const user = ctx.state.user;
      if (!user) {
        return ctx.unauthorized("Please login first");
      }

      const currentDate = new Date();

      // Find events the user has participated in
      const members = await strapi.entityService.findMany(
        "api::star-event-member.star-event-member",
        {
          filters: {
            user: user.id,
            status: "joined",
          },
          populate: ["event"],
        }
      );

      // Extract completed events
      const pastEventIds = members
        .filter((member) => new Date(member.event.startDate) < currentDate)
        .map((member) => member.event.id);

      // Get detailed information for these events
      const pastEvents = await strapi.entityService.findMany(
        "api::star-event.star-event",
        {
          filters: {
            id: { $in: pastEventIds },
          },
          populate: ["photos", "star"],
          sort: { startDate: "desc" },
        }
      );

      return pastEvents;
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * User applies to join an event
   * @param {Object} ctx - Koa context
   */
  async joinEvent(ctx) {
    try {
      const { id } = ctx.params;
      const user = ctx.state.user;

      if (!user) {
        return ctx.unauthorized("Please login first");
      }

      // Check if event exists
      const event = await strapi.entityService.findOne(
        "api::star-event.star-event",
        id
      );
      if (!event) {
        return ctx.notFound("Event not found");
      }

      // Check if event has already started
      if (new Date(event.startDate) < new Date()) {
        return ctx.badRequest("Event has already started, cannot join");
      }

      // Check if the current number of participants has reached the maximum
      const currentMembers = await strapi.entityService.count(
        "api::star-event-member.star-event-member",
        {
          filters: {
            event: id,
            status: "joined",
          },
        }
      );

      if (currentMembers >= event.maxMembers) {
        return ctx.badRequest("Event is full");
      }

      // Check if user has already applied
      const existingMember = await strapi.entityService.findMany(
        "api::star-event-member.star-event-member",
        {
          filters: {
            user: user.id,
            event: id,
          },
        }
      );

      if (existingMember.length > 0) {
        return ctx.badRequest("You have already applied for this event");
      }

      // Create application record
      const member = await strapi.entityService.create(
        "api::star-event-member.star-event-member",
        {
          data: {
            user: user.id,
            event: id,
            status: "applied",
          },
        }
      );

      return {
        message: "Application successful, waiting for approval",
        data: member,
      };
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Approve user's request to join an event
   * @param {Object} ctx - Koa context
   */
  async approveJoinRequest(ctx) {
    try {
      const { id } = ctx.params;
      const { memberId } = ctx.request.body;
      const user = ctx.state.user;

      // Check if event exists
      const event = await strapi.entityService.findOne(
        "api::star-event.star-event",
        id,
        {
          populate: ["star"],
        }
      );

      if (!event) {
        return ctx.notFound("Event not found");
      }

      // Update application status to joined
      const updatedMember = await strapi.entityService.update(
        "api::star-event-member.star-event-member",
        memberId,
        {
          data: {
            status: "joined",
          },
        }
      );

      return { message: "User approved to join event", data: updatedMember };
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Reject user's request to join an event
   * @param {Object} ctx - Koa context
   */
  async rejectJoinRequest(ctx) {
    try {
      const { id } = ctx.params;
      const { memberId } = ctx.request.body;
      const user = ctx.state.user;

      // Check if event exists
      const event = await strapi.entityService.findOne(
        "api::star-event.star-event",
        id,
        {
          populate: ["star"],
        }
      );

      if (!event) {
        return ctx.notFound("Event not found");
      }
      // Find application record
      const member = await strapi.entityService.findOne(
        "api::star-event-member.star-event-member",
        memberId
      );

      // Update application status to rejected
      const updatedMember = await strapi.entityService.update(
        "api::star-event-member.star-event-member",
        memberId,
        {
          data: {
            status: "rejected",
          },
        }
      );

      return {
        message: "User rejected from joining event",
        data: updatedMember,
      };
    } catch (err) {
      ctx.throw(500, err);
    }
  },

  /**
   * Get event member list (can be filtered by status)
   * @param {Object} ctx - Koa context
   */
  async getEventMembers(ctx) {
    try {
      const { id } = ctx.params;
      const { status, page = 1, pageSize = 20 } = ctx.query;

      // Check if event exists
      const event = await strapi.entityService.findOne(
        "api::star-event.star-event",
        id
      );

      if (!event) {
        return ctx.notFound("Event not found");
      }

      // Build query conditions
      const filters = {
        event: id,
      };

      // If status parameter is provided, add to filter conditions
      if (status) {
        filters.status = status;
      }

      // Query members
      const members = await strapi.entityService.findPage(
        "api::star-event-member.star-event-member",
        {
          filters,
          populate: ["user", "user.avatar"],
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      return members;
    } catch (err) {
      ctx.throw(500, err);
    }
  },
});
