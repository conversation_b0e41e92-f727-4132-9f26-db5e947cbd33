'use strict';
const dayjs = require('dayjs');
const utils = require('@strapi/utils');
const { ValidationError } = utils.errors;
const _ = require('lodash');

module.exports = {
    async find(ctx) {
        let { username, cardname, startDate, endDate, type, page = 1, pageSize = 20 } = ctx.query;
        let filters = {};
        if (username) {
            filters = { ...filters, recommended: { username: { $contains: username } } }
        }
        if (cardname) {
            filters = { ...filters, recommender: { username: { $contains: cardname } } }
        }
        if (startDate) startDate = dayjs(startDate).startOf('day').valueOf();
        if (endDate) endDate = dayjs(endDate).endOf('day').valueOf();
        if (startDate && endDate) {
            filters = { ...filters, createdAt: { $between: [startDate, endDate] } };
        } else if (startDate) {
            filters = { ...filters, createdAt: { $gte: startDate } };
        } else if (endDate) {
            filters = { ...filters, createdAt: { $lte: endDate } };
        }
        if (type) {
            filters = { ...filters, type };
        }

        const { results, pagination } = await strapi.entityService.findPage('api::recommendation.recommendation', {
            filters,
            sort: { id: 'desc' },
            populate: {
                recommender: { fields: ['id', 'username', 'level'], populate: { avatar: { fields: ['name', 'url', 'ext', 'mime', 'size', 'formats'] } } },
                recommended: { fields: ['id', 'username', 'level'], populate: { avatar: { fields: ['name', 'url', 'ext', 'mime', 'size', 'formats'] } } }
            },
            page: page,
            pageSize: pageSize,
        });
        const sanitizedResults = results.map(item => {
            const diffInDays = dayjs(item.sequence).diff(dayjs(item.createdAt, "YYYYMMDD"), 'day');
            item.diffInDays = diffInDays;
            return item;
        })
        ctx.body = {
            results: sanitizedResults,
            pagination,
        };
    },

    async getRules(ctx) {
        return await strapi.service('api::setting.setting').getDailyGivenRules();
    },

    async updateRules(ctx) {
        let { values } = ctx.request.body;
        const uniqueValues = _.uniq(values.map(Number));
        const isValid = _.every(uniqueValues, value => _.isInteger(value) && value >= 0 && value <= 23);
        if (!isValid) {
            throw new ValidationError();
        }
        return await strapi.service('api::setting.setting').updateSetting("daily_given_cards", uniqueValues);
    }
};