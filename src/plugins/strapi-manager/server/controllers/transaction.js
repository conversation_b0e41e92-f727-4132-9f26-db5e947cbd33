'use strict';
const dayjs = require('dayjs');
const utils = require('@strapi/utils');
const { ApplicationError } = utils.errors;
const { getErrorMessage } = require('../../../../utils')

module.exports = {
    async find(ctx) {
        let { username, type, startDate, endDate, page = 1, pageSize = 20 } = ctx.query;
        let filters = {};
        if (username) {
            filters = { ...filters, customer: { username: { $contains: username } } }
        }
        if (type) {
            filters = { ...filters, type }
        }

        if (startDate) startDate = dayjs(startDate).startOf('day').valueOf();
        if (endDate) endDate = dayjs(endDate).endOf('day').valueOf();
        if (startDate && endDate) {
            filters = { ...filters, createdAt: { $between: [startDate, endDate] } };
        } else if (startDate) {
            filters = { ...filters, createdAt: { $gte: startDate } };
        } else if (endDate) {
            filters = { ...filters, createdAt: { $lte: endDate } };
        }

        const { results, pagination } = await strapi.entityService.findPage('api::transaction.transaction', {
            filters,
            sort: { id: 'desc' },
            populate: {
                customer: { fields: ['id', 'username'] },
            },
            page: page,
            pageSize: pageSize,
        });
        ctx.body = {
            results,
            pagination,
        };
    },

    async deposit(ctx) {
        const { customer, amount, note } = ctx.request.body;
        if (!customer || !amount || !/^[1-9][0-9]*$/.test(amount)) {
            throw new ApplicationError(getErrorMessage('error.validated'));
        }
        return await strapi.service('api::transaction.transaction').deposit(customer, amount, note);
    },

    async deduct(ctx) {
        const { customer, amount, note } = ctx.request.body;
        if (!customer || !amount || !/^[1-9][0-9]*$/.test(amount)) {
            throw new ApplicationError(getErrorMessage('error.validated'));
        }
        return await strapi.service('api::transaction.transaction').deduct(customer, amount, note);
    },

    async distribute(ctx) {
        let { audienceType, audiences, amount, note } = ctx.request.body;
        if (!audienceType || !amount || !/^[1-9][0-9]*$/.test(amount)) {
            throw new ApplicationError(getErrorMessage('error.validated'));
        }
        if (!['all', 'segment'].includes(audienceType)) {
            throw new ApplicationError(getErrorMessage('error.validated'));
        }
        if (audienceType == 'segment' && audiences.length == 0)
            throw new ApplicationError(getErrorMessage('error.validated'));


        if (audienceType === 'all') {
            const users = await strapi.entityService.findMany('plugin::users-permissions.user', {
                fields: ['id'],
                filters: { deleted: false }
            });
            audiences = users.map(user => user.id);
        }
        if (audiences.length > 0) {
            audiences.forEach(async audience => {
                await strapi.plugin('mq').service('publish').sendmDistributeMessage({
                    type: "MC_DISTRIBUTE",
                    id: audience,
                    amount,
                    note
                });
            });
        }
        return {
            ok: true
        }
    }
};