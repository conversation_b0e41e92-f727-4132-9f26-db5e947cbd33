const semver = require("semver");
const { getAppVersion, setAppVersion } = require("../../../../utils/redis");

module.exports = {
  async getVersionSettings(ctx) {
    return await getAppVersion();
  },

  async updateVersionSettings(ctx) {
    await setAppVersion(ctx.request.body);
    return {
      ok: true,
    };
  },

  async checkVersion(ctx) {
    const version = ctx.request.header["x-starcheck-version"] || "1.0.0";
    const platform = ctx.request.header["x-starcheck-platform"] || "android";

    const appVersion = await getAppVersion();
    const latestVersion = appVersion[platform]?.latestVersion || "1.0.0";
    const forceUpdateVersion =
      appVersion[platform]?.forceUpdateVersion || "1.0.0";
    return {
      latestVersion,
      update: semver.lt(version, latestVersion),
      forceUpdate: semver.lt(version, forceUpdateVersion),
    };
  },

  async getServiceVersion(ctx) {
    const version = ctx.state.version;
    const platform = ctx.state.platform;
    const appVersion = await getAppVersion();
    const jobServiceVersion =
      appVersion[platform]?.jobServiceVersion || "1.0.0";
    // return jobServiceVersion == version ? "B" : "A";
    return jobServiceVersion == version ? "A" : "A";
  },
};
