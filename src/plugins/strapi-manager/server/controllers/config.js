'use strict';

module.exports = {
    async findUniversities(ctx) {
        const entries = await strapi.db.query('api::university.university').findMany({
            select: ['id', 'name', 'emailSuffix', 'emailSuffixes', 'emailRegex', 'website', 'major', 'location', 'rank', 'source'],
            where: { blocked: false },
            populate: {
                logo: {
                    fields: ['name', 'url', 'ext', 'mime', 'size', 'formats']
                }
            },
            orderBy: { id: 'asc' }
        });
        return entries;
    },
};