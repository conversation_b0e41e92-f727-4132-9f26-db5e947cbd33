"use strict";
const dayjs = require("dayjs");
const { VERIFICATION_STATUS, AVATAR } = require("../../../../constants");
const _ = require("lodash");
const utils = require("@strapi/utils");
const { ValidationError } = utils.errors;
const { getService } = require("../utils");
const {
  saveRejectionReason,
  getRejectionReason,
} = require("../../../../utils/redis");
const XLSX = require("xlsx");
const {
  NOTIFICATION_TYPE,
} = require("../../../strapi-notification/server/constants");

module.exports = ({ strapi }) => ({
  async find(ctx) {
    let {
      username,
      verificationType,
      verificationStatus,
      startDate,
      endDate,
      level,
      gender,
      latestAccessTime,
      page = 1,
      pageSize = 20,
    } = ctx.query;

    let filters = { deleted: false };
    if (username) {
      filters = { ...filters, username: { $regex: username, $options: "i" } };
    }
    // if (university) {
    //   filters = { ...filters, "university.id": Number(university) };
    // }
    if (verificationType) {
      filters = { ...filters, verificationType };
    }
    if (verificationStatus) {
      filters = { ...filters, verificationStatus };
    }
    if (startDate) startDate = dayjs(startDate).startOf("day").valueOf();
    if (endDate) endDate = dayjs(endDate).endOf("day").valueOf();
    if (startDate && endDate) {
      filters = { ...filters, createdAt: { $gte: startDate, $lte: endDate } };
    } else if (startDate) {
      filters = { ...filters, createdAt: { $gte: startDate } };
    } else if (endDate) {
      filters = { ...filters, createdAt: { $lte: endDate } };
    }
    if (level) {
      filters = { ...filters, level };
    }
    if (gender) {
      filters = { ...filters, gender };
    }
    if (latestAccessTime) {
      filters = {
        ...filters,
        lastAccessAt: { $gte: dayjs(Number(latestAccessTime)).valueOf() },
      };
    }
    // const { results, pagination } = await getService('user').pageAll({
    //   filters,
    //   sort: { id: 'desc' },
    //   populate: {
    //     avatar: {
    //       fields: ["url"]
    //     },
    //     university: {
    //       fields: ["name"]
    //     }
    //   },
    //   page: page,
    //   pageSize: pageSize,
    // });
    // ctx.body = {
    //   results: results,
    //   pagination,
    // };
    return strapi
      .plugin("meedata")
      .service("users")
      .pageAll(filters, Number(page), Number(pageSize));
  },

  async findOne(ctx) {
    const { id } = ctx.params;
    const user = await getService("user").fetch(id, {
      populate: {
        avatar: { fields: ["url"] },
        fakeAvatar: { fields: ["url"] },
        university: { fields: ["name"] },
        photos: { fields: ["name", "url", "ext", "mime", "size", "formats"] },
        interests: { fields: ["name"] },
        asset_verifications: {
          fields: ["type", "name", "description"],
          populate: {
            proof: {
              fields: ["url", "name", "ext", "mime", "size", "formats"],
            },
          },
        },
        idcard: { fields: ["url", "formats"] },
        asset_certification: {
          fields: ["issuedate", "expirydate", "asset_result"],
          populate: {
            review_users: true,
          },
        },
      },
    });
    user.rejectionReason = await getRejectionReason(id);
    if (user && (user.certificate || user.certificateList)) {
      user.certificateList = Array.isArray(user.certificateList)
        ? user.certificateList
        : [];

      // Add the certificate if the user has one and it doesn't exist in the list
      if (user.certificate && user.certificate.id) {
        const certificateExists = user.certificateList.some(
          (certificate) => certificate.id === user.certificate.id
        );
        if (!certificateExists) {
          user.certificateList.push(user.certificate);
        }
      }
    }
    return user;
  },

  async verification(ctx) {
    const { id } = ctx.params;
    const { verificationStatus, status, rejectionReason } = ctx.request.body;

    if (
      _.has(ctx.request.body, "verificationStatus") &&
      verificationStatus === VERIFICATION_STATUS.rejected &&
      !rejectionReason
    ) {
      throw new ValidationError();
    }
    let user = await getService("user").fetch(id);

    let params = {
      verificationStatus,
      visibility: verificationStatus === VERIFICATION_STATUS.verified,
    };
    if (status)
      params = {
        ...params,
        status,
      };

    //save verification log
    const lastestLog = await strapi.entityService.create(
      "api::verification-log.verification-log",
      {
        data: {
          status: verificationStatus,
          user: id,
          note:
            verificationStatus == VERIFICATION_STATUS.rejected
              ? rejectionReason
              : { en: "", vi: "" },
        },
      }
    );
    params = {
      ...params,
      verificationLogs: { connect: [{ id: lastestLog.id }], disconnect: [] },
    };
    await saveRejectionReason(
      user.id,
      verificationStatus == VERIFICATION_STATUS.rejected
        ? rejectionReason
        : { en: "", vi: "" }
    );

    user = await strapi.entityService.update(
      "plugin::users-permissions.user",
      id,
      {
        data: params,
        populate: { university: true },
      }
    );

    //Automatically join or leave the university board
    if (user && user.university) {
      let related = `api::university.university:${user.university.id}`;
      if (["global", "custom"].includes(user.university.source)) {
        related = "api::university.university:0";
      }
      if (user.verificationStatus === "verified") {
        await strapi.service("plugin::community.board").join(user.id, related);
      } else {
        await strapi.service("plugin::community.board").leave(user.id, related);
      }
    }
    if (user && verificationStatus) {
      if (verificationStatus === VERIFICATION_STATUS.rejected) {
        await strapi
          .plugin("mq")
          .service("publish")
          .sendNotificationMessage({
            type: NOTIFICATION_TYPE.VERIFICATION_REJECT,
            user: { id: user.id },
            rejectionReason,
          });
      } else if (verificationStatus === VERIFICATION_STATUS.verified) {
        await strapi
          .plugin("mq")
          .service("publish")
          .sendNotificationMessage({
            type: NOTIFICATION_TYPE.VERIFICATION_PASSED,
            user: { id: user.id },
          });
      }
    }
    return {
      ok: true,
    };
  },

  async update(ctx) {
    const { id } = ctx.params;
    const { verificationStatus, rejectionReason, referralCode } =
      ctx.request.body;

    if (
      _.has(ctx.request.body, "verificationStatus") &&
      verificationStatus === VERIFICATION_STATUS.rejected &&
      !rejectionReason
    ) {
      throw new ValidationError();
    }

    let user = await getService("user").fetch(id);
    const updateData = {
      ..._.omit(ctx.request.body, [
        "email",
        "confirmed",
        "deleted",
        "confirmationToken",
        "resetPasswordToken",
        "provider",
        "id",
        "createdBy",
        "updatedBy",
        "role",
        "balance",
        "avgRating",
        "regRefCode",
        "verificationLogs",
      ]),
    };
    let lastestLog = null;
    if (verificationStatus !== user.verificationStatus) {
      lastestLog = await strapi.entityService.create(
        "api::verification-log.verification-log",
        {
          data: {
            status: verificationStatus,
            user: id,
            note:
              verificationStatus == VERIFICATION_STATUS.rejected
                ? rejectionReason
                : { en: "", vi: "" },
          },
        }
      );
      updateData.verificationLogs = {
        connect: [{ id: lastestLog.id }],
        disconnect: [],
      };
      updateData.isFirstTimeUpdateProfile = false;
      await saveRejectionReason(
        user.id,
        verificationStatus == VERIFICATION_STATUS.rejected
          ? rejectionReason
          : { en: "", vi: "" }
      );
    }
    user = await getService("user").editPopulateAll(id, updateData);

    user = await strapi
      .service("plugin::users-permissions.user")
      .checkDataIntegrity(user);
    if (lastestLog) {
      user.rejectionReason = lastestLog.note;
    }
    if (
      _.has(ctx.request.body, "referralCode") &&
      referralCode !== user.referralCode
    ) {
      //TODO
    }
    return user;
  },

  async changeLevel(ctx) {
    const { id } = ctx.params;
    const { level } = ctx.request.body;
    if (!level || ![1, 2, 3, 4, 5].includes(level)) {
      throw new ValidationError();
    }
    let user = await getService("user").fetch(id);
    if (!user) throw new ValidationError();

    user = await getService("user").edit(id, { level });
    return {
      level: user.level,
    };
  },

  async changePhoneVerifyStatus(ctx) {
    const { id } = ctx.params;
    const { phoneVerificationStatus } = ctx.request.body;
    if (
      !phoneVerificationStatus ||
      !["no", "pass", "done"].includes(phoneVerificationStatus)
    ) {
      throw new ValidationError();
    }
    let user = await getService("user").fetch(id);
    if (!user) throw new ValidationError();

    user = await getService("user").edit(id, { phoneVerificationStatus });
    return {
      phoneVerificationStatus: user.phoneVerificationStatus,
    };
  },

  async download(ctx) {
    let {
      username,
      university,
      verificationType,
      verificationStatus,
      startDate,
      endDate,
      level,
      gender,
      latestAccessTime,
    } = ctx.query;

    let filters = {};
    if (username) {
      filters = { ...filters, username: { $regex: username, $options: "i" } };
    }
    if (university) {
      filters = { ...filters, "university.id": Number(university) };
    }
    if (verificationType) {
      filters = { ...filters, verificationType };
    }
    if (verificationStatus) {
      filters = { ...filters, verificationStatus };
    }
    if (startDate) startDate = dayjs(startDate).startOf("day").valueOf();
    if (endDate) endDate = dayjs(endDate).endOf("day").valueOf();
    if (startDate && endDate) {
      filters = { ...filters, createdAt: { $gte: startDate, $lte: endDate } };
    } else if (startDate) {
      filters = { ...filters, createdAt: { $gte: startDate } };
    } else if (endDate) {
      filters = { ...filters, createdAt: { $lte: endDate } };
    }
    if (level) {
      filters = { ...filters, level };
    }
    if (gender) {
      filters = { ...filters, gender };
    }
    if (latestAccessTime) {
      filters = {
        ...filters,
        lastAccessAt: { $gte: dayjs(Number(latestAccessTime)).valueOf() },
      };
    }

    const results = await strapi
      .plugin("meedata")
      .service("users")
      .findAll(filters);
    const users = results.map((entry) => {
      return {
        ID: entry.id,
        USERNAME: entry.username,
        USERSTATUS: entry.deleted ? "del" : "active",
        LEVEL: entry.level,
        GENDER: entry.gender,
        NAME: entry.fullname,
        PHONE: entry.countryCode + entry.phone,
        UNIVERSITY: entry.university?.name,
        VERIFICATION: entry.verificationType,
        STATUS: entry.verificationStatus,
        BALANCE: entry.balance,
        TOTAL_EXPENSES: entry.totalExpenses,
        REFERRALCODE: entry.referralCode,
        REGISTER_REFCODE: entry.regRefCode,
        LATEST_ACCESS_TIME: entry.lastAccessAt,
        CREATED_TIME: entry.createdAt,
      };
    });
    const workbook = XLSX.utils.book_new();
    const worksheet1 = XLSX.utils.json_to_sheet(users);
    worksheet1["!cols"] = [
      { wch: 8 },
      { wch: 20 },
      { wch: 20 },
      { wch: 24 },
      { wch: 24 },
      { wch: 24 },
      { wch: 24 },
      { wch: 30 },
      { wch: 24 },
      { wch: 24 },
      { wch: 24 },
      { wch: 24 },
      { wch: 24 },
      { wch: 24 },
      { wch: 30 },
      { wch: 30 },
    ];
    XLSX.utils.book_append_sheet(workbook, worksheet1, "users");

    const excelData = XLSX.write(workbook, {
      type: "buffer",
      bookType: "xlsx",
    });
    const filename = `MeeTok-Users-${dayjs().format("YYYY-MM-DDHH:mm")}`;
    ctx.set(
      "Content-Type",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    ctx.set("Content-Disposition", `attachment; filename=${filename}.xlsx`);
    return excelData;
  },

  async delete(ctx) {
    const { id } = ctx.params;
    const user = await getService("user").fetch(id);
    if (!user) {
      throw new ValidationError();
    }
    const { reasons, note } = ctx.request.body;

    // 创建删除用户记录
    await strapi.entityService.create("api::deleted-user.deleted-user", {
      data: {
        user: user.id,
        username: user.username,
        email: user.email,
        provider: user.provider,
        reasons: reasons,
        note,
      },
    });

    // 更新用户信息
    const updatedUser = await strapi.entityService.update(
      "plugin::users-permissions.user",
      user.id,
      {
        data: {
          username: "Unknown",
          avatar: AVATAR.DEFAULT,
          email: `del_${user.id}@meetok.me`,
          deleted: true,
          visibility: false,
          deletedAt: new Date(),
        },
      }
    );

    return {
      success: true,
      message: "User deleted successfully",
      deletedUserId: user.id,
    };
  },

  async updateFinancialStatus(ctx) {
    const { id } = ctx.params;
    const { financialStatus } = ctx.request.body;

    // 验证财务状态是否有效，并定义对应的等级映射
    const statusLevelMapping = {
      risingStar: 0, // Rising Star (1억~5억)
      oneStar: 1, // 1스타 (5~10억)
      twoStar: 2, // 2스타 (10~30억)
      threeStar: 3, // 3스타 (30~100억)
      fourStar: 4, // 4스타 (100~300억)
      fiveStar: 5, // 5스타 (300~500억)
      sixStar: 6, // 6스타 (500~1000억)
      sevenStar: 7, // 7스타 (1000억~)
    };

    const validStatuses = Object.keys(statusLevelMapping);

    if (!financialStatus || !validStatuses.includes(financialStatus)) {
      throw new ValidationError("Invalid financial status");
    }

    const user = await getService("user").fetch(id, {
      populate: {
        asset_certification: {
          populate: {
            review_users: true,
            asset_verifications: true,
          },
        },
        asset_verifications: true,
      },
    });
    if (!user) {
      throw new ValidationError("User not found");
    }

    try {
      // 根据财务状态确定对应的等级
      const level = statusLevelMapping[financialStatus];

      // 准备更新数据
      const updateData = { financialStatus };
      if (level !== null) {
        updateData.level = level; // 只有当level不为null时才更新用户等级
      }

      const updatedUser = await getService("user").edit(id, updateData);

      // 如果设置了等级，创建或更新对应的asset certification记录
      if (level !== null) {
        const currentDate = new Date();
        const expiryDate = new Date();
        expiryDate.setFullYear(currentDate.getFullYear() + 1); // 设置过期时间为1年后

        // 获取当前登录的管理员用户ID
        const currentAdminUser = ctx.state.user;

        const certificationData = {
          issuedate: currentDate,
          expirydate: expiryDate,
          asset_result: financialStatus,
          owner: id,
        };

        // 处理 review_users 关系
        if (currentAdminUser && currentAdminUser.id) {
          if (
            user.asset_certification &&
            user.asset_certification.review_users
          ) {
            // 如果已存在certification记录，保留现有的review_users并添加当前用户
            const existingReviewUserIds =
              user.asset_certification.review_users.map((ru) => ru.id);
            if (!existingReviewUserIds.includes(currentAdminUser.id)) {
              certificationData.review_users = {
                connect: [
                  ...existingReviewUserIds.map((id) => ({ id })),
                  { id: currentAdminUser.id },
                ],
              };
            } else {
              certificationData.review_users = {
                connect: existingReviewUserIds.map((id) => ({ id })),
              };
            }
          } else {
            // 如果是新记录或没有review_users，设置当前用户
            certificationData.review_users = {
              connect: [{ id: currentAdminUser.id }],
            };
          }
        }

        // 处理 asset_verifications 关系
        if (user.asset_verifications && user.asset_verifications.length > 0) {
          certificationData.asset_verifications = {
            connect: user.asset_verifications.map((av) => ({ id: av.id })),
          };
        }

        if (user.asset_certification) {
          // 如果已存在certification记录，更新它
          await strapi.entityService.update(
            "api::asset-certification.asset-certification",
            user.asset_certification.id,
            {
              data: certificationData,
            }
          );
        } else {
          // 如果不存在certification记录，创建新的
          await strapi.entityService.create(
            "api::asset-certification.asset-certification",
            {
              data: certificationData,
            }
          );
        }
      }

      return {
        ok: true,
        message: "User financial status and level updated successfully",
        user: {
          id: updatedUser.id,
          financialStatus: updatedUser.financialStatus,
          level: updatedUser.level,
        },
      };
    } catch (error) {
      strapi.log.error("Error updating user financial status:", error);
      throw new ValidationError("Failed to update user financial status");
    }
  },

  async meProfile(ctx) {
    const user = ctx.state.user;
    const profile = await strapi.db
      .query("api::admin-profile.admin-profile")
      .findOne({
        where: {
          adminUser: user.id,
        },
        select: ["name", "position"],
        populate: {
          signature: { select: ["id", "url"] },
          avatar: { select: ["id", "url"] },
        },
      });
    return _.pick(profile || {}, ["name", "position", "signature", "avatar"]);
  },

  async updateMeProfile(ctx) {
    const user = ctx.state.user;
    const { name, position, signature, avatar } = ctx.request.body;
    const data = _.pick(ctx.request.body, [
      "name",
      "position",
      "signature",
      "avatar",
    ]);
    const profile = await strapi.db
      .query("api::admin-profile.admin-profile")
      .findOne({
        where: {
          adminUser: user.id,
        },
      });

    let result = {};
    if (!profile) {
      result = await strapi.entityService.create(
        "api::admin-profile.admin-profile",
        {
          data: data,
          populate: {
            signature: { fields: ["url"] },
            avatar: { fields: ["url"] },
          },
        }
      );
    } else {
      result = await strapi.entityService.update(
        "api::admin-profile.admin-profile",
        profile.id,
        {
          data: data,
          populate: {
            signature: { fields: ["url"] },
            avatar: { fields: ["url"] },
          },
        }
      );
    }
    return _.pick(result, ["name", "position", "signature", "avatar"]);
  },
});
