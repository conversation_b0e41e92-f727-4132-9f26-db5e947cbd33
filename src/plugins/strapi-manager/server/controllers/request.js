'use strict';

const dayjs = require('dayjs');

module.exports = {
    async find(ctx) {
        let { sendername, receivername, level, startDate, endDate, page = 1, pageSize = 20 } = ctx.query;
        let filters = {};
        if (sendername) {
            filters = { ...filters, sender: { username: { $contains: sendername } } }
        }
        if (receivername) {
            filters = { ...filters, receiver: { username: { $contains: receivername } } }
        }
        if (level) {
            filters = { ...filters, level };
        }
        if (startDate) startDate = dayjs(startDate).startOf('day').valueOf();
        if (endDate) endDate = dayjs(endDate).endOf('day').valueOf();
        if (startDate && endDate) {
            filters = { ...filters, createdAt: { $between: [startDate, endDate] } };
        } else if (startDate) {
            filters = { ...filters, createdAt: { $gte: startDate } };
        } else if (endDate) {
            filters = { ...filters, createdAt: { $lte: endDate } };
        }

        const { results, pagination } = await strapi.entityService.findPage('api::friendship-request.friendship-request', {
            filters,
            fields: ['level', 'createdAt'],
            sort: { id: 'desc' },
            populate: {
                sender: { fields: ['id', 'username', 'level'], populate: { avatar: { fields: ['name', 'url', 'ext', 'mime', 'size', 'formats'] } } },
                receiver: { fields: ['id', 'username', 'level'], populate: { avatar: { fields: ['name', 'url', 'ext', 'mime', 'size', 'formats'] } } },
            },
            page: page,
            pageSize: pageSize,
        });
        ctx.body = {
            results,
            pagination,
        };
    },
};