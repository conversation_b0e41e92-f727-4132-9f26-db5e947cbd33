"use strict";

module.exports = {
  /**
   * 获取所有 star schedule 列表
   * @param {*} ctx
   */
  async find(ctx) {
    try {
      // 获取查询参数
      const { query } = ctx;
      const { page = 1, pageSize = 10, ...filters } = query;

      // 使用findPage替代findMany以支持分页
      const { results, pagination } = await strapi.entityService.findPage(
        "api::star.star",
        {
          filters,
          sort: { playingDate: "desc" },
          populate: ["event", "previewImage"],
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      // 返回结果和分页信息
      ctx.body = {
        results,
        pagination,
      };
    } catch (error) {
      ctx.throw(500, error);
    }
  },

  /**
   * 根据 ID 获取单个 star schedule
   * @param {*} ctx
   */
  async findOne(ctx) {
    try {
      const { id } = ctx.params;

      const entry = await strapi.entityService.findOne("api::star.star", id, {
        populate: ["event", "previewImage"],
      });

      if (!entry) {
        return ctx.notFound("数据不存在");
      }

      return entry;
    } catch (error) {
      ctx.throw(500, error);
    }
  },

  /**
   * 创建新的 star schedule
   * @param {*} ctx
   */
  async create(ctx) {
    try {
      const { body } = ctx.request;

      // 验证必要字段
      if (!body.playingDate) {
        return ctx.badRequest("缺少必要字段: playingDate");
      }

      if (!body.eventId) {
        return ctx.badRequest("缺少必要字段: eventId");
      }

      // 检查 eventId 是否存在
      const event = await strapi.entityService.findOne(
        "api::star-event.star-event",
        body.eventId
      );
      if (!event) {
        return ctx.badRequest(`找不到对应的 event，ID: ${body.eventId}`);
      }

      // 格式化日期，确保格式正确
      let playingDate = body.playingDate;
      if (!(playingDate instanceof Date) && typeof playingDate === "string") {
        playingDate = new Date(playingDate);

        // 检查日期格式是否有效
        if (isNaN(playingDate.getTime())) {
          return ctx.badRequest(
            "无效的 playingDate 格式，请使用 YYYY-MM-DD HH:MM 或 ISO 格式"
          );
        }
      }

      // 创建数据，正确设置关联
      const entry = await strapi.entityService.create("api::star.star", {
        data: {
          playingDate: playingDate,
          event: body.eventId,
        },
      });

      return entry;
    } catch (error) {
      ctx.throw(500, error);
    }
  },

  /**
   * 更新 star schedule
   * @param {*} ctx
   */
  async update(ctx) {
    try {
      const { id } = ctx.params;
      const { body } = ctx.request;

      // 查找是否存在
      const exists = await strapi.entityService.findOne("api::star.star", id);
      if (!exists) {
        return ctx.notFound("数据不存在");
      }

      // 处理 eventId 转换为 event 关联
      const data = { ...body };
      if (data.eventId) {
        // 检查 eventId 是否存在
        const event = await strapi.entityService.findOne(
          "api::star-event.star-event",
          data.eventId
        );
        if (!event) {
          return ctx.badRequest(`找不到对应的 event，ID: ${data.eventId}`);
        }

        data.event = data.eventId;
        delete data.eventId;
      }

      // 格式化日期，如果有提供
      if (
        data.playingDate &&
        !(data.playingDate instanceof Date) &&
        typeof data.playingDate === "string"
      ) {
        const playingDate = new Date(data.playingDate);

        // 检查日期格式是否有效
        if (isNaN(playingDate.getTime())) {
          return ctx.badRequest(
            "无效的 playingDate 格式，请使用 YYYY-MM-DD HH:MM 或 ISO 格式"
          );
        }

        data.playingDate = playingDate;
      }

      // 更新数据
      const entry = await strapi.entityService.update("api::star.star", id, {
        data,
      });

      return entry;
    } catch (error) {
      ctx.throw(500, error);
    }
  },

  /**
   * 删除 star schedule
   * @param {*} ctx
   */
  async delete(ctx) {
    try {
      const { id } = ctx.params;

      // 查找是否存在
      const exists = await strapi.entityService.findOne("api::star.star", id);
      if (!exists) {
        return ctx.notFound("数据不存在");
      }

      // 删除数据
      const entry = await strapi.entityService.delete("api::star.star", id);

      return { id: entry.id };
    } catch (error) {
      ctx.throw(500, error);
    }
  },
};
