"use strict";

const user = require("./user");
const request = require("./request");
const match = require("./match");
const report = require("./report");
const config = require("./config");
const transaction = require("./transaction");
const version = require("./version");
const upgrade = require("./upgrade");
const recommendation = require("./recommendation");
const feedback = require("./feedback");
const sms = require("./sms");
const starEvent = require("./star-event");
const starSchedule = require("./star-schedule");
const benefits = require("./benefits");

module.exports = {
  user,
  request,
  match,
  report,
  transaction,
  config,
  version,
  upgrade,
  recommendation,
  feedback,
  sms,
  starEvent,
  starSchedule,
  benefits,
};
