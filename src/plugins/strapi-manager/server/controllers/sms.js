'use strict';
const dayjs = require('dayjs');

module.exports = {
    async find(ctx) {
        let { username, startDate, endDate, page = 1, pageSize = 20 } = ctx.query;
        let filters = {};
        if (username) {
            filters = { ...filters, user: { username: { $contains: username } } }
        }
        if (startDate) startDate = dayjs(startDate).startOf('day').valueOf();
        if (endDate) endDate = dayjs(endDate).endOf('day').valueOf();
        if (startDate && endDate) {
            filters = { ...filters, createdAt: { $between: [startDate, endDate] } };
        } else if (startDate) {
            filters = { ...filters, createdAt: { $gte: startDate } };
        } else if (endDate) {
            filters = { ...filters, createdAt: { $lte: endDate } };
        }

        const { results, pagination } = await strapi.entityService.findPage('api::verification.verification', {
            filters,
            sort: { id: 'desc' },
            populate: {
                user: { fields: ['id', 'username', 'level'], populate: { avatar: { fields: ['id', 'url'] } } },
            },
            page: page,
            pageSize: pageSize,
        });
        ctx.body = {
            results,
            pagination,
        };
    },
};