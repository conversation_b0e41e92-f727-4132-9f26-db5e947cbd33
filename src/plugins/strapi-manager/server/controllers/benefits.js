"use strict";

const dayjs = require("dayjs");
const utils = require("@strapi/utils");
const { ValidationError } = utils.errors;

module.exports = ({ strapi }) => ({
  // ==================== BenefitsBoard 相关方法 ====================

  /**
   * 获取所有 BenefitsBoard 列表
   * @param {*} ctx
   */
  async findBoards(ctx) {
    try {
      const {
        name,
        type,
        blocked,
        isDefault,
        userLevelLimited,
        page = 1,
        pageSize = 20,
      } = ctx.query;

      let filters = {};

      // 名称模糊搜索
      if (name) {
        filters = { ...filters, name: { $contains: name } };
      }

      // 类型筛选
      if (type) {
        filters = { ...filters, type };
      }

      // 是否被阻止
      if (blocked !== undefined) {
        filters = { ...filters, blocked: blocked === "true" };
      }

      // 是否为默认
      if (isDefault !== undefined) {
        filters = { ...filters, isDefault: isDefault === "true" };
      }

      // 用户等级限制
      if (userLevelLimited) {
        filters = { ...filters, userLevelLimited };
      }

      const { results, pagination } = await strapi.entityService.findPage(
        "plugin::benefits.board",
        {
          filters,
          sort: { order: "asc", id: "desc" },
          populate: {
            creator: {
              fields: ["id", "username", "email"],
            },
            subscribers: {
              fields: ["id", "username"],
            },
            coupons: {
              fields: ["id", "title"],
            },
          },
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      ctx.body = {
        results,
        pagination,
      };
    } catch (error) {
      strapi.log.error("Error finding benefits boards:", error);
      ctx.throw(500, error);
    }
  },

  /**
   * 根据 ID 获取单个 BenefitsBoard
   * @param {*} ctx
   */
  async findOneBoard(ctx) {
    try {
      const { id } = ctx.params;

      const board = await strapi.entityService.findOne(
        "plugin::benefits.board",
        id,
        {
          populate: {
            creator: {
              fields: ["id", "username", "email", "fullname"],
            },
            subscribers: {
              fields: ["id", "username", "email", "fullname"],
            },
            coupons: {
              fields: [
                "id",
                "title",
                "content",
                "pinned",
                "blocked",
                "removed",
                "likeCount",
                "createdAt",
              ],
              populate: {
                media: {
                  fields: [
                    "id",
                    "name",
                    "url",
                    "ext",
                    "mime",
                    "size",
                    "formats",
                  ],
                },
              },
            },
          },
        }
      );

      if (!board) {
        return ctx.notFound("Benefits board not found");
      }

      return board;
    } catch (error) {
      strapi.log.error("Error finding benefits board:", error);
      ctx.throw(500, error);
    }
  },

  /**
   * 创建新的 BenefitsBoard
   * @param {*} ctx
   */
  async createBoard(ctx) {
    try {
      const { body } = ctx.request;
      const user = ctx.state.user;

      // 验证必要字段
      if (!body.name) {
        throw new ValidationError("Board name is required");
      }

      if (!body.type || !["public", "private"].includes(body.type)) {
        throw new ValidationError(
          'Board type must be either "public" or "private"'
        );
      }

      // 准备创建数据
      const data = {
        ...body,
        creator: user?.id || null,
      };

      // 如果设置为默认板块，需要先取消其他默认板块
      if (data.isDefault) {
        await strapi.entityService.updateMany("plugin::benefits.board", {
          filters: { isDefault: true },
          data: { isDefault: false },
        });
      }

      const board = await strapi.entityService.create(
        "plugin::benefits.board",
        {
          data,
          populate: {
            creator: {
              fields: ["id", "username", "email"],
            },
          },
        }
      );

      return board;
    } catch (error) {
      strapi.log.error("Error creating benefits board:", error);
      if (error instanceof ValidationError) {
        ctx.throw(400, error.message);
      }
      ctx.throw(500, error);
    }
  },

  /**
   * 更新 BenefitsBoard
   * @param {*} ctx
   */
  async updateBoard(ctx) {
    try {
      const { id } = ctx.params;
      const { body } = ctx.request;

      // 检查板块是否存在
      const existingBoard = await strapi.entityService.findOne(
        "plugin::benefits.board",
        id
      );

      if (!existingBoard) {
        return ctx.notFound("Benefits board not found");
      }

      // 验证类型字段
      if (body.type && !["public", "private"].includes(body.type)) {
        throw new ValidationError(
          'Board type must be either "public" or "private"'
        );
      }

      // 如果设置为默认板块，需要先取消其他默认板块
      if (body.isDefault && !existingBoard.isDefault) {
        await strapi.entityService.updateMany("plugin::benefits.board", {
          filters: { isDefault: true },
          data: { isDefault: false },
        });
      }

      const updatedBoard = await strapi.entityService.update(
        "plugin::benefits.board",
        id,
        {
          data: body,
          populate: {
            creator: {
              fields: ["id", "username", "email"],
            },
            subscribers: {
              fields: ["id", "username"],
            },
          },
        }
      );

      return updatedBoard;
    } catch (error) {
      strapi.log.error("Error updating benefits board:", error);
      if (error instanceof ValidationError) {
        ctx.throw(400, error.message);
      }
      ctx.throw(500, error);
    }
  },

  /**
   * 删除 BenefitsBoard
   * @param {*} ctx
   */
  async deleteBoard(ctx) {
    try {
      const { id } = ctx.params;

      // 检查板块是否存在
      const existingBoard = await strapi.entityService.findOne(
        "plugin::benefits.board",
        id,
        {
          populate: {
            coupons: {
              fields: ["id"],
            },
          },
        }
      );

      if (!existingBoard) {
        return ctx.notFound("Benefits board not found");
      }

      // 检查是否有关联的优惠券
      if (existingBoard.coupons && existingBoard.coupons.length > 0) {
        throw new ValidationError(
          "Cannot delete board with existing coupons. Please remove all coupons first."
        );
      }

      // 删除板块
      const deletedBoard = await strapi.entityService.delete(
        "plugin::benefits.board",
        id
      );

      return {
        id: deletedBoard.id,
        message: "Benefits board deleted successfully",
      };
    } catch (error) {
      strapi.log.error("Error deleting benefits board:", error);
      if (error instanceof ValidationError) {
        ctx.throw(400, error.message);
      }
      ctx.throw(500, error);
    }
  },

  // ==================== BenefitsCoupon 相关方法 ====================

  /**
   * 获取所有 BenefitsCoupon 列表
   * @param {*} ctx
   */
  async findCoupons(ctx) {
    try {
      const {
        title,
        boardId,
        pinned,
        blocked,
        removed,
        level,
        phone,
        startDate,
        endDate,
        page = 1,
        pageSize = 20,
      } = ctx.query;

      let filters = { removed: false };

      // 标题模糊搜索
      if (title) {
        filters = { ...filters, title: { $contains: title } };
      }

      // 板块筛选
      if (boardId) {
        filters = { ...filters, board: { id: boardId } };
      }

      // 是否置顶
      if (pinned !== undefined) {
        filters = { ...filters, pinned: pinned === "true" };
      }

      // 是否被阻止
      if (blocked !== undefined) {
        filters = { ...filters, blocked: blocked === "true" };
      }

      // 是否被删除
      if (removed !== undefined) {
        filters = { ...filters, removed: removed === "true" };
      }

      // 等级筛选
      if (level) {
        filters = { ...filters, level: parseInt(level) };
      }

      // 手机号筛选
      if (phone) {
        filters = { ...filters, phone: { $contains: phone } };
      }

      // 日期筛选
      if (startDate) {
        const start = dayjs(startDate).startOf("day").valueOf();
        filters = { ...filters, createdAt: { $gte: start } };
      }
      if (endDate) {
        const end = dayjs(endDate).endOf("day").valueOf();
        filters = {
          ...filters,
          createdAt: { ...filters.createdAt, $lte: end },
        };
      }

      const { results, pagination } = await strapi.entityService.findPage(
        "plugin::benefits.coupon",
        {
          filters,
          sort: { pinned: "desc", createdAt: "desc" },
          populate: {
            board: {
              fields: ["id", "name", "type"],
            },
            media: {
              fields: ["id", "name", "url", "ext", "mime", "size", "formats"],
            },
            likers: {
              fields: ["id", "username"],
            },
          },
          page: parseInt(page),
          pageSize: parseInt(pageSize),
        }
      );

      ctx.body = {
        results,
        pagination,
      };
    } catch (error) {
      strapi.log.error("Error finding benefits coupons:", error);
      ctx.throw(500, error);
    }
  },

  /**
   * 根据 ID 获取单个 BenefitsCoupon
   * @param {*} ctx
   */
  async findOneCoupon(ctx) {
    try {
      const { id } = ctx.params;

      const coupon = await strapi.entityService.findOne(
        "plugin::benefits.coupon",
        id,
        {
          populate: {
            board: {
              fields: ["id", "name", "type", "description"],
            },
            media: {
              fields: ["id", "name", "url", "ext", "mime", "size", "formats"],
            },
            likers: {
              fields: ["id", "username", "email", "fullname"],
            },
          },
        }
      );

      if (!coupon) {
        return ctx.notFound("Benefits coupon not found");
      }

      return coupon;
    } catch (error) {
      strapi.log.error("Error finding benefits coupon:", error);
      ctx.throw(500, error);
    }
  },

  /**
   * 创建新的 BenefitsCoupon
   * @param {*} ctx
   */
  async createCoupon(ctx) {
    try {
      const { body } = ctx.request;

      // 验证必要字段
      if (!body.title) {
        throw new ValidationError("Coupon title is required");
      }

      if (!body.board) {
        throw new ValidationError("Board is required");
      }

      // 验证板块是否存在
      const board = await strapi.entityService.findOne(
        "plugin::benefits.board",
        body.board
      );

      if (!board) {
        throw new ValidationError("Board not found");
      }

      // 处理过期时间
      let expiresAt = body.expiresAt;
      if (expiresAt) {
        expiresAt = dayjs(expiresAt).toISOString();
      }

      const data = {
        ...body,
        expiresAt,
        likeCount: 0,
        pinned: body.pinned || false,
        blocked: body.blocked || false,
        removed: body.removed || false,
      };

      const coupon = await strapi.entityService.create(
        "plugin::benefits.coupon",
        {
          data,
          populate: {
            board: {
              fields: ["id", "name", "type"],
            },
            media: {
              fields: ["id", "name", "url", "ext", "mime", "size", "formats"],
            },
          },
        }
      );

      return coupon;
    } catch (error) {
      strapi.log.error("Error creating benefits coupon:", error);
      if (error instanceof ValidationError) {
        ctx.throw(400, error.message);
      }
      ctx.throw(500, error);
    }
  },

  /**
   * 更新 BenefitsCoupon
   * @param {*} ctx
   */
  async updateCoupon(ctx) {
    try {
      const { id } = ctx.params;
      const { body } = ctx.request;

      // 检查优惠券是否存在
      const existingCoupon = await strapi.entityService.findOne(
        "plugin::benefits.coupon",
        id
      );

      if (!existingCoupon) {
        return ctx.notFound("Benefits coupon not found");
      }

      // 如果更新了板块，验证板块是否存在
      if (body.board && body.board !== existingCoupon.board?.id) {
        const board = await strapi.entityService.findOne(
          "plugin::benefits.board",
          body.board
        );

        if (!board) {
          throw new ValidationError("Board not found");
        }
      }

      // 处理过期时间
      let updateData = { ...body };
      if (updateData.expiresAt) {
        updateData.expiresAt = dayjs(updateData.expiresAt).toISOString();
      }

      const updatedCoupon = await strapi.entityService.update(
        "plugin::benefits.coupon",
        id,
        {
          data: updateData,
          populate: {
            board: {
              fields: ["id", "name", "type"],
            },
            media: {
              fields: ["id", "name", "url", "ext", "mime", "size", "formats"],
            },
            likers: {
              fields: ["id", "username"],
            },
          },
        }
      );

      return updatedCoupon;
    } catch (error) {
      strapi.log.error("Error updating benefits coupon:", error);
      if (error instanceof ValidationError) {
        ctx.throw(400, error.message);
      }
      ctx.throw(500, error);
    }
  },

  /**
   * 删除 BenefitsCoupon
   * @param {*} ctx
   */
  async deleteCoupon(ctx) {
    try {
      const { id } = ctx.params;

      // 检查优惠券是否存在
      const existingCoupon = await strapi.entityService.findOne(
        "plugin::benefits.coupon",
        id
      );

      if (!existingCoupon) {
        return ctx.notFound("Benefits coupon not found");
      }

      // 删除优惠券
      const deletedCoupon = await strapi.entityService.delete(
        "plugin::benefits.coupon",
        id
      );

      return {
        id: deletedCoupon.id,
        message: "Benefits coupon deleted successfully",
      };
    } catch (error) {
      strapi.log.error("Error deleting benefits coupon:", error);
      ctx.throw(500, error);
    }
  },
});
