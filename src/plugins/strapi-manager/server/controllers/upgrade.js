'use strict';

const dayjs = require('dayjs');
const { addUserRecommendations, setTotalExpenses } = require("../../../../utils/redis");
const { addPostCommenter } = require('../../../strapi-community/server/utils/redis');


module.exports = {
    /**
     *  Synchronize users' latest data to im server.
     * @param {*} ctx 
     */
    async syncImUsers(ctx) {
        await strapi.plugin('openim').service('user').importUsers();
        return { ok: true }
    },

    /**
     * Synchronize users' latest data to mongodb.
     * @param {*} ctx 
     */
    async syncUsers(ctx) {
        await strapi.plugin('meedata').service('users').importUsers();
        return { ok: true }
    },

    /**
     * Cache all recommended users in redis.
     * @param {*} ctx 
     * @returns 
     */
    async syncUserRecommendations(ctx) {
        const entries = await strapi.entityService.findMany('api::recommendation.recommendation', {
            populate: {
                recommender: {
                    fields: ['id']
                },
                recommended: {
                    fields: ['id']
                }
            },
        });
        entries.forEach(async item => {
            if (item.recommender && item.recommended) {
                const scores = [dayjs(item.createdAt).valueOf(), item.recommender.id];
                await addUserRecommendations(item.recommended.id, scores);
            }
        })
        return { ok: true }
    },

    async syncDailyRecommendations(ctx) {
        const result = await strapi.db.connection.context.raw("SELECT link.user_id as userId, r.sequence, count(r.id) as count FROM recommendations r INNER JOIN recommendations_recommended_links link on r.id = link.recommendation_id WHERE r.type = 'standard' GROUP BY link.user_id, r.sequence")
        const logs = result[0];
        const keyValuePairs = logs.map(log => [`RECOMMENDDATION_USED_COUNT:${log.sequence}:${log.userId}`, Number(log.count)]).flatMap(([key, value]) => [key, value]);
        await strapi.redis.connections.default.client.mset(...keyValuePairs);

        return {
            count: logs.length
        };
    },

    async syncUsersCount(ctx) {
        await strapi.plugin('community').service('user').syncUserPostCount();
        // await strapi.plugin('community').service('user').syncUserCommentCount();
        await strapi.plugin('community').service('user').syncUserLikeCount();
        return {
            ok: true
        }
    },

    /**
     * Merge University and Graduate boards. 
        We realized that it's hard to distinguish the users from graduated or not. 
        So just make board for each univ and gradu or undergradu can use it. 
        Display the board name as univ name.
     * @param {*} ctx 
     */
    async mergeUniversityBoards(ctx) {
        await strapi.plugin('community').service('board').mergeUniversityBoards();
        return {
            ok: true
        }
    },

    async syncExpenseLogs(ctx) {
        const sql = 'SELECT l.user_id, sum(amount) as expenses FROM transactions t LEFT JOIN transactions_customer_links l on t.id = l.transaction_id WHERE t.amount < 0 GROUP BY l.user_id';
        const result = await strapi.db.connection.context.raw(sql);
        const logs = result[0];
        logs.forEach(async log => {
            const totalExpenses = Math.abs(Number(log.expenses));
            await setTotalExpenses(log.user_id, totalExpenses);
            await strapi.entityService.update('plugin::users-permissions.user', log.user_id, {
                data: { totalExpenses }
            });
        })
        return {
            ok: true
        }
    },

    async synCommenters(ctx) {
        const sql = "SELECT link2.post_id, link1.comment_id, link1.user_id FROM comm_comments_author_user_links link1 INNER JOIN comm_comments_post_links link2 on link1.comment_id = link2.comment_id";
        const [result] = await strapi.db.connection.context.raw(sql);
        const totalCount = result.length;
        if (totalCount == 0) return { totalCount };
        let offset = 0;
        let updatedCount = 0;
        const batchSize = 100;
        while (updatedCount < totalCount) {
            const remainingCount = totalCount - updatedCount;
            const currentBatchSize = Math.min(batchSize, remainingCount);
            const batchToUpdate = result.slice(offset, offset + currentBatchSize);
            await Promise.all(batchToUpdate.map(async el => {
                await addPostCommenter(el.post_id, el.user_id);
            }))
            updatedCount += currentBatchSize;
            offset += currentBatchSize;
            console.log(`sync post commenters: ${updatedCount}/${totalCount}`);
        }
        return {
            totalCount
        }
    },

    async synPostAuthor(ctx) {
        const sql = "SELECT post_id, user_id FROM comm_posts_author_user_links";
        const [result] = await strapi.db.connection.context.raw(sql);
        const totalCount = result.length;
        if (totalCount == 0) return { totalCount };
        let offset = 0;
        let updatedCount = 0;
        const batchSize = 100;
        while (updatedCount < totalCount) {
            const remainingCount = totalCount - updatedCount;
            const currentBatchSize = Math.min(batchSize, remainingCount);
            const batchToUpdate = result.slice(offset, offset + currentBatchSize);
            await Promise.all(batchToUpdate.map(async el => {
                await addPostCommenter(el.post_id, el.user_id);
            }))
            updatedCount += currentBatchSize;
            offset += currentBatchSize;
            console.log(`sync post author: ${updatedCount}/${totalCount}`);
        }
        return {
            totalCount
        }
    },

    async synBoards(ctx) {
        return await strapi.plugin('community').service('board').initBoards();
    }
};