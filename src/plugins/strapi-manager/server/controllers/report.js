'use strict';

const dayjs = require('dayjs');
const utils = require('@strapi/utils');
const { getService } = require('../utils');
const { ApplicationError, ValidationError, NotFoundError } = utils.errors;
const { REPORT_ACTION, REPORT_ACTION_LIST } = require('../../../../constants')

module.exports = {
    async find(ctx) {
        const { page = 1, pageSize = 20, username, action, processed, startDate, endDate } = ctx.query;
        let filters = {};
        if (username) {
            filters = { ...filters, reported: { username: username } };
        }
        if (action) {
            filters = { ...filters, action };
        }
        if (processed == 1) {
            filters = { ...filters, processedAt: { $notNull: true } };
        } else if (processed == 0) {
            filters = { ...filters, processedAt: { $null: true } };
        }
        if (startDate) startDate = dayjs(startDate).startOf('day').valueOf();
        if (endDate) endDate = dayjs(endDate).endOf('day').valueOf();
        if (startDate && endDate) {
            filters = { ...filters, createdAt: { $between: [startDate, endDate] } };
        } else if (startDate) {
            filters = { ...filters, createdAt: { $gte: startDate } };
        } else if (endDate) {
            filters = { ...filters, createdAt: { $lte: endDate } };
        }

        const params = {
            sort: { id: 'desc' },
            populate: {
                reporter: {
                    fields: ['id', 'username', 'blocked', 'deleted']
                },
                reported: {
                    fields: ['id', 'username', 'blocked', 'deleted']
                },
                attachments: { fields: ['name', 'url', 'ext', 'mime', 'size', 'formats'] }
            },
            filters: filters,
            page: page,
            pageSize: pageSize,
        };
        const { results, pagination } = await strapi.entityService.findPage('api::report.report', params);
        ctx.body = {
            results,
            pagination,
        };
    },

    async findOne(ctx) {
        const { id } = ctx.params;
        return await strapi.entityService.findOne('api::report.report', id, {
            populate: {
                reporter: {
                    fields: ['id', 'username', 'blocked', 'deleted']
                },
                reported: {
                    fields: ['id', 'username', 'blocked', 'deleted']
                },
                attachments: { fields: ['name', 'url', 'ext', 'mime', 'size', 'formats'] }
            },
        });
    },

    async process(ctx) {
        const { id } = ctx.params;
        const { note, action, tempBanDuration = 0 } = ctx.request.body;
        if (!REPORT_ACTION_LIST.includes(action))
            throw new ValidationError();
        if (action === REPORT_ACTION.TEMP_BAN && tempBanDuration == 0)
            throw new ValidationError();

        const entry = await strapi.entityService.findOne('api::report.report', id, {
            populate: {
                reported: true
            }
        });
        if (!entry)
            throw NotFoundError();

        if (entry.processedAt)
            throw new ApplicationError('This report has been processed.');

        await strapi.entityService.update('api::report.report', id, {
            data: {
                processedAt: new Date(),
                action,
                tempBanDuration: action === REPORT_ACTION.TEMP_BAN ? tempBanDuration : 0,
                note
            },
        });
        if (entry.reported) {
            await getService('report').process(entry.reported, action, tempBanDuration);
        }
        return { ok: true }
    }
};