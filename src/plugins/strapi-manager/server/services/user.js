// @ts-nocheck
'use strict';

/**
 * User.js service
 *
 * @description: A set of functions similar to controller's actions to avoid code duplication.
 */

module.exports = ({ strapi }) => ({

    async edit(userId, params = {}) {
        return await strapi.entityService.update('plugin::users-permissions.user', userId, {
            data: params
        });
    },

    async editPopulateAll(userId, params = {}) {
        return strapi.entityService.update(
          "plugin::users-permissions.user",
          userId,
          {
            data: params,
            populate: {
              avatar: { fields: ["url"] },
              university: { fields: ["name"] },
              photos: { fields: ["url"] },
              interests: { fields: ["name"] },
              asset_verifications: { fields: ["type", "owner", "proof"] },
              idcard: { fields: ["url", "formats"] },
              asset_certification: {
                fields: [
                  "issuedate",
                  "expirydate",
                  "asset_result",
                  "review_users",
                ],
              },
            },
          }
        );
    },

    async fetch(id, params) {
        return await strapi.entityService.findOne('plugin::users-permissions.user', id, params);
    },

    async pageAll(params) {
        return await strapi.entityService.findPage('plugin::users-permissions.user', params);
    }
});
