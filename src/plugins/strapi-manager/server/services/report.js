'use strict';
const { REPORT_ACTION, BLOCK_TYPE } = require('../../../../constants');
const dayjs = require('dayjs');

module.exports = {
    async process(targetUser, action, banDuration) {
        let data = null;
        if (action === REPORT_ACTION.WARNING) {
            if (!targetUser.blocked) {
                //send kakaomessage
            }
        } else if (action === REPORT_ACTION.TEMP_BAN) {
            //the stopped days will be accumulated
            if (!targetUser.blocked || targetUser.blockedReason === BLOCK_TYPE.TEMP_BAN) {
                let tempBanDuration = targetUser.tempBanDuration || 0;
                tempBanDuration += banDuration;
                const banStartedAt = targetUser.banStartedAt || new Date();
                data = {
                    blocked: true,
                    blockedReason: REPORT_ACTION.TEMP_BAN,
                    banStartedAt,
                    tempBanDuration
                }
            }
        } else if (action === REPORT_ACTION.PERMA_BAN) {
            if (![BLOCK_TYPE.DELETE, BLOCK_TYPE.PERMA_BAN].includes(targetUser.blockedReason)) {
                data = {
                    blocked: true,
                    blockedReason: BLOCK_TYPE.PERMA_BAN,
                    banStartedAt: null,
                    tempBanDuration: 0
                }
            }
        }

        if (!data)
            return;
        await strapi.entityService.update('plugin::users-permissions.user', targetUser.id, {
            data: data
        });

        //set automatic unblocking(MQ)
        if (action != BLOCK_TYPE.TEMP_BAN)
            return;
        const message = {
            id: targetUser.id,
            ...data
        }
        const delay = dayjs(data.banStartedAt).add(data.tempBanDuration, 'day').valueOf() - dayjs().valueOf();
        await strapi.plugin('mq').service('publish').pushlishUnBan(message, delay);
    }
};