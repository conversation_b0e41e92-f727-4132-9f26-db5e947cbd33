'use strict';

module.exports = [
    {
        method: 'POST',
        path: '/upgrade/sync-im-users',
        handler: 'upgrade.syncImUsers'
    },
    {
        method: 'POST',
        path: '/upgrade/sync-users',
        handler: 'upgrade.syncUsers'
    },
    {
        method: 'POST',
        path: '/upgrade/sync-user-recommendations',
        handler: 'upgrade.syncUserRecommendations'
    },

    {
        method: 'POST',
        path: '/upgrade/sync-daily-recommendations',
        handler: 'upgrade.syncDailyRecommendations'
    },
    {
        method: 'POST',
        path: '/upgrade/sync-users-count',
        handler: 'upgrade.syncUsersCount'
    },
    {
        method: 'POST',
        path: '/upgrade/merge-university-boards',
        handler: 'upgrade.mergeUniversityBoards'
    },
    {
        method: 'POST',
        path: '/upgrade/sync-expenses',
        handler: 'upgrade.syncExpenseLogs'
    },
    {
        method: 'POST',
        path: '/upgrade/sync-commenters',
        handler: 'upgrade.synCommenters'
    },
    {
        method: 'POST',
        path: '/upgrade/sync-postauthor',
        handler: 'upgrade.synPostAuthor'
    },
    {
        method: 'POST',
        path: '/upgrade/sync-boards',
        handler: 'upgrade.synBoards'
    }
];