"use strict";

module.exports = [
  {
    method: "GET",
    path: "/users",
    handler: "user.find",
  },
  {
    method: "GET",
    path: "/users/:id",
    handler: "user.findOne",
  },
  {
    method: "PUT",
    path: "/users/:id",
    handler: "user.update",
  },
  {
    method: "POST",
    path: "/users/:id/verification",
    handler: "user.verification",
  },
  {
    method: "POST",
    path: "/users/:id/level",
    handler: "user.changeLevel",
  },
  {
    method: "POST",
    path: "/users/:id/phone_verify_status",
    handler: "user.changePhoneVerifyStatus",
  },
  {
    method: "GET",
    path: "/users/search/download",
    handler: "user.download",
  },
  {
    method: "DELETE",
    path: "/users/:id",
    handler: "user.delete",
  },
  {
    method: "PUT",
    path: "/users/:id/financial-status",
    handler: "user.updateFinancialStatus",
  },
  {
    method: "GET",
    path: "/users/me/profile",
    handler: "user.meProfile",
  },
  {
    method: "POST",
    path: "/users/me/profile",
    handler: "user.updateMeProfile",
  },
];
