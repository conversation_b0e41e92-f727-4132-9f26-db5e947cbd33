"use strict";

const userRoutes = require("./user");
const requestRoutes = require("./request");
const matchRoutes = require("./match");
const reportRoutes = require("./report");
const configRoutes = require("./config");
const transactionRoutes = require("./transaction");
const settingsRoutes = require("./setting");
const upgradeRoutes = require("./upgrade");
const recommendationRoutes = require("./recommendation");
const feedbackRoutes = require("./feedback");
const smsRoutes = require("./sms");
const starEventRoutes = require("./star-event");
const starScheduleRoutes = require("./star-schedule");
const benefitsRoutes = require("./benefits");

module.exports = {
  type: "admin",
  routes: [
    ...userRoutes,
    ...requestRoutes,
    ...matchRoutes,
    ...reportRoutes,
    ...configRoutes,
    ...transactionRoutes,
    ...settingsRoutes,
    ...upgradeRoutes,
    ...recommendationRoutes,
    ...feedbackRoutes,
    ...smsRoutes,
    ...starEventRoutes,
    ...starScheduleRoutes,
    ...benefitsRoutes,
  ],
};
