"use strict";

module.exports = [
  {
    method: "GET",
    path: "/star-events",
    handler: "starEvent.find",
  },
  {
    method: "GET",
    path: "/star-events/:id",
    handler: "starEvent.findOne",
  },
  {
    method: "POST",
    path: "/star-events",
    handler: "starEvent.create",
  },
  {
    method: "PUT",
    path: "/star-events/:id",
    handler: "starEvent.update",
  },
  {
    method: "DELETE",
    path: "/star-events/:id",
    handler: "starEvent.delete",
  },
  {
    method: "GET",
    path: "/star-events/sections",
    handler: "starEvent.getEventSections",
  },
  {
    method: "GET",
    path: "/star-events/list",
    handler: "starEvent.getEvents",
  },
  {
    method: "GET",
    path: "/star-events/my-past",
    handler: "starEvent.getMyPastEvents",
  },
  {
    method: "POST",
    path: "/star-events/:id/join",
    handler: "starEvent.joinEvent",
  },
  {
    method: "POST",
    path: "/star-events/:id/approve",
    handler: "starEvent.approveJoinRequest",
  },
  {
    method: "POST",
    path: "/star-events/:id/reject",
    handler: "starEvent.rejectJoinRequest",
  },
  {
    method: "GET",
    path: "/star-events/:id/members",
    handler: "starEvent.getEventMembers",
  },
];
