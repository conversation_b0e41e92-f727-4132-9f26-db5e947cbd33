'use strict';

module.exports = [
  // BenefitsBoard 相关路由
  {
    method: 'GET',
    path: '/benefits/boards',
    handler: 'benefits.findBoards'
  },
  {
    method: 'GET',
    path: '/benefits/boards/:id',
    handler: 'benefits.findOneBoard'
  },
  {
    method: 'POST',
    path: '/benefits/boards',
    handler: 'benefits.createBoard'
  },
  {
    method: 'PUT',
    path: '/benefits/boards/:id',
    handler: 'benefits.updateBoard'
  },
  {
    method: 'DELETE',
    path: '/benefits/boards/:id',
    handler: 'benefits.deleteBoard'
  },
  
  // BenefitsCoupon 相关路由
  {
    method: 'GET',
    path: '/benefits/coupons',
    handler: 'benefits.findCoupons'
  },
  {
    method: 'GET',
    path: '/benefits/coupons/:id',
    handler: 'benefits.findOneCoupon'
  },
  {
    method: 'POST',
    path: '/benefits/coupons',
    handler: 'benefits.createCoupon'
  },
  {
    method: 'PUT',
    path: '/benefits/coupons/:id',
    handler: 'benefits.updateCoupon'
  },
  {
    method: 'DELETE',
    path: '/benefits/coupons/:id',
    handler: 'benefits.deleteCoupon'
  }
];
