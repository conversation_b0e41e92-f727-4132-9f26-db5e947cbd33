import React, { useEffect, useReducer, useRef } from 'react';
import { Helmet } from 'react-helmet';
import { useIntl } from 'react-intl';
import {
    LoadingIndicatorPage,
    useFocusWhenNavigate,
    useNotification,
    useOverlayBlocker,
} from '@strapi/helper-plugin';
import Check from '@strapi/icons/Check';
import { Box } from '@strapi/design-system/Box';
import { Flex } from '@strapi/design-system/Flex';
import { ToggleInput } from '@strapi/design-system/ToggleInput';
import { Typography } from '@strapi/design-system/Typography';
import { Button } from '@strapi/design-system/Button';
import { Main } from '@strapi/design-system/Main';
import { Stack } from '@strapi/design-system/Stack';
import { Grid, GridItem } from '@strapi/design-system/Grid';
import { ContentLayout, HeaderLayout, Layout } from '@strapi/design-system/Layout';
import { TextInput } from '@strapi/design-system';
import axios from 'axios';
import isEqual from 'lodash/isEqual';
import { axiosInstance, getRequestUrl, getTrad } from '../../utils';
import init from './init';
import reducer, { initialState } from './reducer';

export const SettingsPage = () => {
    const { formatMessage } = useIntl();
    const { lockApp, unlockApp } = useOverlayBlocker();
    const toggleNotification = useNotification();

    useFocusWhenNavigate();

    const [{ initialData, isLoading, isSubmiting, modifiedData }, dispatch] = useReducer(
        reducer,
        initialState,
        init
    );

    const isMounted = useRef(true);

    useEffect(() => {
        const CancelToken = axios.CancelToken;
        const source = CancelToken.source();

        const getData = async () => {
            try {
                const res = await axiosInstance.get(getRequestUrl('settings/version'), {
                    cancelToken: source.token,
                });
                dispatch({
                    type: 'GET_DATA_SUCCEEDED',
                    data: res.data,
                });
            } catch (err) {
                console.error(err);
            }
        };

        if (isMounted.current) {
            getData();
        }

        return () => {
            source.cancel('Operation canceled by the user.');
            isMounted.current = false;
        };
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const isSaveButtonDisabled = isEqual(initialData, modifiedData);

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (isSaveButtonDisabled) {
            return;
        }

        lockApp();

        dispatch({ type: 'ON_SUBMIT' });

        try {
            await axiosInstance.put(getRequestUrl('settings/version'), modifiedData);

            dispatch({
                type: 'SUBMIT_SUCCEEDED',
            });

            toggleNotification({
                type: 'success',
                message: { id: 'notification.form.success.fields' },
            });
        } catch (err) {
            console.error(err);

            dispatch({ type: 'ON_SUBMIT_ERROR' });
        }

        unlockApp();
    };

    const handleChange = ({ target: { name, value } }) => {
        dispatch({
            type: 'ON_CHANGE',
            keys: name,
            value,
        });
    };

    const getError = (version) => {
        if (!version) {
            return "This is a required field.";
        }
        const pattern = /^(\d+)\.(\d+)\.(\d+)$/;
        if (!pattern.test(version)) {
            return "This is an invalid version.";
        }
        return null;
    };

    return (
        <Main tabIndex={-1}>
            <form onSubmit={handleSubmit}>
                <HeaderLayout
                    title="App Version"
                    primaryAction={
                        <Button
                            disabled={isSaveButtonDisabled}
                            data-testid="save-button"
                            loading={isSubmiting}
                            type="submit"
                            startIcon={<Check />}
                            size="S"
                        >
                            {formatMessage({
                                id: 'global.save',
                                defaultMessage: 'Save',
                            })}
                        </Button>
                    }
                    subtitle="Configure the settings for the app version"
                />
                <ContentLayout>
                    {isLoading ? (
                        <LoadingIndicatorPage />
                    ) : (
                        <Layout>
                            <Stack spacing={12}>
                                <Flex direction="column" alignItems="stretch" gap={7}>
                                    <Box
                                        background="neutral0"
                                        paddingTop={6}
                                        paddingBottom={6}
                                        paddingLeft={7}
                                        paddingRight={7}
                                        shadow="filterShadow"
                                        hasRadius>
                                        <Stack spacing={4}>
                                            <Flex>
                                                <Typography variant="delta" as="h2">
                                                    Android:
                                                </Typography>
                                            </Flex>
                                            <Grid gap={5}>
                                                <GridItem col={6} s={12}>
                                                    <TextInput
                                                        id="test-address-input"
                                                        name="test-address"
                                                        label="Latest Version"
                                                        placeholder="1.0.0"
                                                        required
                                                        value={modifiedData.android.latestVersion}
                                                        error={getError(modifiedData.android.latestVersion)}
                                                        onChange={(e) => {
                                                            handleChange({
                                                                target: { name: 'android.latestVersion', value: e.target.value },
                                                            });
                                                        }}
                                                    />
                                                </GridItem>
                                                <GridItem col={6} s={12}>
                                                    <TextInput
                                                        id="test-address-input"
                                                        name="test-address"
                                                        label="Force Update Version"
                                                        placeholder="1.0.0"
                                                        required
                                                        value={modifiedData.android.forceUpdateVersion}
                                                        error={getError(modifiedData.android.forceUpdateVersion)}
                                                        onChange={(e) => {
                                                            handleChange({
                                                                target: { name: 'android.forceUpdateVersion', value: e.target.value },
                                                            });
                                                        }}
                                                    />
                                                </GridItem>
                                                <GridItem col={6} s={12}>
                                                    <TextInput
                                                        id="test-address-input"
                                                        name="test-address"
                                                        label="Job Service Version"
                                                        placeholder="1.0.0"
                                                        required
                                                        value={modifiedData.android.jobServiceVersion}
                                                        error={getError(modifiedData.android.jobServiceVersion)}
                                                        onChange={(e) => {
                                                            handleChange({
                                                                target: { name: 'android.jobServiceVersion', value: e.target.value },
                                                            });
                                                        }}
                                                    />
                                                </GridItem>
                                            </Grid>
                                        </Stack>
                                    </Box>

                                    <Box
                                        background="neutral0"
                                        paddingTop={6}
                                        paddingBottom={6}
                                        paddingLeft={7}
                                        paddingRight={7}
                                        shadow="filterShadow"
                                        hasRadius>
                                        <Stack spacing={4}>
                                            <Flex>
                                                <Typography variant="delta" as="h2">
                                                    IOS:
                                                </Typography>
                                            </Flex>
                                            <Grid gap={5}>
                                                <GridItem col={6} s={12}>
                                                    <TextInput
                                                        id="test-address-input"
                                                        name="test-address"
                                                        label="Latest Version"
                                                        placeholder="1.0.0"
                                                        required
                                                        value={modifiedData.ios.latestVersion}
                                                        error={getError(modifiedData.ios.latestVersion)}
                                                        onChange={(e) => {
                                                            handleChange({
                                                                target: { name: 'ios.latestVersion', value: e.target.value },
                                                            });
                                                        }}
                                                    />
                                                </GridItem>
                                                <GridItem col={6} s={12}>
                                                    <TextInput
                                                        id="test-address-input"
                                                        name="test-address"
                                                        label="Force Update Version"
                                                        placeholder="1.0.0"
                                                        required
                                                        value={modifiedData.ios.forceUpdateVersion}
                                                        error={getError(modifiedData.ios.forceUpdateVersion)}
                                                        onChange={(e) => {
                                                            handleChange({
                                                                target: { name: 'ios.forceUpdateVersion', value: e.target.value },
                                                            });
                                                        }}
                                                    />
                                                </GridItem>
                                                <GridItem col={6} s={12}>
                                                    <TextInput
                                                        id="test-address-input"
                                                        name="test-address"
                                                        label="Job Service Version"
                                                        placeholder="1.0.0"
                                                        required
                                                        value={modifiedData.ios.jobServiceVersion}
                                                        error={getError(modifiedData.ios.jobServiceVersion)}
                                                        onChange={(e) => {
                                                            handleChange({
                                                                target: { name: 'ios.jobServiceVersion', value: e.target.value },
                                                            });
                                                        }}
                                                    />
                                                </GridItem>
                                            </Grid>
                                        </Stack>
                                    </Box>
                                </Flex>
                            </Stack>
                        </Layout>
                    )}
                </ContentLayout>
            </form>
        </Main >
    );
};

const VersionSettingsPage = () => (
    <SettingsPage />
);

export default VersionSettingsPage;
