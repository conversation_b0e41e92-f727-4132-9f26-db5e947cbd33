'use strict';
const { getPluginService } = require('../utils/getPluginService');
const dayjs = require('dayjs');

module.exports = {
	registerCronTasks: ({ strapi }) => {
		// create cron check
		strapi.cron.add({
			'0 0 0/1 * * ?': async ({ strapi }) => {
				const curHour = dayjs().hour();

				// Notifications will be made every 8:00 am when there's more than one matched friend and they haven't started chat yet.
				if (curHour === 8) {
					const lockMatches = await strapi.redis.connections.default.client.set('lock:stat_unconnect_matches', 1, 'ex', 120, 'nx');
					if (lockMatches === 'OK') {
						getPluginService(strapi, 'action').statUnConnectedMatches();
					}
				}

				if (curHour === 9) {
					const lockMatches = await strapi.redis.connections.default.client.set('lock:stat_high_eval', 1, 'ex', 120, 'nx');
					if (lockMatches === 'OK') {
						getPluginService(strapi, 'action').statHighEvals();
					}
				}

				// Notifications will be made when there're more than 2 Given Cards available
				if (curHour === 12) {
					const lockUnAccess = await strapi.redis.connections.default.client.set('lock:stat_unaccess_users', 1, 'ex', 120, 'nx');
					if (lockUnAccess === 'OK') {
						getPluginService(strapi, 'action').statUnAccessUsers();
					}
				}

				// Notifications will be made when there're more than 2 Given Cards available
				if (curHour === 20) {
					const lockUnViewGiven = await strapi.redis.connections.default.client.set('lock:stat_unview_given', 1, 'ex', 120, 'nx');
					if (lockUnViewGiven === 'OK') {
						getPluginService(strapi, 'action').statUnViewGivenCards();
					}
				}

				if (curHour === 13) {
					const lockUnreadMessage = await strapi.redis.connections.default.client.set('lock:stat_unread_message', 1, 'ex', 120, 'nx');
					if (lockUnreadMessage === 'OK') {
						await getPluginService(strapi, 'action').statUnReadUsers();
					}
				}
				//Once the card is delivered, a push message must arrive at every event so that the user can know it.
				const dailyGivenRules = await strapi.service('api::setting.setting').getDailyGivenRules();
				if (dailyGivenRules.includes(curHour)) {
					const lockDailyGiven = await strapi.redis.connections.default.client.set('lock:noti_daily_given', 1, 'ex', 120, 'nx');
					if (lockDailyGiven === 'OK') {
						getPluginService(strapi, 'action').sendDailyGiven();
					}
				}

			}
		});
	},
};
