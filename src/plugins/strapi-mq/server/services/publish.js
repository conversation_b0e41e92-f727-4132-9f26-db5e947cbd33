'use strict';
const { QUEUES, MSG_TYPE } = require('../constants');
const { HEART_LEVEL, REQUEST_STATUS } = require('../../../../constants')
const { NOTIFICATION_TYPE } = require('../../../strapi-notification/server/constants')
const { getHeartReminderCount, incrHeartReminderCount } = require('../../../../utils/redis')
const dayjs = require("dayjs");

module.exports = ({ strapi }) => ({

    /**
     * 1.Send a push to the recipient immediately
     * 2.If doubleHeart does not match after 7 days return part of the amount
     * 3.check after 24 hours whether the match is successful, if no further reminders
     * 4.After 7 days, if there is no match, a push is sent to the sender.
     * sender,receiver,relatedId
     */
    async sendGotHeartMessage(data) {
        const { sender, receiver } = data;
        await this.sendNotificationMessage({
            type: NOTIFICATION_TYPE.GOT_HEART,
            user: { id: receiver },
            senderId: sender
        });
        await this.sendHeartExpiredMessage(data);
        await this.sendHeartReminderMessage(data);
    },

    async sendHeartExpiredMessage(data) {
        const { level } = data;
        if (!level) return;
        try {
            const delay = 1000 * 60 * 60 * 24 * 7;

            const msg = {
                type: MSG_TYPE.HEART_EXPIRED,
                data
            }
            strapi.mq.channels.heart.publish("delay-exchange", QUEUES.delayedQueue, Buffer.from(JSON.stringify(msg)), { headers: { "x-delay": delay } });
        } catch (error) {
            console.log(error);
        }
    },

    async sendHeartReminderMessage(data) {
        try {
            const { sender, receiver, relatedId } = data;
            if (!relatedId) return;

            const isMatched = await strapi.plugin('meedata').service('friendships').isMatched(sender, receiver);
            if (isMatched) return;

            const request = await strapi.plugin('meedata').service('requests').findOneByRelatedId(Number(relatedId));
            if (!request || request.handleResult != REQUEST_STATUS.PENDING) return;

            const count = await getHeartReminderCount(relatedId);
            if (count > 6) return;

            const delay = 1000 * 60 * 60 * 24;
            const msg = {
                type: MSG_TYPE.HEART_REMINDER,
                data
            }
            strapi.mq.channels.heart.publish("delay-exchange", QUEUES.delayedQueue, Buffer.from(JSON.stringify(msg)), { headers: { "x-delay": delay } });
            await incrHeartReminderCount(relatedId)
        } catch (error) {
            console.log(error);
        }
    },

    async sendRegBonusMessage(msg) {
        try {
            strapi.mq.channels.bonus.sendToQueue(QUEUES.registrationBonus, Buffer.from(JSON.stringify(msg)), { persistent: true });
        } catch (error) {
            console.log(error);
        }
    },

    async sendNotificationMessage(msg) {
        try {
            strapi.mq.channels.push.sendToQueue(QUEUES.pushNotification, Buffer.from(JSON.stringify(msg)), { persistent: true });
        } catch (error) {
            console.log(error);
        }
    },

    async pushlishUnBan(msg, delay) {
        try {
            strapi.mq.channels.unban.publish("delay-exchange", QUEUES.unban_account, Buffer.from(JSON.stringify(msg)), { headers: { "x-delay": delay } });
        } catch (error) {
            console.log(error);
        }
    },

    async sendmDistributeMessage(msg) {
        try {
            strapi.mq.channels.general.sendToQueue(QUEUES.mc_distribute, Buffer.from(JSON.stringify(msg)), { persistent: true });
        } catch (error) {
            console.log(error);
        }
    },
})