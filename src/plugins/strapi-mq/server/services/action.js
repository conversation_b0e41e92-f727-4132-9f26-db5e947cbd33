'use strict';
const { getPluginService } = require('../utils/getPluginService');
const { NOTIFICATION_TYPE } = require('../../../strapi-notification/server/constants');
const dayjs = require("dayjs");

module.exports = ({ strapi }) => ({

    async statUnConnectedMatches() {
        console.log('====================== start unconnected match statistics ======================')
        const result = await strapi.plugin('meedata').service('friendships').checkUnConnected();
        if (!result || result.length == 0) return;
        result.forEach(async item => {
            const msg = {
                type: NOTIFICATION_TYPE.MATCHED_REMINDER,
                user: { id: item }
            }
            await strapi.plugin('mq').service('publish').sendNotificationMessage(msg);
        })
        console.log('====================== end unconnected match statistics  ======================')
    },

    async statUnAccessUsers() {
        console.log('====================== start unaccess users ======================')
        const result = await strapi.plugin('meedata').service('users').statUnAccessUsers(3);
        if (!result || result.length == 0) return;
        result.forEach(async item => {
            const msg = {
                type: NOTIFICATION_TYPE.ACCESS_REMINDER,
                user: { id: item.id, username: item.username, receiveNotification: item.receiveNotification || true }
            }
            await strapi.plugin('mq').service('publish').sendNotificationMessage(msg);
        })
        console.log('====================== end end unaccess users  ======================')
    },

    async statUnViewGivenCards() {
        const result = await strapi.plugin('meedata').service('users').statUnViewGivenCards();
        if (!result || result.length == 0) return;
        result.forEach(async item => {
            const msg = {
                type: NOTIFICATION_TYPE.GIVEN_REMINDER,
                user: { id: item.id, username: item.username, receiveNotification: item.receiveNotification || true },
                count: item.limit
            }
            await strapi.plugin('mq').service('publish').sendNotificationMessage(msg);
        })
    },

    async statHighEvals() {
        console.log('====================== start highly rated statistics  ======================')
        const result = await strapi.plugin('meedata').service('ratings').statHighEvals();
        result.forEach(async item => {
            const msg = {
                type: NOTIFICATION_TYPE.HIGH_EVAL,
                user: { id: item.userId },
                count: item.count
            }
            await strapi.plugin('mq').service('publish').sendNotificationMessage(msg);
        })
        console.log('====================== end highly rated statistics  ======================')
    },

    async sendDailyGiven() {
        console.log('====================== start send daily given push  ======================')
        const lastAccessAt = dayjs().subtract(2, 'day').toDate();
        const users = await strapi.plugin('meedata').service('users').findRecentAccessUsers(lastAccessAt);
        while (users.length > 0) {
            const chunkUsers = users.splice(0, 500);
            if (chunkUsers.length > 0) {
                const userIds = chunkUsers.map(user => String(user.id));
                if (userIds.length > 0) {
                    await strapi.plugin('mq').service('publish').sendNotificationMessage({
                        type: NOTIFICATION_TYPE.DAILY_GIVEN,
                        userIds: userIds
                    });
                }
            }
        }
    },

    async statUnReadUsers() {
        console.log('====================== start unread users statistics  ======================')
        try {
            const lastAccessAt = dayjs().subtract(30, 'day').toDate();
            const users = await strapi.plugin('meedata').service('users').findRecentAccessUsers(lastAccessAt);
            if (!users || users.length == 0) return;

            while (users.length > 0) {
                const chunkUsers = users.splice(0, 100);
                if (chunkUsers.length > 0) {
                    const userIds = chunkUsers.map(user => String(user.id));
                    const unReadUserIds = await strapi.plugin('openim').service('user').checkUsersUnRead(userIds);
                    if (unReadUserIds.length > 0) {
                        await strapi.plugin('mq').service('publish').sendNotificationMessage({
                            type: NOTIFICATION_TYPE.CHAT_UNREAD_REMINDER,
                            userIds: unReadUserIds
                        });
                    }
                }
            }
        } catch (error) {
            console.log('====================== stat unread users fail ======================')
        }
        console.log('====================== end unread users statistics  ======================')
    },
});