'use strict';
const amqplib = require('amqplib');
const { QUEUES } = require("../constants");

module.exports = ({ strapi }) => ({
  async buildAll({ rabbitmq }) {
    await this.buildRabbitMQ(rabbitmq);
  },

  async buildRabbitMQ(config) {
    strapi.log.info("=========RabbitMQ connecting============");

    strapi.mq = {
      config,
      channels: {
      }
    }
    let opts = {
    }
    if (config.tls) {
      opts = {
        ...opts,
        ca: [config.tls.ca]
      }
    }
    const client = await amqplib.connect(`amqp://${config.user}:${config.pass}@${config.host}`, opts);

    //sent double heart pub
    const heartChannel = await client.createChannel();
    await heartChannel.assertQueue(QUEUES.delayedQueue, { durable: true });
    heartChannel.assertExchange("delay-exchange", "x-delayed-message", {
      autoDelete: false,
      durable: true,
      arguments: { "x-delayed-type": "direct" },
    });
    heartChannel.bindQueue(
      QUEUES.delayedQueue,
      "delay-exchange",
      QUEUES.delayedQueue
    );

    heartChannel.consume(QUEUES.delayedQueue, async (msg) => {
      try {
        await strapi.plugin('mq').service('consume').processDelayhMessage(msg);
      } catch (error) { }
      heartChannel.ack(msg);
    });
    strapi.mq.channels['heart'] = heartChannel;


    //regBonus
    const regBonusChannel = await client.createChannel();
    await regBonusChannel.assertQueue(QUEUES.registrationBonus, { durable: true });
    regBonusChannel.consume(QUEUES.registrationBonus, async (msg) => {
      try {
        await strapi.plugin('mq').service('consume').processRegBonusMessage(msg);
      } catch (error) { }
      regBonusChannel.ack(msg);
    });
    strapi.mq.channels['bonus'] = regBonusChannel;

    //push notification
    const pushNotiChannel = await client.createChannel();
    await pushNotiChannel.assertQueue(QUEUES.pushNotification, { durable: true });
    pushNotiChannel.consume(QUEUES.pushNotification, async (msg) => {
      try {
        await strapi.plugin('mq').service('consume').processPushNotificationMessage(msg);
      } catch (error) { }
      pushNotiChannel.ack(msg);
    });
    strapi.mq.channels['push'] = pushNotiChannel;

    //unban_account
    const unbanSubChannel = await client.createChannel();
    await unbanSubChannel.assertQueue(QUEUES.unban_account, { durable: true });
    unbanSubChannel.consume(QUEUES.unban_account, async (msg) => {
      try {
        await strapi.plugin('mq').service('consume').processUnbanMessage(msg);
      } catch (error) { }
      unbanSubChannel.ack(msg);
    });
    unbanSubChannel.assertExchange("delay-exchange", "x-delayed-message", { autoDelete: false, durable: true, passive: true, arguments: { 'x-delayed-type': "direct" } })
    unbanSubChannel.bindQueue(QUEUES.unban_account, "delay-exchange", QUEUES.unban_account);

    strapi.mq.channels['unban'] = unbanSubChannel;

    //push notification
    const generalChannel = await client.createChannel();

    await generalChannel.assertQueue(QUEUES.mc_distribute, { durable: true });
    generalChannel.consume(QUEUES.mc_distribute, async (msg) => {
      try {
        await strapi.plugin('mq').service('consume').processGenneralMessage(msg);
      } catch (error) { }
      generalChannel.ack(msg);
    });

    strapi.mq.channels['general'] = generalChannel;

    strapi.log.info("=========RabbitMQ connected============");
  }
});
