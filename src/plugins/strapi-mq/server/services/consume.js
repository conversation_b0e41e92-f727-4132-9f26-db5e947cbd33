'use strict';
const { PRICES, TRANSACTION, BLOCK_TYPE, HEART_LEVEL, REQUEST_STATUS } = require("../../../../constants");
const { NOTIFICATION_TYPE } = require('../../../strapi-notification/server/constants')
const {
    getReferralCodeOwner,
    addRegisterBonus
} = require("../../../../utils/redis");
const { MSG_TYPE } = require('../constants')

module.exports = ({ strapi }) => ({
    async processDelayhMessage(msg) {
        const { type, data } = JSON.parse(msg.content.toString());
        switch (type) {
            case MSG_TYPE.HEART_EXPIRED:
                await this.processHeartExpired(data);
                break;
            case MSG_TYPE.HEART_REMINDER:
                await this.processHeartReminder(data);
                break;
        }
    },

    async processRegBonusMessage(msg) {
        const { user, type, regRefCode, service } = JSON.parse(msg.content.toString());
        if (!user || !type)
            return;
        if (type === 'REGISTER') {
            //JOB模式下注册奖励500, 给审核人员的,后期需要关闭
            if (service && service == 'B') {
                await strapi.service("api::transaction.transaction").executeBonus(user.id, TRANSACTION.PRE_REG_BONUS, 500);
                await addRegisterBonus(user.id, 500);
            }
            // Check whether it is pre - registered
            const preEntry = await strapi.service("api::pre-membership.pre-membership").checkPreRegisteredUser(user.countryCode, user.phone);
            if (!preEntry.isPreRegistered) return;

            //Pre-registration bonus.
            const regBouns = preEntry.photos >= 3 ? PRICES.PRE_REG_BONUS_2 : PRICES.PRE_REG_BONUS_1;
            await strapi.service("api::transaction.transaction").executeBonus(user.id, TRANSACTION.PRE_REG_BONUS, regBouns);
            await addRegisterBonus(user.id, regBouns);
        } else if (type === 'REFERER') {
            if (!regRefCode) return;

            //check referralCode
            const referrerId = await getReferralCodeOwner(regRefCode);
            if (!referrerId) return;

            const referrer = await strapi.query('plugin::users-permissions.user')
                .findOne({ where: { id: referrerId } });
            if (!referrer) return;

            //give bonuse to recommender and recommended.
            await strapi.service("api::transaction.transaction").executeBonus(referrer.id, TRANSACTION.RECOMMENDER, PRICES.RECOMMENDER, `plugin::users-permissions.user:${user.id}`, user.id);
            await strapi.service("api::transaction.transaction").executeBonus(user.id, TRANSACTION.RECOMMENDED, PRICES.RECOMMENDED, `plugin::users-permissions.user:${referrer.id}`, referrer.id);

            await addRegisterBonus(user.id, PRICES.RECOMMENDED);
        }
    },

    async processPushNotificationMessage(msg) {
        await strapi.plugin('notification').service('notification').processNotificationMessage(msg);
    },

    async processHeartExpired(data) {
        const { sender, receiver, relatedId, level } = data;
        const isMatched = await strapi.plugin('meedata').service('friendships').isMatched(sender, receiver);
        if (isMatched) {
            return;
        }
        if (level === HEART_LEVEL.HIGHT) {
            await strapi.service("api::transaction.transaction").executeHeartCompensation(sender, `api::friendship-request.friendship-request:${relatedId}`, receiver);
        }

        const rejecter = await strapi.plugin('meedata').service('users').findOne(Number(receiver));
        if (!rejecter)
            return;

        if (relatedId) {
            await strapi.service("api::friendship-request.friendship-request").edit(relatedId, { handleResult: REQUEST_STATUS.EXPIRED });
        }

        await strapi.plugin('mq').service('publish').sendNotificationMessage({
            type: NOTIFICATION_TYPE.HEART_REJECT,
            user: { id: sender },
            rejecter: { id: rejecter.id, username: rejecter.username },
            ex: {
                en: "You heart has expired after 7 days of no response and has been automatically declined.",
                vi: "Trái tim của bạn đã hết hạn sau 7 ngày không có phản hồi và đã tự động bị từ chối."
            }
        });
    },

    async processHeartReminder(data) {
        const { sender, receiver } = data;
        const isMatched = await strapi.plugin('meedata').service('friendships').isMatched(sender, receiver);
        if (isMatched) return;
        // Send a push to the receiver.
        await strapi.plugin('mq').service('publish').sendNotificationMessage({
            type: NOTIFICATION_TYPE.GOT_HEART_REMINDER,
            user: { id: receiver }
        });
        //Plan the next reminder.
        await strapi.plugin('mq').service('publish').sendHeartReminderMessage(data);
    },

    async processUnbanMessage(msg) {
        try {
            strapi.log.info('================ processUnbanMessage ==================');
            const message = JSON.parse(msg.content.toString());
            const authUser = await strapi.entityService.findOne('plugin::users-permissions.user', message.id, {});
            if (authUser
                && authUser.blocked
                && authUser.blockedReason === BLOCK_TYPE.TEMP_BAN
                && authUser.tempBanDuration == message.tempBanDuration
                && authUser.banStartedAt == message.banStartedAt) {
                await strapi.entityService.update('plugin::users-permissions.user', message.id, {
                    data: {
                        blocked: false,
                        blockedReason: null,
                        banStartedAt: null,
                        tempBanDuration: 0
                    }
                });
            }
        } catch (error) {
            strapi.log.error('consuming unban message failed', error);
        }
    },

    async processGenneralMessage(msg) {
        try {
            const message = JSON.parse(msg.content.toString());
            const { type } = message;
            //mc distribute
            if (type === MSG_TYPE.MC_DISTRIBUTE) {
                const { id, amount, note } = message;
                await strapi.service('api::transaction.transaction').deposit(id, amount, note);
            }
        } catch (error) {
            strapi.log.error('consuming genneral message failed', error);
        }
    },
})