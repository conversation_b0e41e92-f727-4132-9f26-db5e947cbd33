// @ts-nocheck
'use strict';
const { AVATAR } = require("./constants");
const { generateReferralCode } = require("./utils");
const { saveUsedReferralCode } = require("./utils/redis");
const fs = require('fs');
const path = require('path');

module.exports = {
  /**
   * An asynchronous register function that runs before
   * your application is initialized.
   *
   * This gives you an opportunity to extend code.
   */
  register({ strapi }) {
  },

  /**
   * An asynchronous bootstrap function that runs before
   * your application gets started.
   *
   * This gives you an opportunity to set up your data model,
   * run jobs, or perform some special logic.
   */
  async bootstrap(/*{ strapi }*/) {
    //add indexs
    const knex = strapi.db.connection;
    const indexs = [
      {
        table: 'files_related_morphs',
        columm: 'field',
        index: 'files_related_morphs_idx_field'
      },
      {
        table: 'files_related_morphs',
        columm: 'related_type',
        index: 'files_related_morphs_idx_related_type'
      },
      {
        table: 'files_related_morphs',
        columm: 'order',
        index: 'files_related_morphs_idx_order'
      },
      {
        table: 'files_related_morphs',
        columm: 'related_id',
        index: 'files_relate_morphs_idx_related_id'
      }
    ]
    indexs.map(async index => {
      const query = `SELECT *
                     FROM information_schema.statistics
                     where
                        table_schema = '${knex.client.database()}'
                        and table_name = '${index.table}'
                        and index_name = '${index.index}'`;
      const existInddex = await knex.raw(query);
      if (existInddex[0].length === 0) {
        await knex.schema.alterTable(index.table, t => {
          t.index(index.columm, index.index)
          strapi.log.info(`============ add index = (table: ${index.table}, columm: ${index.columm}, index: ${index.index}) ============`)
        })
      }
    })

    const locales = ['en', 'vi'];
    strapi.messages = {};
    locales.forEach(locale => {
      const data = fs.readFileSync(path.join(__dirname, `./translations/${locale}.json`), 'utf8');
      strapi.messages[locale] = JSON.parse(data);
    })

    //plugin::users-permissions.user lifecycles
    strapi.db.lifecycles.subscribe({
      // @ts-ignore
      models: ['plugin::users-permissions.user'],
      async beforeCreate(event) {
        const { avatar, username } = event.params.data;
        const referralCode = await generateReferralCode();
        if (username) {
          const userWithSameName = await strapi
            .query('plugin::users-permissions.user')
            .findOne({ where: { username } });
          if (userWithSameName) {
            event.params.data.username = `${username}_${referralCode.toLowerCase()}`;
          }
        }
        event.params.data.balance = 0;
        event.params.data.avgRating = 0;
        event.params.data.referralCode = referralCode;
        if (!avatar) {
          event.params.data.avatar = AVATAR.DEFAULT;
        }
      },
      async afterCreate(event) {
        // @ts-ignore
        const { result } = event;
        if (result.referralCode) {
          await saveUsedReferralCode(result.referralCode, result.id);
        }
        //Synchronize user information into the IM system.
        // try {
        //   await strapi.plugin('openim').service('user').register({ userID: result.id, nickname: result.username, faceURL: result.avatar?.url });
        // } catch (error) {
        //   strapi.log.info('Failed to synchronize user information into the IM system.')
        // }
        try {
          strapi.plugin('meedata').service('users').syncUserInfo(result.id);
        } catch (error) {
          strapi.log.info('Failed to synchronize user information into the mongodb.')
        }
      },
      async afterUpdate(event) {
        // @ts-ignore
        const { result, params } = event;
        try {
          strapi.plugin('meedata').service('users').syncUserInfo(result.id);
        } catch (error) {
          strapi.log.info('Failed to update user information in the mongodb!!!')
        }
        //Update user information in the IM system.
        // try {
        //   if (params.data?.username && params.data?.avatar) {
        //     strapi.plugin('openim').service('user').update({ userID: result.id, nickname: result.username, faceURL: result.avatar?.url });
        //   }
        // } catch (error) {
        //   strapi.log.info('Failed to update user information in the IM system!!!', error)
        // }
      }
    })
    await strapi.service("api::setting.setting").importSettings();
  },
};
