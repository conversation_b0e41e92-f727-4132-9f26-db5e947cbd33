// @ts-nocheck
'use strict';

/**
 * Module dependencies
 */

/* eslint-disable no-unused-vars */
// Public node modules.
const _ = require('lodash');
const AWS = require('aws-sdk');
const dayjs = require("dayjs");
const { HttpsProxyAgent } = require("https-proxy-agent");

function assertUrlProtocol(url) {
  // Regex to test protocol like "http://", "https://"
  return /^\w*:\/\//.test(url);
}

module.exports = {
  init(config) {
    // 配置AWS SDK使用代理
    const awsConfig = {
      apiVersion: "2006-03-01",
      ...config,
    };

    // 如果设置了HTTP_PROXY环境变量或配置中有proxy属性，则使用代理
    if (process.env.HTTP_PROXY || process.env.HTTPS_PROXY || config.proxy) {
      const proxyUrl =
        config.proxy || process.env.HTTPS_PROXY || process.env.HTTP_PROXY;
      if (proxyUrl) {
        awsConfig.httpOptions = {
          agent: new HttpsProxyAgent(proxyUrl),
        };
        console.log(`AWS S3 using proxy: ${proxyUrl}`);
      }
    }

    const S3 = new AWS.S3(awsConfig);

    const upload = (file, customParams = {}) =>
      new Promise((resolve, reject) => {
        // upload file on S3 bucket
        const path = file.path
          ? `${file.path}/`
          : dayjs().format("YYYY/MM/DD/");
        const objectPath = `${path}${file.hash}${file.ext}`;
        S3.upload(
          {
            Key: objectPath,
            Body: file.stream || Buffer.from(file.buffer, "binary"),
            ContentType: file.mime,
            ...customParams,
          },
          (err, data) => {
            if (err) {
              return reject(err);
            }
            // set the bucket file url
            if (config.baseUrl) {
              file.url =
                (config.baseUrl.endsWith("/")
                  ? config.baseUrl
                  : config.baseUrl + "/") + objectPath;
            } else {
              if (assertUrlProtocol(data.Location)) {
                file.url = data.Location;
              } else {
                // Default protocol to https protocol
                file.url = `https://${data.Location}`;
              }
            }
            resolve();
          }
        );
      });

    return {
      uploadStream(file, customParams = {}) {
        return upload(file, customParams);
      },
      upload(file, customParams = {}) {
        return upload(file, customParams);
      },
      delete(file, customParams = {}) {
        return new Promise((resolve, reject) => {
          // delete file on S3 bucket
          var reg = /\d{4}(\-|\/|.)\d{1,2}\1\d{1,2}/;
          if (reg.test(file.url)) {
            file.path = file.url.match(reg)[0];
          }
          const path = file.path ? `${file.path}/` : "";
          S3.deleteObject(
            {
              Key: `${path}${file.hash}${file.ext}`,
              ...customParams,
            },
            (err, data) => {
              if (err) {
                return reject(err);
              }

              resolve();
            }
          );
        });
      },
    };
  },
};