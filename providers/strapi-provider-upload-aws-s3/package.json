{"name": "aws-s3", "version": "4.5.0", "description": "AWS S3 provider for strapi upload", "keywords": ["upload", "aws", "s3", "strapi"], "license": "SEE LICENSE IN LICENSE", "main": "./lib", "directories": {"lib": "./lib"}, "scripts": {"test": "echo \"no tests yet\""}, "dependencies": {"aws-sdk": "2.1215.0", "dayjs": "1.11.6", "https-proxy-agent": "^7.0.6", "lodash": "4.17.21"}, "engines": {"node": ">=14.19.1 <=18.x.x", "npm": ">=6.0.0"}}