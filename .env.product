HOST=0.0.0.0
PORT=5337
APP_KEYS=CM889X5Vs1Y6j6M4jpVWOw==,hAOYjmnQCVX+lp/sP4148g==,d85dxcisUgS7Kd3735/F+Q==,KlpnGUR/KKbK7qi+AJouCg==
API_TOKEN_SALT=5AUh787HwSMScRRzcfiLcw==
ADMIN_JWT_SECRET=H/0HaRv2KdabmBo2rkLIWA==
TRANSFER_TOKEN_SALT=1nAp53rVbEaayN8YKYc5Kg==
JWT_SECRET=t/CYuAvF3QZF2OmThQkFKw==
IS_PROD=true

#MYSQL
DATABASE_URL=
DATABASE_HOST=127.0.0.1
DATABASE_PORT=3308
DATABASE_NAME=meetok
DATABASE_USERNAME=dbuser
DATABASE_PASSWORD=CuqWhk93RiT5
DATABASE_CHARSET=utf8mb4

#REDIS
REDIS_HOST=127.0.0.1
REDIS_PORT=6380
REDIS_DB=0
REDIS_PASSWORD=youlike123
REDIS_SSL=false
REDIS_SSL_CA=

#OPENIM REDIS
OPENIM_REDIS_HOST=127.0.0.1
OPENIM_REDIS_PORT=16379
OPENIM_REDIS_DB=0
OPENIM_REDIS_PASSWORD=MeeTokIM7898

#AWS
AWS_ACCESS_KEY_ID=********************
AWS_ACCESS_SECRET=SdfE3kmQdiNCvhd4EJnrMpQH00cC5UYqtQL+B/fl
AWS_REGION=ap-northeast-2
AWS_BUCKET=an2-dev-atreez
# AWS_BUCKET_URL=an2-dev-atreez.s3.ap-northeast-2.amazonaws.com
CDN_BASE_URL=https://an2-dev-atreez.s3.ap-northeast-2.amazonaws.com
CDN_ROOT_PATH=starcheck

#GCP
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
GCS_BUCKET_NAME=starcheck
GCS_BASE_PATH=
GCS_BASE_URL=https://storage.googleapis.com/starcheck
GCS_PUBLIC_FILES=true
GCS_UNIFORM=true

INIT_ADMIN=true
INIT_ADMIN_USERNAME=admin
INIT_ADMIN_PASSWORD=Meetok123
INIT_ADMIN_FIRSTNAME=Admin
INIT_ADMIN_LASTNAME=Admin
INIT_ADMIN_EMAIL=<EMAIL>

#Sendgrid
SENDGRID_API_KEY=*********************************************************************
SENDGRID_FROM=<EMAIL>
SENDGRID_REPLY_TO=<EMAIL>

#MONGODB
MONGO_HOST=mongodb://127.0.0.1
MONGO_PORT=27018
MONGO_NAME=meetok
MONGO_USER=admin
MONGO_PASS=youlike123
MONGO_SSL=false
MONGO_SSL_CA=

#MQ
RABBITMQ_HOST=127.0.0.1
RABBITMQ_USER=mqadmin
RABBITMQ_PASS=Youlike123$
RABBITMQ_SSL=false
RABBITMQ_SSL_CA=

#OPEN IM
OPEN_IM_HOST=https://im.meetok.me/api
OPEN_IM_SECRET=MeeTokIM7898
OPEN_IM_ADMIN=mtkIMAdmin

#ONESIGNAL
ONESIGNAL_APPID=************************************
ONESIGNAL_APPKEY=os_v2_app_sybgnmnj6vgfxfuseg6qwfdfe4xzpo2whweumm5xhbkeq4si3kjnjmi5elg6nwxgudqrtsxkoo2spy67d6pvltbesf6sgeb3fd65sca
