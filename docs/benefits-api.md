# Benefits API 文档

本文档描述了 Benefits 模块的 API 接口，包括 BenefitsBoard（福利板块）和 BenefitsCoupon（福利优惠券）的增删改查操作。

## API 基础信息

- **基础路径**: `/strapi-manager/benefits`
- **认证**: 需要管理员权限
- **数据格式**: JSON

## BenefitsBoard API

### 1. 获取板块列表

**GET** `/strapi-manager/benefits/boards`

**查询参数**:
- `name` (string, optional): 板块名称模糊搜索
- `type` (string, optional): 板块类型 (`public` | `private`)
- `blocked` (boolean, optional): 是否被阻止
- `isDefault` (boolean, optional): 是否为默认板块
- `userLevelLimited` (string, optional): 用户等级限制
- `page` (number, optional): 页码，默认 1
- `pageSize` (number, optional): 每页数量，默认 20

**响应示例**:
```json
{
  "results": [
    {
      "id": 1,
      "name": "公共福利板块",
      "type": "public",
      "description": "面向所有用户的福利板块",
      "blocked": false,
      "isDefault": true,
      "order": 1,
      "creator": {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>"
      },
      "coupons": [
        {
          "id": 1,
          "title": "新用户优惠券"
        }
      ]
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "pageCount": 1,
    "total": 1
  }
}
```

### 2. 获取单个板块

**GET** `/strapi-manager/benefits/boards/:id`

**响应示例**:
```json
{
  "id": 1,
  "name": "公共福利板块",
  "type": "public",
  "description": "面向所有用户的福利板块",
  "blocked": false,
  "isDefault": true,
  "order": 1,
  "creator": {
    "id": 1,
    "username": "admin",
    "email": "<EMAIL>",
    "fullname": "管理员"
  },
  "subscribers": [],
  "coupons": [
    {
      "id": 1,
      "title": "新用户优惠券",
      "content": "新用户专享优惠券",
      "pinned": false,
      "blocked": false,
      "removed": false,
      "likeCount": 0,
      "createdAt": "2023-12-01T00:00:00.000Z",
      "media": []
    }
  ]
}
```

### 3. 创建板块

**POST** `/strapi-manager/benefits/boards`

**请求体**:
```json
{
  "name": "新福利板块",
  "type": "public",
  "description": "板块描述",
  "blocked": false,
  "isDefault": false,
  "order": 2,
  "postingPermission": 1,
  "userLevelLimited": "1"
}
```

### 4. 更新板块

**PUT** `/strapi-manager/benefits/boards/:id`

**请求体**: 同创建板块，所有字段都是可选的

### 5. 删除板块

**DELETE** `/strapi-manager/benefits/boards/:id`

**响应示例**:
```json
{
  "id": 1,
  "message": "Benefits board deleted successfully"
}
```

## BenefitsCoupon API

### 1. 获取优惠券列表

**GET** `/strapi-manager/benefits/coupons`

**查询参数**:
- `title` (string, optional): 标题模糊搜索
- `boardId` (number, optional): 板块ID筛选
- `pinned` (boolean, optional): 是否置顶
- `blocked` (boolean, optional): 是否被阻止
- `removed` (boolean, optional): 是否被删除
- `level` (number, optional): 等级筛选
- `phone` (string, optional): 手机号筛选
- `startDate` (string, optional): 开始日期 (YYYY-MM-DD)
- `endDate` (string, optional): 结束日期 (YYYY-MM-DD)
- `page` (number, optional): 页码，默认 1
- `pageSize` (number, optional): 每页数量，默认 20

**响应示例**:
```json
{
  "results": [
    {
      "id": 1,
      "title": "新用户优惠券",
      "content": "新用户专享优惠券内容",
      "pinned": false,
      "blocked": false,
      "removed": false,
      "likeCount": 5,
      "level": 1,
      "phone": "1234567890",
      "expiresAt": "2024-12-31T16:00:00.000Z",
      "board": {
        "id": 1,
        "name": "公共福利板块",
        "type": "public"
      },
      "media": [],
      "likers": []
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "pageCount": 1,
    "total": 1
  }
}
```

### 2. 获取单个优惠券

**GET** `/strapi-manager/benefits/coupons/:id`

**响应示例**:
```json
{
  "id": 1,
  "title": "新用户优惠券",
  "content": "新用户专享优惠券内容",
  "description": "详细描述",
  "pinned": false,
  "blocked": false,
  "removed": false,
  "likeCount": 5,
  "level": 1,
  "phone": "1234567890",
  "expiresAt": "2024-12-31T16:00:00.000Z",
  "board": {
    "id": 1,
    "name": "公共福利板块",
    "type": "public",
    "description": "板块描述"
  },
  "media": [],
  "likers": []
}
```

### 3. 创建优惠券

**POST** `/strapi-manager/benefits/coupons`

**请求体**:
```json
{
  "title": "新优惠券",
  "content": "优惠券内容",
  "description": "优惠券描述",
  "board": 1,
  "level": 1,
  "phone": "1234567890",
  "expiresAt": "2024-12-31T16:00:00.000Z",
  "pinned": false,
  "blocked": false
}
```

### 4. 更新优惠券

**PUT** `/strapi-manager/benefits/coupons/:id`

**请求体**: 同创建优惠券，所有字段都是可选的

### 5. 删除优惠券

**DELETE** `/strapi-manager/benefits/coupons/:id`

**响应示例**:
```json
{
  "id": 1,
  "message": "Benefits coupon deleted successfully"
}
```

## 错误响应

所有 API 在出错时会返回相应的 HTTP 状态码和错误信息：

```json
{
  "error": {
    "status": 400,
    "name": "ValidationError",
    "message": "错误描述"
  }
}
```

常见状态码：
- `400`: 请求参数错误
- `404`: 资源不存在
- `500`: 服务器内部错误

## 注意事项

1. 所有日期时间字段使用 ISO 8601 格式
2. 删除板块前需要确保没有关联的优惠券
3. 设置板块为默认时，会自动取消其他板块的默认状态
4. 优惠券的 `expiresAt` 字段默认为 "2039-12-31T16:00:00.000Z"
5. 所有 API 都需要管理员权限认证
